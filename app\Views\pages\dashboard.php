<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <!-- <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Dashboard Overview</h1>
            <p class="text-gray-500 dark:text-gray-400">Welcome back, <?= $userDetails['full_name'] ?>! Here's your
                trading performance
                summary.</p> -->
        </div>
        <div class="flex space-x-3">
            <div class="relative">
                <select id="marketTypeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">Indian</option>
                    <option value="2">Forex</option>
                    <option value="3">Crypto</option>
                </select>
            </div>
            
            <div class="relative">
                <select id="rangeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">Last 30 Days</option>
                    <option value="3">Last 90 Days</option>
                    <option value="12">Year to Date</option>
                    <!-- <option value="">All Time</option> -->
                </select>
            </div>

            <button
                class="hidden px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all">
                <i class="fas fa-download mr-2"></i> Export
            </button>
            <button id="addTradeBtn"
                class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-md text-sm font-medium hover:bg-primary-dark dark:hover:bg-primary-light transition-all shadow hover:shadow-lg">
                <i class="fas fa-plus"></i><span class="ml-2 hidden sm:inline">New Trade</span>
            </button>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="stats-card bg-white dark:bg-gray-800 rounded-xl p-6 card-3d">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Higest P&L</p>
                    <h3 id="thisMonthHighestPnl" class="text-2xl font-bold mt-1 text-green-600 dark:text-green-400">0.00
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2"><span id="monthChangePnl">0.00</span> <span
                            class="vsClass"></span></p>
                </div>
                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full shadow-inner">
                    <i class="fas fa-wallet text-green-600 dark:text-green-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div id="monthPnlProgress" class="h-1 bg-green-500 rounded-full" style="width: 78%"></div>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white dark:bg-gray-800 rounded-xl p-6 card-3d">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Win Rate</p>
                    <h3 id="thisMonthWinRate" class="text-2xl font-bold mt-1 text-primary-light dark:text-primary-dark">
                        0.00%</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2"><span id="monthChangeWinRate">0.00</span>
                        <span class="vsClass"></span>
                    </p>
                </div>
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full shadow-inner">
                    <i class="fas fa-trophy text-primary-light dark:text-primary-dark text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div id="monthWinRateProgress" class="h-1 bg-blue-500 rounded-full" style="width: 72%"></div>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white dark:bg-gray-800 rounded-xl p-6 card-3d">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg. Risk/Reward</p>
                    <h3 class="text-2xl font-bold mt-1 text-purple-600 dark:text-purple-400">1:<span
                            id="thisMonthRiskReward">0</span></h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2"><span id="monthChangeRR">0.00</span> <span
                            class="vsClass"></span></p>
                </div>
                <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full shadow-inner">
                    <i class="fas fa-balance-scale text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div id="monthRRProgress" class="h-1 bg-purple-500 rounded-full" style="width: 85%"></div>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white dark:bg-gray-800 rounded-xl p-6 card-3d">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Trades This Month</p>
                    <h3 id="tradesThisMonth" class="text-2xl font-bold mt-1 text-orange-600 dark:text-orange-400">0</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2"><span id="monthChangeTrades">0</span> <span
                            class="vsClass"></span></p>
                </div>
                <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-full shadow-inner">
                    <i class="fas fa-chart-bar text-orange-600 dark:text-orange-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div id="monthTradeProgress" class="h-1 bg-orange-500 rounded-full" style="width: 63%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Market sentiment -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold">Confidence Index</h2>
            <div id="confIndText" class="text-sm text-gray-500 dark:text-gray-400">...</div>
        </div>
        <div class="flex items-center mb-2">
            <div class="w-16 text-sm font-medium text-red-500 dark:text-red-400">Low</div>
            <div class="flex-1 mx-2 relative">
                <div class="sentiment-bar"></div>
                <div id="confidence_progress" class="sentiment-indicator bg-gray-800 dark:bg-gray-200 shadow-md"
                    style="left: 65%">
                </div>
            </div>
            <div class="w-16 text-sm font-medium text-green-500 dark:text-green-400 text-right">High
            </div>
        </div>
        <div class="text-center text-sm text-gray-500 dark:text-gray-400"><span id="confidence_message">...</span></div>
    </div>

    <!-- Charts and Recent Trades -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- P&L Chart -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-lg font-semibold text-gray-600 dark:text-white">Cumulative P&L</h4>
                <div class="flex space-x-2">
                    <button
                        class="pnlChartFilter px-3 py-1 rounded-lg text-sm text-white bg-blue-500 dark:bg-blue-600">D</button>
                    <button
                        class="pnlChartFilter px-3 py-1 rounded-lg text-sm text-gray-800 dark:text-gray-200 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600">W</button>
                    <button
                        class="pnlChartFilter px-3 py-1 rounded-lg text-sm text-gray-800 dark:text-gray-200 bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600">M</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="equityChart"></canvas>
            </div>
        </div>

        <!-- Recent Trades -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold">Top Trades</h2>
                <a href="<?= base_url('myTrades') ?>"
                    class="text-primary-light dark:text-primary-dark text-sm font-medium">View All</a>
            </div>
            <div id="topTradesContainer" class="space-y-4">
                <div
                    class="trade-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-all duration-200 cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">XXXXX</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Long • 0h 00m</p>
                        </div>
                        <div class="text-green-600 dark:text-green-400 font-medium">+0.00 (0.00%)</div>
                    </div>
                    <div class="flex justify-between mt-0 text-sm text-gray-600 dark:text-gray-300">
                        <span>Entry: ₹0.00</span>
                        <span>Exit: ₹0.00</span>
                    </div>
                </div>
                <div
                    class="trade-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-all duration-200 cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">XXXXX</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Long • 0h 00m</p>
                        </div>
                        <div class="text-green-600 dark:text-green-400 font-medium">+0.00 (0.00%)</div>
                    </div>
                    <div class="flex justify-between mt-0 text-sm text-gray-600 dark:text-gray-300">
                        <span>Entry: ₹0.00</span>
                        <span>Exit: ₹0.00</span>
                    </div>
                </div>
                <div
                    class="trade-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-all duration-200 cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">XXXXX</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Long • 0h 00m</p>
                        </div>
                        <div class="text-green-600 dark:text-green-400 font-medium">+0.00 (0.00%)</div>
                    </div>
                    <div class="flex justify-between mt-0 text-sm text-gray-600 dark:text-gray-300">
                        <span>Entry: ₹0.00</span>
                        <span>Exit: ₹0.00</span>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Win/Loss Distribution -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold">Win/Loss Distribution</h2>
                <div class="flex items-center text-sm">
                    <div class="flex items-center mr-3">
                        <div class="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                        <span>Wins</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                        <span>Losses</span>
                    </div>
                </div>
            </div>
            <div id="winLossChartContainer" class="chart-container">
                <canvas id="winLossChart"></canvas>
            </div>
        </div>

        <!-- Strategy Performance -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Strategy vs P&L</h2>
            <div class="chart-container">
                <canvas id="strategyPnlChart"></canvas>
            </div>
        </div>
    </div>


    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Mistakes Chart (50%) -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="modern-chart rounded-xl h-full">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">Most Common Mistakes</h2>
                    <div id="comMisText" class="text-sm text-gray-500 dark:text-gray-400">Last 1 Month</div>
                </div>
                <div class="mt-6" id="mistakeStatsContainer">
                    <!-- Mistake rows will be injected here -->
                </div>
            </div>
        </div>

        <!-- Daily P&L Chart (50%) -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-lg font-semibold text-gray-600 dark:text-white">Daily P&L</h4>
            </div>
            <div class="chart-container">
                <canvas id="dailyPnlChart"></canvas>
            </div>
        </div>
    </div>



    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
        <div
            class="flex flex-col md:flex-row justify-between items-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
            <div>
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Trade History</h2>
                <!-- <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Last 30 days activity</p> -->
            </div>
            <div class="flex space-x-3 mt-4 md:mt-0">
                <div class="dropdown">
                    <div class="relative">
                        <select id="sortSelect"
                            class="appearance-none px-3 py-2 pr-10 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 w-full">
                            <option value="">Default</option>
                            <option value="pnl-desc">Highest PnL</option>
                            <option value="pnl-asc">Lowest PnL</option>
                            <option value="rr-desc">Highest R:R</option>
                            <option value="rr-asc">Lowest R:R</option>
                            <option value="date-desc">Newest First</option>
                            <option value="date-asc">Oldest First</option>
                        </select>
                        <i
                            class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none"></i>
                    </div>
                </div>
                <button id="openModal"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    <span class="hidden sm:inline">Filter Trades</span>
                </button>
                <button id="addTradeBtn2"
                    class="px-3 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                    <i class="fas fa-plus mr-1"></i>
                    <span class="hidden sm:inline">New Trade</span>
                </button>
            </div>
        </div>

        <div class="trade-table-container">
            <table class="w-full trade-table">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Date</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Symbol</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Direction</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Entry/Exit</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            P/L (<span class="currencySymbol">₹</span> / %)</th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Risk/Reward</th>
                        <th
                            class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Strategy</th>
                        <th
                            class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Outcome</th>
                        <th
                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">

                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div
            class="px-6 py-4 border-t dark:border-gray-700 flex flex-col md:flex-row items-center justify-between gap-4">
            <div class="text-sm text-gray-500 dark:text-gray-400 pagination-summary">
                <!-- Filled dynamically -->
            </div>
            <div class="pagination-container flex space-x-1"></div>
        </div>
    </div>

    <div id="modalBackdrop"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden modal-container">
        <!-- Modal Container -->
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto fade-in modal-content">
            <!-- Modal Header -->
            <div
                class="sticky top-0 bg-white dark:bg-gray-800 border-b dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">Filter Trades</h3>
                <button id="closeModal"
                    class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body - Filters -->
            <div class="p-6 space-y-6">

                <!-- market type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Market type</label>
                    <div class="grid grid-cols-3 gap-2">
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="1" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Indian</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="2" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Forex</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="3" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Crypto</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="mkt_type" value="0" class="modern-radio" checked>
                            <span class="ml-2 text-gray-700 dark:text-gray-300">All</span>
                        </label>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start
                            Date</label>
                        <div class="relative">
                            <input type="date" id="startDate"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white modern-input">
                            <i class="fas fa-calendar absolute right-3 top-3 text-gray-400 pointer-events-none"></i>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
                        <div class="relative">
                            <input type="date" id="endDate"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white modern-input">
                            <i class="fas fa-calendar absolute right-3 top-3 text-gray-400 pointer-events-none"></i>
                        </div>
                    </div>
                </div>

                <!-- Strategy Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Trading
                        Strategy</label>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                        <?php foreach ($strategies as $s => $strategy) { ?>
                            <label
                                class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                                <input type="checkbox" value="<?= $strategy['id'] ?>" class="modern-checkbox">
                                <span class="ml-2 text-gray-700 dark:text-gray-300"><?= $strategy['strategy'] ?></span>
                            </label>
                        <?php } ?>
                    </div>
                </div>

                <!-- Direction -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Trade
                        Direction</label>
                    <div class="grid grid-cols-3 gap-2">
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="1" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Long</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="2" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Short</span>
                        </label>
                        <label
                            class="filter-card flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer">
                            <input type="radio" name="direction" value="0" class="modern-radio">
                            <span class="ml-2 text-gray-700 dark:text-gray-300">Both</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end space-x-3 border-t dark:border-gray-600">
                <button id="resetFilters"
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition modern-btn modern-btn-secondary">
                    Reset Filters
                </button>
                <button id="applyFilters" onclick="applyFilters()"
                    class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition modern-btn modern-btn-primary">
                    Apply Filters
                </button>
            </div>
        </div>
    </div>


    <div id="addTradeModal"
        class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 hidden backdrop-blur-sm">
        <div
            class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
            <!-- Fixed Header -->
            <div
                class="px-6 py-3 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center sticky top-0 bg-white dark:bg-gray-800 z-10">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">Add New Trade</h2>
                </div>
                <button id="closeTradeModal"
                    class="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Scrollable Body -->
            <div class="overflow-y-auto flex-grow">
                <div class="px-6 py-3">
                    <!-- Tab Navigation -->
                    <div class="flex border-b border-gray-200 dark:border-gray-700 mb-6">
                        <button class="nav-tab active px-4 py-2 font-medium text-gray-800 dark:text-gray-200 mr-1"
                            id="trade-tab" data-tab="basic-tab">
                            <i class="fas fa-info-circle mr-2"></i> General
                        </button>
                        <button
                            class="nav-tab px-4 py-2 font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mr-1"
                            data-tab="psychology-tab">
                            <i class="fas fa-brain mr-2"></i> Psychology
                        </button>
                    </div>

                    <!-- Trade Form -->
                    <form id="tradeForm" class="mb-6" enctype="multipart/form-data" method="post" action="#">
                        <!-- Basic Information Tab -->
                        <input type="hidden" id="editId" name="editId" value="">
                        <div id="basic-tab" class="tab-content active">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                <!-- Type -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Market type<sup
                                            class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Market type" id="market_type" name="market_type">
                                        <option value="1">Indian</option>
                                        <!-- <option value="2">Forex</option> -->
                                        <option value="3">Crypto</option>
                                    </select>
                                </div>

                                <!-- Symbol -->
                                <div class="sm:col-span-2 lg:col-span-1">
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Symbol<sup
                                            class="text-red-400"> *</sup></label>
                                    <div class="relative">
                                        <input type="text"
                                            class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                            placeholder="RELIANCE, NIFTY 50, etc." id="symbol" name="symbol"
                                            data-inputname="Symbol">
                                        <div
                                            class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <!-- <i class="fas fa-search text-gray-400"></i> -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Date & Time -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Date<sup
                                            class="text-red-400"> *</sup></label>
                                    <input type="date" class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Date" id="datetime" name="datetime">
                                </div>

                                <!-- Entry Price -->
                                <div>
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Entry
                                        Price
                                        <sup class="text-red-400"> *</sup></label>
                                    <input type="number" step="0.01"
                                        class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Entry Price" id="entry_price" placeholder="Entry Price"
                                        name="entry_price">
                                </div>

                                <!-- Position Size -->
                                <div>

                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Quantity<sup
                                                    class="text-red-400"> *</sup></label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="Quantity" id="entry_quantity" name="entry_quantity"
                                                placeholder="Quantity">
                                        </div>
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Total
                                                amount</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="Total
                                        amount" id="entry_amount" name="entry_amount" placeholder=" Amount" readonly>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exit Price -->
                                <div>
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Exit
                                        Price
                                        <sup class="text-red-400"> *</sup></label>
                                    <input type="number" step="0.01" id="exit_price" name="exit_price"
                                        class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Exit Price" placeholder="Exit Price">
                                </div>

                                <!-- Profit / Loss -->
                                <div>

                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">P&L
                                                Amount</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="P&L Amount" id="pnlAmount" name="pnlAmount"
                                                placeholder=" Amount" readonly>
                                        </div>
                                        <div>
                                            <label
                                                class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">P&L
                                                (%)</label>
                                            <input type="number"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                                data-inputname="P&L (%)" id="pnlPercent" name="pnlPercent"
                                                placeholder="% Change" readonly>
                                        </div>
                                    </div>
                                </div>

                                <!-- Direction -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Direction<sup
                                            class="text-red-400"> *</sup></label>
                                    <div class="flex space-x-2">
                                        <button type="button" id="btn-long"
                                            class="flex-1 py-2 px-3 rounded-lg border border-green-500 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-300 font-medium flex items-center justify-center">
                                            <i class="fas fa-arrow-up mr-2"></i> Long
                                        </button>
                                        <button type="button" id="btn-short"
                                            class="flex-1 py-2 px-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium flex items-center justify-center">
                                            <i class="fas fa-arrow-down mr-2"></i> Short
                                        </button>
                                    </div>
                                    <input type="hidden" id="trade_type" name="trade_type" class="tReq"
                                        data-inputname="Trade Direction" value="1">
                                </div>

                                <!-- Stop Loss & Target -->
                                <div>
                                    <div class="col-span-12 md:col-span-4">
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                            <div>
                                                <label
                                                    class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Stop
                                                    Loss </label>
                                                <input type="number" step="0.01" id="stop_loss" name="stop_loss"
                                                    class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                    placeholder="Stop Loss">
                                            </div>
                                            <div>
                                                <label
                                                    class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Target
                                                    </label>
                                                <input type="number" step="0.01" id="target" name="target"
                                                    class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                    placeholder="Target">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Strategy -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Strategy<sup
                                            class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Strategy" id="strategy" name="strategy">
                                        <option value="">Select Strategy</option>
                                        <?php foreach ($strategies as $key => $str) { ?>
                                            <option value="<?= $str['id'] ?>"><?= $str['strategy'] ?></option>
                                        <?php } ?>
                                    </select>
                                </div>

                                <!-- Outcome Summary -->
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Outcome
                                        Summary<sup class="text-red-400"> *</sup></label>
                                    <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                        data-inputname="Outcome summary" id="outcome" name="outcome">
                                        <option value="">Select Outcome Summary</option>
                                        <?php foreach ($summaries as $key => $smr) { ?>
                                            <option value="<?= $smr['id'] ?>"><?= $smr['summary'] ?></option>
                                        <?php } ?>
                                    </select>
                                </div>

                                <!-- Trade Rationale & Rules Followed -->
                                <div class="sm:col-span-2 lg:col-span-3">
                                    <label class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Trade
                                        Analysis</label>
                                    <textarea rows="4" class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                        data-inputname="Trade Analysis" id="rationale" name="rationale"
                                        placeholder="Why did you take this trade? What was your analysis?"></textarea>

                                    <label class="block text-sm font-medium mb-1 mt-4 text-gray-700 dark:text-gray-300">
                                        Rules Followed
                                    </label>
                                    <div
                                        class="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-gray-50 dark:bg-gray-700/30">
                                        <div id="selectedRules" class="flex flex-wrap mb-3"></div>
                                        <div id="selectedRuleInputs"></div>

                                        <div class="relative">
                                            <input type="text" id="ruleSearch"
                                                class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                                placeholder="Search or add rules...">

                                            <div id="ruleDropdown"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                                <?php foreach ($rules as $key => $rl) { ?>
                                                    <div class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                                                        data-rule="<?= $rl['id'] ?>">
                                                        <?= htmlspecialchars($rl['rule']) ?>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>

                                    <script>
                                        // Build JS rules object from PHP
                                        const rules = {
                                            <?= implode(",\n", array_map(function ($rl) {
                                                return "'" . $rl['id'] . "': " . json_encode($rl['rule']);
                                            }, $rules)) ?>
                                        };
                                    </script>

                                </div>

                                <!-- File Upload -->
                                <div class="sm:col-span-2 lg:col-span-3">
                                    <label class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Trade
                                        Screenshots</label>
                                    <div
                                        class="file-upload rounded-lg p-8 text-center cursor-pointer transition-all duration-200">
                                        <div
                                            class="w-16 h-16 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <i
                                                class="fas fa-cloud-upload-alt text-2xl text-blue-500 dark:text-blue-400"></i>
                                        </div>
                                        <p class="text-gray-500 dark:text-gray-400 mb-1 font-medium">Drag & drop your
                                            trade
                                            screenshots here</p>
                                        <p class="text-xs text-gray-400">Supports JPG, PNG (Max 5MB each)</p>
                                        <input type="file" class="hidden" name="screenshots[]" multiple
                                            accept="image/*">
                                    </div>
                                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4"
                                        id="previewContainer"></div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-span-6 md:col-span-3 bg-red-200 p-4">Col 3</div> -->

                        <!-- Psychological Review Tab -->
                        <div id="psychology-tab" class="tab-content">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Column 1 -->
                                <div class="space-y-6">
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Entry
                                            Confidence Level (1-10)</label>
                                        <div class="flex items-center space-x-4">
                                            <input type="range" min="1" max="10" value="5" class="w-full slider-thumb"
                                                id="confidence" name="confidence">
                                            <span
                                                class="text-lg font-medium w-8 text-center text-gray-700 dark:text-gray-300"
                                                id="confidenceValue">5</span>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <span>Low</span>
                                            <span>Medium</span>
                                            <span>High</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Satisfaction
                                            Rating (1-10)</label>
                                        <div class="flex items-center space-x-4">
                                            <input type="range" min="1" max="10" value="5" class="w-full slider-thumb"
                                                id="satisfaction" name="satisfaction">
                                            <span
                                                class="text-lg font-medium w-8 text-center text-gray-700 dark:text-gray-300"
                                                id="executionValue">5</span>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <span>Not Satisfied</span>
                                            <span>Average</span>
                                            <span>Satisfied</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Emotional
                                            State During Trade<sup class="text-red-400"> *</sup></label>
                                        <select class="w-full px-4 py-2 form-input rounded-lg focus:ring-0 tReq"
                                            data-inputname="Emotional State" id="emotion" name="emotion">
                                            <option value="">Select Emotional State</option>
                                            <?php foreach ($emotions as $key => $em) { ?>
                                                <option value="<?= $em['id'] ?>"><?= $em['emotion'] ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Column 2 -->
                                <div class="space-y-6">
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Mistakes
                                            Made</label>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                            <?php foreach ($mistakes as $key => $mst) { ?>
                                                <div class="checkbox-item flex items-center">
                                                    <label for="mistake<?= $mst['id'] ?>" class="checkbox-item flex items-center cursor-pointer">
                                                        <input type="checkbox" id="mistake<?= $mst['id'] ?>" name="mistakes[]"
                                                            value="<?= $mst['id'] ?>" class="hidden peer">

                                                        <span class="checkmark w-4 h-4 border border-gray-400 mr-2 rounded-sm peer-checked:bg-red-500"></span>

                                                        <span class="text-sm"><?= $mst['mistake'] ?></span>
                                                    </label>
                                                </div>
                                            <?php } ?>
                                        </div>

                                    </div>
                                    <div>
                                        <label
                                            class="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Lessons
                                            Learned</label>
                                        <textarea rows="3"
                                            class="w-full px-4 py-2 form-input rounded-lg focus:ring-0"
                                            data-inputname="Lessons Learned" id="lesson" name="lesson"
                                            placeholder="What did you learn from this trade?"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Fixed Footer -->
            <div
                class="px-6 py-3 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-3 sticky bottom-0 bg-white dark:bg-gray-800 z-10">
                <button type="button" id="resetFormBtn"
                    class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 font-medium">
                    <i class="fas fa-redo mr-2"></i> Reset
                </button>
                <button type="button" id="saveTradeBtn" onclick="saveTrade('<?= base_url('saveTrade') ?>')"
                    class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white px-6 py-2 rounded-lg flex items-center font-medium shadow-sm hover:shadow-md transition-all duration-200 glow-effect">
                    <i class="fas fa-save mr-2"></i> Save Trade
                </button>
            </div>
        </div>
    </div>



    <div id="successModal" class="modal">
        <div class="modal-content p-8 success-modal">
            <div class="mb-6">
                <div
                    class="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check-circle text-4xl text-green-500 dark:text-green-400"></i>
                </div>
                <h3 class="text-2xl font-bold text-center mb-2 text-gray-800 dark:text-gray-200">Trade Saved
                    Successfully!</h3>
                <p id="response_message" class="text-gray-600 dark:text-gray-300 text-center">...</p>
            </div>

            <div class="trade-comparison">
                <h4 class="font-medium text-lg mb-2 text-gray-800 dark:text-gray-200">Comparison with
                    Previous Day's P&L</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Today's P&L</div>
                        <div class="font-medium" id="todays_pnl">0.00</div>
                        <!-- <div class="text-xs text-gray-500 dark:text-gray-400">Breakout Strategy</div> -->
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Yesterday's P&L</div>
                        <div class="font-medium" id="yesterdays_pnl">0.00</div>
                        <!-- <div class="text-xs text-gray-500 dark:text-gray-400">Reversal Strategy</div> -->
                    </div>
                </div>
                <div class="mt-3 text-sm text-gray-600 dark:text-gray-300">
                    <i id="message_icon" class=""></i>
                    <span id="comparison_message">...</span>
                </div>
            </div>

            <div class="mt-6 flex justify-center">
                <button id="closeSuccessModal"
                    class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white px-6 py-2 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200">
                    Keep Journaling
                </button>
            </div>
        </div>
    </div>

    <div id="viewTradeDetailsModal"
        class="fixed inset-0 z-9999 flex items-center justify-center p-4 modal-bg backdrop-blur-sm hidden modal">
        <!-- Modal Container -->
        <div
            class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden animate-slide-up transition-all duration-300">
            <!-- Modal Header -->
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100">Trade Summary</h3>
                <div class="flex items-center space-x-4">
                    <button id="closeModalDet"
                        class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <div class="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-10">
                <div class="flex overflow-x-auto">
                    <button data-tab="general"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-info-circle"></i>
                        <span>General</span>
                    </button>
                    <button data-tab="analytics"
                        class="hidden tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics</span>
                    </button>
                    <button data-tab="journal"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-book"></i>
                        <span>Journal</span>
                    </button>
                    <button data-tab="media"
                        class="tab-btn-det tab-btn flex-1 py-3 px-4 text-center font-medium text-gray-500 dark:text-gray-400 hover:text-primary-light dark:hover:text-primary-dark border-b-2 border-transparent hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-images"></i>
                        <span>Media</span>
                    </button>
                </div>
            </div>

            <!-- Tab Contents -->
            <div class="overflow-y-auto p-6" style="max-height: calc(90vh - 120px)">
                <!-- General Tab -->
                <div id="general" class="tab-content tab-content-det active">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Trade Overview -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-tag"></i>
                                <span>Trade Overview</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Symbol</span>
                                    <span id="viewSymbol"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Direction</span>
                                    <span id="viewDirection" class="font-medium">...</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Entry Price</span>
                                    <span id="viewEntryPrice" class="font-medium text-gray-800 dark:text-gray-100">₹
                                        0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Exit Price</span>
                                    <span id="viewExitPrice" class="font-medium text-gray-800 dark:text-gray-100">₹
                                        0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Trade Session</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-100">2 days 4 hours</span>
                                </div>
                            </div>
                        </div>

                        <!-- Risk Management -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-shield-alt"></i>
                                <span>Risk Management</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Position Size</span>
                                    <span id="viewPositionSize" class="font-medium text-gray-800 dark:text-gray-100">0
                                        Qty</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Stop Loss</span>
                                    <span id="viewStopLoss" class="font-medium text-red-500">₹ 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Target</span>
                                    <span id="viewTarget" class="font-medium text-green-500">₹ 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Expected Risk/Reward</span>
                                    <span id="viewRrRatio"
                                        class="font-medium text-gray-800 dark:text-gray-100">1:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Risk/Reward %</span>
                                    <span id="viewRiskPercent"
                                        class="font-medium text-gray-800 dark:text-gray-100">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Performance -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-trophy"></i>
                                <span>Performance</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">P&L</span>
                                    <span id="viewPnl" class="font-medium text-green-500">₹ 0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">P&L %</span>
                                    <span id="viewPnlPercent" class="font-medium text-green-500">0.00%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Final Risk/Reward</span>
                                    <span id="viewActualRR"
                                        class="font-medium text-gray-800 dark:text-gray-100">0:0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Trade Outcome</span>
                                    <span id="viewOutcome"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Evaluation -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-star"></i>
                                <span>Trade Evaluation</span>
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600 dark:text-gray-300">Entry Confidence</span>
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                            <div id="viewConfidence" class="bg-yellow-500 h-2.5 rounded-full"
                                                style="width: 75%"></div>
                                        </div>
                                        <span id="viewConfidenceTExt"
                                            class="ml-2 text-sm font-medium text-gray-800 dark:text-gray-100">75%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600 dark:text-gray-300">Satisfaction</span>
                                    <div class="flex items-center">
                                        <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                            <div id="viewSatisfaction" class="bg-green-500 h-2.5 rounded-full"
                                                style=" width: 85%"></div>
                                        </div>
                                        <span id="viewSatisfactionText"
                                            class="ml-2 text-sm font-medium text-gray-800 dark:text-gray-100">85%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Emotional State</span>
                                    <span id="viewEmotion"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                                <div class="flex justify-between items-center hidden">
                                    <span class="text-gray-600 dark:text-gray-300">Satisfaction</span>
                                    <div class="flex">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-300">Strategy</span>
                                    <span id="viewStrategy"
                                        class="font-medium text-gray-800 dark:text-gray-100">...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Market Conditions -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-chart-bar"></i>
                                <span>Market Conditions</span>
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Volatility Index</p>
                                    <p class="text-lg font-bold text-purple-500">68.2</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Market Sentiment</p>
                                    <p class="text-lg font-bold text-green-500">Bullish</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Liquidity</p>
                                    <p class="text-lg font-bold text-blue-500">High</p>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-3 rounded-lg shadow-sm">
                                    <p class="text-xs text-gray-500 dark:text-gray-300">Trend Strength</p>
                                    <p class="text-lg font-bold text-yellow-500">Medium</p>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Metrics -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-ruler-combined"></i>
                                <span>Trade Metrics</span>
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Win Probability</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 72%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">72%
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Trade Efficiency</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">85%
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Risk Adjusted Return</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                        <div class="bg-purple-500 h-2.5 rounded-full" style="width: 64%"></div>
                                    </div>
                                    <p class="text-right text-sm font-medium text-gray-800 dark:text-gray-100 mt-1">1.42
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Trade Timeline -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-timeline"></i>
                                <span>Trade Timeline</span>
                            </h4>
                            <div class="relative">
                                <div class="absolute left-4 h-full w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                                <div class="space-y-6">
                                    <!-- Entry -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                            <i class="fas fa-arrow-up text-blue-600 dark:text-blue-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Entry</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    15, 14:00 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Entered long at
                                                $28,450 with 0.5 BTC position</p>
                                        </div>
                                    </div>

                                    <!-- Stop Loss Adjustment -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                                            <i class="fas fa-adjust text-yellow-600 dark:text-yellow-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Stop Loss
                                                    Adjustment</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    15, 18:30 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Moved stop loss
                                                from $27,500 to $27,800 after initial pullback</p>
                                        </div>
                                    </div>

                                    <!-- Take Profit Hit -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Partial Exit
                                                </h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    17, 10:15 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Exited 50% position
                                                at $29,120 (80% of target)</p>
                                        </div>
                                    </div>

                                    <!-- Full Exit -->
                                    <div class="relative pl-10">
                                        <div
                                            class="absolute left-0 top-0 w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                                            <i class="fas fa-flag-checkered text-purple-600 dark:text-purple-400"></i>
                                        </div>
                                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm">
                                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                                                <h5 class="font-medium text-gray-800 dark:text-gray-200">Full Exit</h5>
                                                <span class="text-sm text-gray-500 dark:text-gray-400 mt-1 sm:mt-0">June
                                                    17, 18:00 UTC</span>
                                            </div>
                                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Closed remaining
                                                position at $29,120 for total profit of $335.21</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Strategy Performance -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-chart-pie"></i>
                                <span>Strategy Performance</span>
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Total Trades</p>
                                            <p class="text-xl font-bold text-gray-800 dark:text-gray-100">24</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Win Rate</p>
                                            <p class="text-xl font-bold text-green-500">68%</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-300">
                                            <i class="fas fa-trophy"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Avg Profit</p>
                                            <p class="text-xl font-bold text-purple-500">2.1%</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-300">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white dark:bg-gray-600 p-4 rounded-lg shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-xs text-gray-500 dark:text-gray-300">Expectancy</p>
                                            <p class="text-xl font-bold text-yellow-500">0.45</p>
                                        </div>
                                        <div
                                            class="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-300">
                                            <i class="fas fa-balance-scale"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Journal Tab -->
                <div id="journal" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Trade Rationale -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-lightbulb text-yellow-500"></i>
                                    <span>Trade Rationale</span>
                                </h4>
                                <span id="viewStrategyBadge"
                                    class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full">...</span>
                            </div>
                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4">
                                <textarea readonly id="viewRationale"
                                    class="w-full h-32 p-3 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                                    placeholder="Describe your trade rationale here...">...</textarea>
                            </div>
                        </div>

                        <!-- Mistakes Made -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                                    <span>Mistakes Made</span>
                                </h4>
                                <!-- <button
                                    class="text-xs px-3 py-1 bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                                    <i class="fas fa-plus mr-1"></i> Add Mistake
                                </button> -->
                            </div>

                            <div id="viewMistakesContainer" class="space-y-3">
                                <!-- dynamically append mistakes -->
                            </div>
                        </div>

                        <!-- Emotional State -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                    <i class="fas fa-heart text-pink-500"></i>
                                    <span>Emotional State</span>
                                </h4>
                                <div class="flex items-center space-x-2">
                                    <span
                                        class="text-xs px-2 py-1 bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 rounded-full">Moderate
                                        Stress</span>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4 mb-4">
                                <p class="text-gray-700 dark:text-gray-200 mb-3">
                                    During this trade, I experienced a mix of emotions. Initially, I felt confident
                                    about the setup but became nervous during the first pullback. As the trade moved in
                                    my favor, excitement grew, but I also felt some regret for not taking partial
                                    profits earlier when the price stalled near my initial target.
                                </p>
                                <p class="text-gray-700 dark:text-gray-200">
                                    The most challenging moment was when price retraced nearly to my entry point - I
                                    felt tempted to exit early to avoid a loss, but stuck to my plan. Overall, I'm
                                    satisfied with how I managed my emotions, though I recognize room for improvement in
                                    maintaining composure during drawdowns.
                                </p>
                            </div>

                            <div class="grid grid-cols-2 sm:grid-cols-5 gap-3">
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-smile text-blue-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Confident</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-flushed text-yellow-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Nervous</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-grin-stars text-green-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Excited</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-sad-tear text-purple-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Regretful</span>
                                </button>
                                <button
                                    class="emoji-selector flex flex-col items-center p-2 rounded-lg bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                                    <div
                                        class="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center mb-1">
                                        <i class="fas fa-angry text-red-500 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center text-gray-700 dark:text-gray-300">Frustrated</span>
                                </button>
                            </div>
                        </div>

                        <!-- Lessons Learned -->
                        <div class="journal-card bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-sm">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                                <i class="fas fa-graduation-cap text-indigo-500"></i>
                                <span>Lessons Learned</span>
                            </h4>
                            <div class="bg-white dark:bg-gray-600 rounded-lg p-4">
                                <textarea readonly id="viewLesson"
                                    class="w-full h-32 p-3 text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                                    placeholder="What lessons did you learn from this trade?">...</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Media Tab -->
                <div id="media" class="tab-content tab-content-det">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Screenshots -->
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                                <i class="fas fa-camera"></i>
                                <span>Screenshots</span>
                            </h4>
                            <div id="viewImgContainer" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                                <!-- Images will be injected here -->
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-end">
                <button
                    class="hidden px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors mr-2">
                    Export
                </button>
                <button id="closeTradeViewButton"
                    class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-lg hover:bg-blue-600 dark:hover:bg-blue-500 transition-colors">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Lightbox Modal -->
<div id="imgLightbox" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 hidden z-50">
    <img id="lightboxImage" src="" alt="Screenshot Preview" class="max-h-full max-w-full rounded-lg shadow-lg" />
    <button id="closeLightbox"
        class="absolute top-5 right-5 text-white text-3xl font-bold hover:text-gray-300">&times;</button>
</div>