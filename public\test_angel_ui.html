<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Angel One Login Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-md">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Angel One Login Test</h1>
                <p class="text-gray-600">Test the Angel One integration</p>
            </div>

            <!-- Angel One Connection Form -->
            <form id="angel-test-form" class="space-y-4">
                <div>
                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                    <input type="text" id="api_key" name="api_key"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                           placeholder="Enter your Angel One API Key" required>
                </div>

                <div>
                    <label for="client_code" class="block text-sm font-medium text-gray-700 mb-2">Client Code</label>
                    <input type="text" id="client_code" name="client_code"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                           placeholder="Enter your Angel One Client Code" required>
                </div>

                <div>
                    <label for="mpin" class="block text-sm font-medium text-gray-700 mb-2">M-PIN</label>
                    <input type="password" id="mpin" name="mpin" maxlength="6"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                           placeholder="Enter your 6-digit M-PIN" required>
                    <p class="text-xs text-gray-500 mt-1">6-digit M-PIN for trading</p>
                </div>

                <div>
                    <label for="totp" class="block text-sm font-medium text-gray-700 mb-2">TOTP (6-digit code)</label>
                    <div class="flex space-x-2">
                        <input type="text" id="totp" name="totp" maxlength="6"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                               placeholder="Enter 6-digit TOTP code" required>
                        <button type="button" onclick="generateTOTP()" class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition text-sm flex items-center">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                        Get this from your authenticator app or click the refresh button to generate
                    </p>
                </div>

                <div class="bg-blue-50 p-3 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Setup Instructions:</h4>
                    <ol class="text-xs text-blue-700 space-y-1">
                        <li>1. Visit <a href="https://smartapi.angelbroking.com/" target="_blank" class="underline">Angel One SmartAPI</a></li>
                        <li>2. Generate API Key and Secret</li>
                        <li>3. Enable TOTP and scan QR code with authenticator app</li>
                        <li>4. Use your Client Code (not Client ID) and M-PIN</li>
                        <li>5. Get TOTP from authenticator app</li>
                    </ol>
                </div>

                <button type="submit" id="test-btn" 
                        class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition flex items-center justify-center">
                    <span>Test Angel One Connection</span>
                    <i id="loading-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                </button>
            </form>

            <!-- Results Section -->
            <div id="results" class="mt-6 hidden">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Test Results</h3>
                <div id="result-content" class="space-y-2"></div>
            </div>

            <!-- Demo Section -->
            <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
                <h4 class="text-sm font-medium text-yellow-800 mb-2">Demo Mode</h4>
                <p class="text-xs text-yellow-700 mb-3">
                    You can test the form validation and API structure without real credentials.
                </p>
                <button id="demo-btn" class="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700 mr-2">
                    Fill Demo Data
                </button>
                <button id="debug-btn" class="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700">
                    Debug Parameters
                </button>
            </div>
        </div>
    </div>

    <script>
        // Base URL for API calls
        const baseUrl = window.location.origin + '/';

        // Demo button functionality
        $('#demo-btn').click(function() {
            $('#api_key').val('demo_api_key_12345');
            $('#client_code').val('DEMO123');
            $('#mpin').val('123456');

            // Generate current TOTP for testing
            generateTOTP();
        });

        // Function to generate TOTP from the test secret
        function generateTOTP() {
            // This would normally come from an authenticator app
            // For testing purposes, we'll use a placeholder
            const currentTime = Math.floor(Date.now() / 1000 / 30);
            const testTOTP = '684262'; // Current TOTP from the generator
            $('#totp').val(testTOTP);

            // Show a note about TOTP
            if (!$('#totp-note').length) {
                $('#totp').after('<p id="totp-note" class="text-xs text-blue-600 mt-1">Using test TOTP. In real usage, get this from your authenticator app.</p>');
            }
        }

        // Debug button functionality
        $('#debug-btn').click(function() {
            const formData = {
                broker: 'angel_one',
                api_key: $('#api_key').val(),
                client_code: $('#client_code').val(),
                mpin: $('#mpin').val(),
                totp: $('#totp').val()
            };

            $.ajax({
                url: baseUrl + 'debug/broker',
                method: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    console.log('Debug Response:', response);
                    showResult(true, 'Debug data logged to console. Check browser developer tools.');
                },
                error: function(xhr, status, error) {
                    console.error('Debug Error:', xhr.responseText);
                    showResult(false, 'Debug failed: ' + error);
                }
            });
        });

        // Form submission
        $('#angel-test-form').submit(function(e) {
            e.preventDefault();
            
            const formData = {
                broker: 'angel_one',
                api_key: $('#api_key').val(),
                client_code: $('#client_code').val(),
                mpin: $('#mpin').val(),
                totp: $('#totp').val()
            };

            // Show loading state
            $('#test-btn').prop('disabled', true);
            $('#loading-spinner').removeClass('hidden');
            
            // Clear previous results
            $('#results').addClass('hidden');
            $('#result-content').empty();

            // Make API call to test Angel One connection
            $.ajax({
                url: baseUrl + 'saveBrokerDetails',
                method: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    showResult(response.success, response.message || 'Connection test completed');
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'Connection failed';
                    
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            const parsed = JSON.parse(xhr.responseText);
                            errorMessage = parsed.message || errorMessage;
                        } catch (e) {
                            errorMessage = 'Server error: ' + xhr.status;
                        }
                    }
                    
                    showResult(false, errorMessage);
                },
                complete: function() {
                    // Hide loading state
                    $('#test-btn').prop('disabled', false);
                    $('#loading-spinner').addClass('hidden');
                }
            });
        });

        function showResult(success, message) {
            const resultHtml = `
                <div class="flex items-start space-x-3 p-3 rounded-lg ${success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}">
                    <i class="fas ${success ? 'fa-check-circle text-green-600' : 'fa-exclamation-circle text-red-600'} mt-0.5"></i>
                    <div>
                        <p class="text-sm font-medium ${success ? 'text-green-800' : 'text-red-800'}">
                            ${success ? 'Success' : 'Error'}
                        </p>
                        <p class="text-xs ${success ? 'text-green-700' : 'text-red-700'} mt-1">
                            ${message}
                        </p>
                    </div>
                </div>
            `;
            
            $('#result-content').html(resultHtml);
            $('#results').removeClass('hidden');

            // If successful, show next steps
            if (success) {
                const nextStepsHtml = `
                    <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p class="text-sm font-medium text-blue-800 mb-2">Next Steps:</p>
                        <ul class="text-xs text-blue-700 space-y-1">
                            <li>• Go to your profile page to see the connected broker</li>
                            <li>• Use the "Sync Trades" button to fetch your trades</li>
                            <li>• Analyze your trading mistakes with real data</li>
                        </ul>
                    </div>
                `;
                $('#result-content').append(nextStepsHtml);
            }
        }

        // Auto-format TOTP input
        $('#totp').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            $(this).val(value);
        });

        // Real-time validation
        $('input[required]').on('blur', function() {
            const $input = $(this);
            const value = $input.val().trim();
            
            if (!value) {
                $input.addClass('border-red-500');
            } else {
                $input.removeClass('border-red-500').addClass('border-green-500');
            }
        });
    </script>
</body>
</html>
