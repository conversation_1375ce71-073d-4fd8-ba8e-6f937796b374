<?php
/**
 * Mistake Analysis Database Setup Script
 * Run this file in your browser to set up the mistake analysis tables
 */

// Database configuration - Update these with your XAMPP MySQL settings
$host = '     => ';
$username = '   => ';  // Default XAMPP username
$password = '   => '; (usually empty)
$database = '     => '; // Replace with your actual database name

echo "<h2>Mistake Analysis Database Setup</h2>";
echo "<p>Setting up database tables for mistake analysis functionality...</p>";

try {
    // Create connection
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Create mistakes table
    $createMistakesTable = "
    CREATE TABLE IF NOT EXISTS `mistakes` (
        `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `category` varchar(50) NOT NULL,
        `description` text,
        `severity` enum('Low','Medium','High') DEFAULT 'Medium',
        `impact` enum('Minor','Moderate','Critical') DEFAULT 'Moderate',
        `icon` varchar(50) DEFAULT NULL,
        `color_class` varchar(50) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` datetime DEFAULT NULL,
        `updated_at` datetime DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `idx_category` (`category`),
        KEY `idx_severity` (`severity`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    $pdo->exec($createMistakesTable);
    echo "<p style='color: green;'>✓ Mistakes table created successfully</p>";
    
    // Check if mistake_id column exists in trades table
    $checkColumn = "SHOW COLUMNS FROM `trades` LIKE 'mistake_id'";
    $result = $pdo->query($checkColumn);
    
    if ($result->rowCount() == 0) {
        // Add mistake_id column to trades table
        $addColumn = "ALTER TABLE `trades` ADD COLUMN `mistake_id` int(11) unsigned DEFAULT NULL AFTER `rr_ratio`";
        $pdo->exec($addColumn);
        echo "<p style='color: green;'>✓ Added mistake_id column to trades table</p>";
        
        // Add foreign key constraint
        $addForeignKey = "ALTER TABLE `trades` ADD CONSTRAINT `fk_trades_mistake` FOREIGN KEY (`mistake_id`) REFERENCES `mistakes` (`id`) ON DELETE SET NULL ON UPDATE CASCADE";
        $pdo->exec($addForeignKey);
        echo "<p style='color: green;'>✓ Added foreign key constraint</p>";
        
        // Add index
        $addIndex = "ALTER TABLE `trades` ADD INDEX `idx_mistake_id` (`mistake_id`)";
        $pdo->exec($addIndex);
        echo "<p style='color: green;'>✓ Added index for mistake_id</p>";
    } else {
        echo "<p style='color: orange;'>⚠ mistake_id column already exists in trades table</p>";
    }
    
    // Check if default mistakes exist
    $checkMistakes = "SELECT COUNT(*) FROM `mistakes`";
    $stmt = $pdo->query($checkMistakes);
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert default mistakes
        $insertMistakes = "
        INSERT INTO `mistakes` (`name`, `category`, `description`, `severity`, `impact`, `icon`, `color_class`, `created_at`, `updated_at`) VALUES
        ('Overconfidence', 'Cognitive', 'Ignored warning signs due to overconfidence in initial analysis', 'High', 'Critical', 'fas fa-brain', 'red', NOW(), NOW()),
        ('Rushing', 'Behavioral', 'Made decision too quickly without proper verification', 'Medium', 'Moderate', 'fas fa-running', 'yellow', NOW(), NOW()),
        ('Distraction', 'Environmental', 'Lost focus during critical decision moment', 'Low', 'Minor', 'fas fa-mobile-alt', 'blue', NOW(), NOW()),
        ('Fatigue', 'Psychological', 'Mental exhaustion affected decision quality', 'Medium', 'Moderate', 'fas fa-tired', 'purple', NOW(), NOW()),
        ('Knowledge Gap', 'Educational', 'Lacked sufficient knowledge about the market condition', 'Medium', 'Moderate', 'fas fa-book', 'green', NOW(), NOW()),
        ('Emotional Trading', 'Psychological', 'Let emotions override logical analysis', 'High', 'Critical', 'fas fa-heart', 'red', NOW(), NOW()),
        ('FOMO (Fear of Missing Out)', 'Psychological', 'Entered trade due to fear of missing opportunity', 'Medium', 'Moderate', 'fas fa-clock', 'orange', NOW(), NOW()),
        ('Revenge Trading', 'Psychological', 'Traded to recover losses from previous trade', 'High', 'Critical', 'fas fa-angry', 'red', NOW(), NOW()),
        ('Poor Risk Management', 'Technical', 'Failed to set proper stop loss or position size', 'High', 'Critical', 'fas fa-shield-alt', 'red', NOW(), NOW()),
        ('Analysis Paralysis', 'Cognitive', 'Over-analyzed and missed the optimal entry point', 'Medium', 'Moderate', 'fas fa-search', 'blue', NOW(), NOW())
        ";
        
        $pdo->exec($insertMistakes);
        echo "<p style='color: green;'>✓ Inserted default mistake types (10 mistakes)</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Default mistakes already exist ($count mistakes found)</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 Setup Complete!</h3>";
    echo "<p>The mistake analysis functionality has been successfully set up. You can now:</p>";
    echo "<ul>";
    echo "<li>Access the Mistake Analysis dashboard from the sidebar</li>";
    echo "<li>Select mistakes when adding trades</li>";
    echo "<li>View comprehensive mistake analytics</li>";
    echo "</ul>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li>Delete this setup file for security</li>";
    echo "<li>Go to your application and click 'Mistake Analysis' in the sidebar</li>";
    echo "<li>Start adding trades with mistake selections</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "<h4>Common Issues:</h4>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Update the database name in this script</li>";
    echo "<li>Check if the database exists</li>";
    echo "<li>Verify MySQL username/password</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h2 {
    color: #333;
    border-bottom: 2px solid #007cba;
    padding-bottom: 10px;
}
p {
    margin: 10px 0;
    padding: 8px;
    border-radius: 4px;
}
ul, ol {
    background-color: white;
    padding: 15px 30px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
</style>
