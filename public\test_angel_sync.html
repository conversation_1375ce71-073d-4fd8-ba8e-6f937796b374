<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Angel One Sync Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-sync-alt text-blue-600 mr-3"></i>
                Angel One Tradebook Sync Test
            </h1>

            <!-- Test Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-blue-800 mb-2">Prerequisites</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• You must be logged in to the platform</li>
                    <li>• Angel One account must be connected via Profile page</li>
                    <li>• Your Angel One account should have some trades to sync</li>
                </ul>
            </div>

            <!-- Test Buttons -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <button onclick="testAngelOneConnection()" class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition flex items-center justify-center">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>

                <button onclick="testAngelOneTradeBook()" class="bg-orange-600 text-white px-6 py-3 rounded-md hover:bg-orange-700 transition flex items-center justify-center">
                    <i class="fas fa-book mr-2"></i>
                    Test TradeBook API
                </button>

                <button onclick="syncAngelOneTrades()" class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition flex items-center justify-center">
                    <i class="fas fa-download mr-2"></i>
                    Sync Trades
                </button>

                <button onclick="viewAngelOneTrades()" class="bg-purple-600 text-white px-6 py-3 rounded-md hover:bg-purple-700 transition flex items-center justify-center">
                    <i class="fas fa-eye mr-2"></i>
                    View Raw Trades
                </button>
            </div>

            <!-- Status Display -->
            <div id="status-display" class="hidden mb-6">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">Status</h3>
                    <div id="status-content" class="text-sm text-gray-700">
                        <!-- Status will be displayed here -->
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="results-section" class="hidden">
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-chart-line text-green-600 mr-2"></i>
                        Results
                    </h2>
                    <div id="results-content" class="space-y-4">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loading" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                    <i class="fas fa-spinner animate-spin text-blue-600 text-xl"></i>
                    <span class="text-gray-700" id="loading-text">Processing...</span>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="text-center space-x-4">
            <a href="/profile" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-user mr-1"></i>
                Go to Profile
            </a>
            <a href="/trades" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-chart-line mr-1"></i>
                View Trades
            </a>
            <a href="/profile" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-cog mr-1"></i>
                Angel One Setup
            </a>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDisplay = $('#status-display');
            const statusContent = $('#status-content');
            
            let bgColor = 'bg-blue-50 border-blue-200';
            let textColor = 'text-blue-700';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200';
                textColor = 'text-green-700';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200';
                textColor = 'text-red-700';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200';
                textColor = 'text-yellow-700';
            }
            
            statusDisplay.removeClass('hidden');
            statusDisplay.find('.bg-gray-50').removeClass('bg-gray-50 border-gray-200').addClass(bgColor);
            statusContent.removeClass('text-gray-700').addClass(textColor);
            statusContent.html(message);
        }

        function showLoading(text = 'Processing...') {
            $('#loading-text').text(text);
            $('#loading').removeClass('hidden');
        }

        function hideLoading() {
            $('#loading').addClass('hidden');
        }

        function showResults(data) {
            const resultsContainer = $('#results-content');
            resultsContainer.empty();

            if (typeof data === 'object') {
                resultsContainer.append(`
                    <pre class="bg-gray-100 p-4 rounded text-xs overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>
                `);
            } else {
                resultsContainer.append(`
                    <div class="text-gray-700">${data}</div>
                `);
            }

            $('#results-section').removeClass('hidden');
        }

        function testAngelOneConnection() {
            showLoading('Testing Angel One connection...');
            showStatus('Testing connection to Angel One API...', 'info');

            $.ajax({
                url: '/angel/stats',
                method: 'GET',
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        showStatus('✅ Angel One connection successful!', 'success');
                        showResults(response);
                    } else {
                        showStatus('❌ Connection failed: ' + response.message, 'error');
                        showResults(response);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showStatus('❌ Connection test failed: ' + error, 'error');
                    showResults({
                        error: error,
                        status: status,
                        response: xhr.responseText
                    });
                }
            });
        }

        function testAngelOneTradeBook() {
            showLoading('Testing Angel One TradeBook API...');
            showStatus('Testing TradeBook API endpoint...', 'info');

            $.ajax({
                url: '/debug/angel/tradebook',
                method: 'GET',
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        showStatus('✅ TradeBook API test successful! Found ' + response.data_count + ' trades', 'success');
                        showResults(response);
                    } else {
                        showStatus('❌ TradeBook API test failed: ' + response.message, 'error');
                        showResults(response);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showStatus('❌ TradeBook API test failed: ' + error, 'error');
                    showResults({
                        error: error,
                        status: status,
                        response: xhr.responseText
                    });
                }
            });
        }

        function syncAngelOneTrades() {
            showLoading('Syncing trades from Angel One...');
            showStatus('Fetching tradebook data from Angel One...', 'info');

            $.ajax({
                url: '/syncTrades',
                method: 'POST',
                dataType: 'json',
                timeout: 60000,
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        showStatus('✅ ' + response.message, 'success');
                        showResults(response);
                    } else {
                        showStatus('❌ Sync failed: ' + response.message, 'error');
                        showResults(response);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showStatus('❌ Sync failed: ' + error, 'error');
                    showResults({
                        error: error,
                        status: status,
                        response: xhr.responseText
                    });
                }
            });
        }

        function viewAngelOneTrades() {
            showLoading('Fetching Angel One trades...');
            showStatus('Loading raw Angel One trade data...', 'info');

            $.ajax({
                url: '/angel/trades',
                method: 'GET',
                dataType: 'json',
                timeout: 30000,
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        showStatus('✅ Raw trades loaded successfully', 'success');
                        showResults(response);
                    } else {
                        showStatus('❌ Failed to load trades: ' + response.message, 'error');
                        showResults(response);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showStatus('❌ Failed to load trades: ' + error, 'error');
                    showResults({
                        error: error,
                        status: status,
                        response: xhr.responseText
                    });
                }
            });
        }

        // Auto-refresh status every 30 seconds when testing
        let autoRefreshInterval;
        
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                showStatus('Auto-refreshing status...', 'info');
            }, 30000);
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        // Initialize
        $(document).ready(function() {
            showStatus('Ready to test Angel One integration. Click a button above to start.', 'info');
        });
    </script>
</body>
</html>
