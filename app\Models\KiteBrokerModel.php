<?php

namespace App\Models;

use CodeIgniter\Model;

class KiteBrokerModel extends Model
{
    protected $table = 'kite_brokers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $allowedFields = ['user_id', 'api_key', 'api_secret', 'access_token', 'token_generated_at'];
}
