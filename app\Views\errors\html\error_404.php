<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern 404 Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 5s infinite'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-15px)' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s, color 0.3s;
            overflow-x: hidden;
        }

        .glow {
            text-shadow: 0 0 15px rgba(96, 165, 250, 0.5);
        }

        .dark .glow {
            text-shadow: 0 0 15px rgba(139, 92, 246, 0.7);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
        }

        .dark .gradient-bg {
            background: linear-gradient(135deg, #0c1a25, #111827);
        }

        .card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.85);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .dark .card {
            background: rgba(15, 23, 42, 0.85);
            border: 1px solid rgba(30, 41, 59, 0.5);
        }

        .number-container {
            position: relative;
            height: 220px;
        }

        @media (min-width: 768px) {
            .number-container {
                height: 320px;
            }
        }

        .floating-element {
            animation: float 4s ease-in-out infinite;
        }
    </style>
</head>

<body class="gradient-bg min-h-screen flex items-center justify-center text-gray-900 dark:text-gray-100 py-10">
    <div class="absolute top-5 right-5 z-50">
        <button id="theme-toggle"
            class="p-3 rounded-full bg-white/80 dark:bg-slate-800/80 shadow-lg hover:scale-110 transition-all duration-300 flex items-center justify-center">
            <svg id="theme-toggle-dark-icon" class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
            <svg id="theme-toggle-light-icon" class="w-5 h-5 hidden text-blue-500" fill="currentColor"
                viewBox="0 0 20 20">
                <path
                    d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                    clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>

    <div class="container mx-auto px-4 flex flex-col items-center">
        <div class="card rounded-2xl p-6 md:p-12 max-w-3xl w-full relative overflow-hidden shadow-2xl">
            <!-- Decorative elements -->
            <div
                class="absolute -top-16 -left-16 w-32 h-32 bg-blue-400 dark:bg-violet-600 rounded-full opacity-20 blur-3xl animate-pulse-slow">
            </div>
            <div
                class="absolute -bottom-20 -right-20 w-40 h-40 bg-indigo-400 dark:bg-purple-700 rounded-full opacity-30 blur-3xl animate-pulse-slow">
            </div>
            <div
                class="absolute top-1/4 right-1/4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 blur-3xl animate-bounce-slow">
            </div>

            <div class="relative z-10 flex flex-col items-center">
                <div class="number-container w-full flex justify-center items-center mb-6">
                    <div class="flex items-center">
                        <span
                            class="glow text-8xl md:text-9xl font-bold text-blue-600 dark:text-violet-400 mr-2 md:mr-4">4</span>
                        <div class="relative mx-2 md:mx-4">
                            <div
                                class="absolute inset-0 bg-blue-200 dark:bg-violet-900 rounded-full blur-xl animate-ping opacity-30">
                            </div>
                            <div
                                class="relative w-32 h-32 md:w-48 md:h-48 flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-300 dark:from-indigo-900 dark:to-violet-800 rounded-full shadow-lg">
                                <div class="floating-element">
                                    <div class="w-12 h-12 md:w-16 md:h-16 bg-yellow-400 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                        <span
                            class="glow text-8xl md:text-9xl font-bold text-blue-600 dark:text-violet-400 ml-2 md:ml-4">4</span>
                    </div>
                </div>

                <h1 class="text-3xl md:text-4xl font-bold text-center">Page Not Found</h1>
                <p class="mt-4 text-center text-gray-600 dark:text-gray-300 max-w-md">
                    The page you're looking for has been lost in the digital universe.
                </p>

                <div class="mt-8 w-full max-w-md">
                    <a href="<?= base_url() ?>"
                        class="py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg flex items-center justify-center gap-2">
                        <i class="fas fa-home"></i>
                        Go Home
                    </a>
                    <!-- <a href="#"
                        class="py-3 px-6 bg-transparent border border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2">
                        <i class="fas fa-arrow-left"></i>
                        Go Back 
                    </a>-->
                </div>

                <!-- <div class="mt-10 w-full max-w-md">
                    <p class="text-center text-gray-500 dark:text-gray-400 mb-3">Or search for something else</p>
                    <div class="relative">
                        <input type="text" placeholder="Search..."
                            class="w-full py-3 px-4 pr-12 bg-white/70 dark:bg-slate-800/70 border border-gray-300 dark:border-slate-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-violet-500 shadow-sm">
                        <button
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div> -->
            </div>
        </div>

        <div class="mt-8 text-center text-gray-500 dark:text-gray-400 text-sm">
            <p>© 2023 Digital Universe. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Theme toggle functionality
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');

        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            themeToggleLightIcon.classList.remove('hidden');
            themeToggleDarkIcon.classList.add('hidden');
        } else {
            document.documentElement.classList.remove('dark');
            themeToggleLightIcon.classList.add('hidden');
            themeToggleDarkIcon.classList.remove('hidden');
        }

        themeToggleBtn.addEventListener('click', function () {
            themeToggleDarkIcon.classList.toggle('hidden');
            themeToggleLightIcon.classList.toggle('hidden');

            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        });
    </script>
</body>

</html>