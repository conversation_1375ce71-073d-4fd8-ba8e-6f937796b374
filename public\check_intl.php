<?php
// Check if intl extension is loaded
if (extension_loaded('intl')) {
    echo "✅ PHP intl extension is loaded!<br>";
    echo "Locale class exists: " . (class_exists('Locale') ? 'Yes' : 'No') . "<br>";
    echo "Current locale: " . Locale::getDefault() . "<br>";
} else {
    echo "❌ PHP intl extension is NOT loaded!<br>";
    echo "Please enable the intl extension in your php.ini file.<br>";
}

// Show all loaded extensions for debugging
echo "<br><strong>All loaded extensions:</strong><br>";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $ext) {
    echo $ext . "<br>";
}
?>
