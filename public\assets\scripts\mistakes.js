// Mistake Analysis Dashboard JavaScript

// Global function for button onclick (backup method)
function handleAddCustomMistake() {
    const modal = document.getElementById('customMistakeModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

// Make function available globally
window.handleAddCustomMistake = handleAddCustomMistake;

// Global variable to track current period
let currentPeriod = 'month';

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the mistake dashboard
    initializeMistakeDashboard();

    // Add resize listener for mobile responsiveness
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Reload chart on resize to adjust for mobile/desktop using current period
            loadMistakeDistribution(currentPeriod);
        }, 250);
    });
});

function initializeMistakeDashboard() {
    loadMistakeMetrics();
    loadMistakeDistribution('month');
    loadRecentMistakes();
    loadMistakeHeatmap();
    loadCustomMistakes();
    initializeModal();
    initializeCustomMistakeModal();

    // Initialize period buttons
    setTimeout(initializePeriodButtons, 100);
}

// Load mistake metrics (overview cards)
function loadMistakeMetrics() {
    fetch('/getMistakeMetrics')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading metrics:', data.error);
                return;
            }

            // Update total mistakes
            document.getElementById('totalMistakes').textContent = data.totalMistakes;
            
            // Update most common mistake
            document.getElementById('mostCommonName').textContent = data.mostCommon.name;
            document.getElementById('mostCommonCount').textContent = data.mostCommon.count;
            
            // Update improvement rate
            const improvementRate = data.improvementRate;
            document.getElementById('improvementRate').textContent = improvementRate + '%';
            
            // Update progress bars and colors
            updateProgressBar('weeklyProgress', Math.min(100, (data.thisWeekMistakes / 10) * 100));
            updateProgressBar('mostCommonProgress', Math.min(100, (data.mostCommon.count / 50) * 100));
            updateProgressBar('improvementProgress', Math.min(100, Math.abs(improvementRate)));
            
            // Update improvement color and elements
            const improvementElement = document.getElementById('improvementChange');
            const improvementProgress = document.getElementById('improvementProgress');
            const improvementIcon = document.querySelector('.metric-card:nth-child(3) .rounded-full'); // Icon container

            if (improvementRate > 0) {
                // Positive improvement (fewer mistakes)
                improvementElement.textContent = '+' + improvementRate + '%';
                improvementElement.className = 'font-medium text-green-500';
                improvementProgress.className = 'bg-green-500 h-2 rounded-full transition-all duration-300';
                if (improvementIcon) {
                    improvementIcon.className = 'p-2 sm:p-3 rounded-full bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-300 flex-shrink-0';
                }
            } else if (improvementRate < 0) {
                // Negative improvement (more mistakes)
                improvementElement.textContent = improvementRate + '%';
                improvementElement.className = 'font-medium text-red-500';
                improvementProgress.className = 'bg-red-500 h-2 rounded-full transition-all duration-300';
                if (improvementIcon) {
                    improvementIcon.className = 'p-2 sm:p-3 rounded-full bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-300 flex-shrink-0';
                }
            } else {
                // No change
                improvementElement.textContent = '0%';
                improvementElement.className = 'font-medium text-gray-500';
                improvementProgress.className = 'bg-gray-500 h-2 rounded-full transition-all duration-300';
                if (improvementIcon) {
                    improvementIcon.className = 'p-2 sm:p-3 rounded-full bg-gray-100 dark:bg-gray-900/50 text-gray-600 dark:text-gray-300 flex-shrink-0';
                }
            }
        })
        .catch(error => {
            console.error('Error loading mistake metrics:', error);
        });
}

// Load mistake distribution chart
function loadMistakeDistribution(period = 'month') {
    fetch(`/getMistakeDistribution?period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading distribution:', data.error);
                return;
            }

            const chartContainer = document.getElementById('mistakeChart');
            if (!chartContainer) {
                console.error('Chart container not found!');
                return;
            }

            chartContainer.innerHTML = '';
            console.log('Chart container found:', chartContainer);

            if (!data.mistakes || data.mistakes.length === 0) {
                chartContainer.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">No mistake data available</div>';
                return;
            }

            // Color mapping for different mistake types with actual hex colors
            const colorMap = {
                'red': '#ef4444',
                'orange': '#f97316',
                'yellow': '#eab308',
                'blue': '#3b82f6',
                'purple': '#8b5cf6',
                'green': '#22c55e',
                'pink': '#ec4899',
                'indigo': '#6366f1',
                'gray': '#6b7280'
            };

            // Find the maximum percentage for scaling
            const maxPercentage = Math.max(...data.mistakes.map(m => m.percentage));
            console.log('Max percentage:', maxPercentage);
            console.log('Mistake data:', data.mistakes);

            data.mistakes.forEach((mistake, index) => {
                // Scale height relative to the maximum percentage (make tallest bar 100% of container)
                // Use a minimum height of 10% to ensure small bars are visible
                const scaledHeight = (mistake.percentage / maxPercentage) * 90 + 10; // 10-100% range
                const color = colorMap[mistake.color] || colorMap['blue'];

                // Check if mobile view
                const isMobile = window.innerWidth < 640;
                // Use full name for mobile inside labels, truncate for desktop bottom labels
                const fullName = mistake.name;
                const truncatedName = mistake.name.length > 12 ?
                    mistake.name.substring(0, 12) + '...' : mistake.name;

                console.log(`${mistake.name}: ${mistake.percentage}% -> ${scaledHeight}% height, color: ${color}`);

                const barContainer = document.createElement('div');
                barContainer.className = 'bar-container';

                // Create mobile-specific label inside bar
                let mobileLabel = '';
                if (isMobile) {
                    if (scaledHeight > 40) {
                        // Full name for tall bars
                        mobileLabel = `
                            <div class="mobile-bar-label" style="
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translateX(-50%) translateY(-50%) rotate(-90deg);
                                transform-origin: center;
                                font-size: 10px;
                                font-weight: 600;
                                color: white;
                                text-shadow: 0 1px 2px rgba(0,0,0,0.8);
                                white-space: nowrap;
                                pointer-events: none;
                                z-index: 5;
                                letter-spacing: 0.2px;
                            ">${fullName}</div>
                        `;
                    } else if (scaledHeight > 20) {
                        // Abbreviated name for medium bars
                        const shortName = fullName.length > 6 ? fullName.substring(0, 6) + '...' : fullName;
                        mobileLabel = `
                            <div class="mobile-bar-label" style="
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translateX(-50%) translateY(-50%) rotate(-90deg);
                                transform-origin: center;
                                font-size: 9px;
                                font-weight: 600;
                                color: white;
                                text-shadow: 0 1px 2px rgba(0,0,0,0.8);
                                white-space: nowrap;
                                pointer-events: none;
                                z-index: 5;
                            ">${shortName}</div>
                        `;
                    }
                    // No label for very short bars (< 20% height)
                }

                barContainer.innerHTML = `
                    <div class="bar transition-all cursor-pointer"
                         style="height: ${scaledHeight}%; background-color: ${color}; position: relative;"
                         title="${mistake.name}: ${mistake.count} mistakes (${mistake.percentage}%)"
                         data-mistake-name="${mistake.name}">
                        <div class="percentage-label" style="position: absolute; top: -24px; left: 50%; transform: translateX(-50%); font-size: 11px; color: #374151; opacity: 0; transition: opacity 0.3s; background: #1f2937; color: white; padding: 3px 6px; border-radius: 3px; white-space: nowrap; z-index: 10;">
                            ${mistake.percentage}%
                        </div>
                        ${mobileLabel}
                    </div>
                    <div class="bar-label ${isMobile ? 'hidden' : ''}" title="${mistake.name}">${truncatedName}</div>
                `;

                // Add hover effect to show percentage
                const bar = barContainer.querySelector('.bar');
                const percentageLabel = barContainer.querySelector('.percentage-label');

                bar.addEventListener('mouseenter', () => {
                    bar.style.transform = 'scale(1.05)';
                    percentageLabel.style.opacity = '1';
                });
                bar.addEventListener('mouseleave', () => {
                    bar.style.transform = 'scale(1)';
                    percentageLabel.style.opacity = '0';
                });

                chartContainer.appendChild(barContainer);
                console.log('Added bar:', mistake.name, 'Height:', scaledHeight + '%');
            });

            console.log('Total bars added:', data.mistakes.length);
        })
        .catch(error => {
            console.error('Error loading mistake distribution:', error);
            const chartContainer = document.getElementById('mistakeChart');
            chartContainer.innerHTML = '<div class="text-center text-red-500 py-8">Failed to load chart data</div>';
        });
}

// Load recent mistakes table
function loadRecentMistakes() {
    fetch('/getRecentMistakes')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Recent mistakes data received:', data);

            if (data.error) {
                console.error('Error loading recent mistakes:', data.error);
                return;
            }

            const tableBody = document.getElementById('mistakeTableBody');
            tableBody.innerHTML = '';

            if (!data || data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            <div class="flex flex-col items-center py-8">
                                <i class="fas fa-chart-line text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                                <p class="text-lg font-medium mb-2">No mistakes recorded yet</p>
                                <p class="text-sm">Add some trades with mistakes to see analytics here.</p>
                                <p class="text-xs mt-2 text-blue-500">
                                    <a href="/addTrade" class="hover:underline">Add a trade →</a>
                                </p>
                            </div>
                        </td>
                    </tr>
                `;
                // Also update total count
                document.getElementById('totalMistakeCount').textContent = '0';
                return;
            }

            data.forEach(mistake => {
                // Debug logging
                console.log('Processing mistake:', mistake);
                console.log('Original icon:', mistake.icon);

                // Validate and sanitize data
                let name = mistake.name || 'Unknown';
                let category = mistake.category || 'Unknown';
                const severity = mistake.severity || 'Medium';
                const impact = mistake.impact || 'Moderate';
                let icon = mistake.icon || 'fas fa-question-circle';
                const colorClass = mistake.color_class || 'gray';
                const count = mistake.count || 0;

                // Clean up any corrupted data
                name = name.replace(/ace[^a-zA-Z]/gi, '').trim() || 'Unknown';
                category = category.replace(/ace[^a-zA-Z]/gi, '').trim() || 'Unknown';

                // Log problematic data
                if (mistake.name && mistake.name.includes('ace')) {
                    console.warn('Found "ace" in original data:', mistake);
                    console.warn('Cleaned name:', name);
                }

                // Ensure icon has proper Font Awesome classes and remove any problematic characters
                if (!icon || !icon.includes('fa-') || icon.includes('ace') || icon.length < 5) {
                    icon = 'fas fa-question-circle';
                }

                // Clean icon string of any non-standard characters and validate format
                icon = icon.replace(/[^\w\s-]/g, '').trim();

                // Additional validation - ensure it starts with fa
                if (!icon.startsWith('fa')) {
                    icon = 'fas fa-question-circle';
                }

                // Validate color class and remove any problematic characters
                const validColors = ['red', 'blue', 'green', 'yellow', 'purple', 'indigo', 'pink', 'gray', 'orange'];
                const cleanColorClass = colorClass.replace(/[^\w-]/g, '');
                const safeColorClass = validColors.includes(cleanColorClass) ? cleanColorClass : 'gray';

                const severityColor = getSeverityColor(severity);
                const impactColor = getImpactColor(impact);

                const row = document.createElement('tr');
                row.className = 'mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer';
                row.setAttribute('data-mistake-id', name.toLowerCase());

                // Create table cells step by step to avoid encoding issues
                const nameCell = document.createElement('td');
                nameCell.className = 'px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap';

                // Create elements safely to avoid XSS and encoding issues
                const flexDiv = document.createElement('div');
                flexDiv.className = 'flex items-center';

                const iconDiv = document.createElement('div');
                iconDiv.className = `flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-${safeColorClass}-100 dark:bg-${safeColorClass}-900/50 flex items-center justify-center`;

                const iconElement = document.createElement('i');
                iconElement.className = `${icon} text-${safeColorClass}-600 dark:text-${safeColorClass}-300 text-sm sm:text-base`;
                iconDiv.appendChild(iconElement);

                const textDiv = document.createElement('div');
                textDiv.className = 'ml-2 sm:ml-4';

                const nameDiv = document.createElement('div');
                nameDiv.className = 'text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-32 sm:max-w-none';
                nameDiv.textContent = name; // Use textContent to prevent XSS

                const detailDiv = document.createElement('div');
                detailDiv.className = 'text-xs text-gray-500 dark:text-gray-400 sm:hidden';
                detailDiv.textContent = `${severity} • ${impact}`;

                textDiv.appendChild(nameDiv);
                textDiv.appendChild(detailDiv);
                flexDiv.appendChild(iconDiv);
                flexDiv.appendChild(textDiv);
                nameCell.appendChild(flexDiv);

                const categoryCell = document.createElement('td');
                categoryCell.className = 'px-3 sm:px-6 py-3 sm:py-4';
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'text-xs sm:text-sm text-gray-900 dark:text-gray-100';
                categoryDiv.textContent = category;
                categoryCell.appendChild(categoryDiv);

                const severityCell = document.createElement('td');
                severityCell.className = 'hidden sm:table-cell px-6 py-4 whitespace-nowrap';
                const severitySpan = document.createElement('span');
                severitySpan.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${severityColor}`;
                severitySpan.textContent = severity;
                severityCell.appendChild(severitySpan);

                const impactCell = document.createElement('td');
                impactCell.className = 'hidden sm:table-cell px-6 py-4 whitespace-nowrap';
                const impactSpan = document.createElement('span');
                impactSpan.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${impactColor}`;
                impactSpan.textContent = impact;
                impactCell.appendChild(impactSpan);

                const countCell = document.createElement('td');
                countCell.className = 'px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap';
                const countDiv = document.createElement('div');
                countDiv.className = 'text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100';
                countDiv.textContent = count;
                countCell.appendChild(countDiv);

                // Append cells to row
                row.appendChild(nameCell);
                row.appendChild(categoryCell);
                row.appendChild(severityCell);
                row.appendChild(impactCell);
                row.appendChild(countCell);

                // Add click event to show modal
                row.addEventListener('click', () => showMistakeModal(mistake));

                tableBody.appendChild(row);
            });

            // Update total count
            document.getElementById('totalMistakeCount').textContent = data.length;
        })
        .catch(error => {
            console.error('Error loading recent mistakes:', error);
        });
}

// Load mistake heatmap
function loadMistakeHeatmap() {
    fetch('/getMistakeHeatmap')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading heatmap:', data.error);
                return;
            }

            const heatmapGrid = document.getElementById('heatmapGrid');
            heatmapGrid.innerHTML = '';

            // Create 35 cells (5 weeks × 7 days)
            const today = new Date();
            for (let i = 0; i < 35; i++) {
                const cell = document.createElement('div');
                cell.className = 'heatmap-day bg-gray-200 dark:bg-gray-700';

                // Calculate the date for this cell (going back from today)
                const cellDate = new Date(today);
                cellDate.setDate(today.getDate() - (34 - i));
                const dateString = cellDate.toISOString().split('T')[0];

                cell.setAttribute('data-date', dateString);
                cell.setAttribute('data-count', '0');
                heatmapGrid.appendChild(cell);
            }

            // Populate with actual data
            data.forEach(item => {
                const dayIndex = calculateDayIndex(item.date, item.day_of_week);
                if (dayIndex >= 0 && dayIndex < 35) {
                    const cell = heatmapGrid.children[dayIndex];
                    const intensity = Math.min(4, item.mistake_count);
                    const colors = [
                        'bg-gray-200 dark:bg-gray-700',
                        'bg-blue-200 dark:bg-blue-900',
                        'bg-blue-400 dark:bg-blue-700',
                        'bg-blue-600 dark:bg-blue-500',
                        'bg-blue-800 dark:bg-blue-400'
                    ];

                    cell.className = `heatmap-day ${colors[intensity]}`;
                    cell.setAttribute('data-date', item.date);
                    cell.setAttribute('data-count', item.mistake_count);
                }
            });

            // Add click event listeners to all heatmap cells
            addHeatmapClickListeners();
        })
        .catch(error => {
            console.error('Error loading mistake heatmap:', error);
        });
}

// Add click event listeners to heatmap cells
function addHeatmapClickListeners() {
    const heatmapCells = document.querySelectorAll('.heatmap-day');

    heatmapCells.forEach(cell => {
        cell.addEventListener('click', function(event) {
            event.stopPropagation();

            const date = this.getAttribute('data-date');
            const count = this.getAttribute('data-count');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 300);

            // Remove any existing tooltips
            removeHeatmapTooltips();

            // Show tooltip for all cells (including empty ones)
            showHeatmapTooltip(this, date, count);
        });
    });

    // Close tooltip when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.heatmap-day') && !event.target.closest('.heatmap-tooltip')) {
            removeHeatmapTooltips();
        }
    });
}

// Show heatmap tooltip
function showHeatmapTooltip(cell, date, count) {
    const tooltip = document.createElement('div');
    tooltip.className = 'heatmap-tooltip show';

    // Format the date nicely
    const formattedDate = formatHeatmapDate(date);
    const mistakeText = count == 1 ? 'mistake' : 'mistakes';

    tooltip.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 2px;">${formattedDate}</div>
        <div style="color: #9ca3af;">${count} ${mistakeText}</div>
    `;

    // Position the tooltip
    const cellRect = cell.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    tooltip.style.left = (cellRect.left + scrollLeft + cellRect.width / 2) + 'px';
    tooltip.style.top = (cellRect.top + scrollTop - 10) + 'px';

    document.body.appendChild(tooltip);

    // Adjust position if tooltip goes off screen
    const tooltipRect = tooltip.getBoundingClientRect();
    if (tooltipRect.right > window.innerWidth) {
        tooltip.style.left = (window.innerWidth - tooltipRect.width - 10) + 'px';
    }
    if (tooltipRect.left < 0) {
        tooltip.style.left = '10px';
    }
}

// Remove all heatmap tooltips
function removeHeatmapTooltips() {
    const tooltips = document.querySelectorAll('.heatmap-tooltip');
    tooltips.forEach(tooltip => tooltip.remove());
}

// Format date for heatmap tooltip
function formatHeatmapDate(dateString) {
    if (!dateString) return 'No date';

    const date = new Date(dateString);
    const options = {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    };

    return date.toLocaleDateString('en-US', options);
}

// Helper functions
function updateProgressBar(elementId, percentage) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.width = percentage + '%';
    }
}

function getSeverityColor(severity) {
    switch (severity) {
        case 'High': return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
        case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
        case 'Low': return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        default: return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200';
    }
}

function getImpactColor(impact) {
    switch (impact) {
        case 'Critical': return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
        case 'Moderate': return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        case 'Minor': return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
        default: return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200';
    }
}

function calculateDayIndex(date, dayOfWeek) {
    // This is a simplified calculation - you might need to adjust based on your specific needs
    const today = new Date();
    const mistakeDate = new Date(date);
    const diffTime = today - mistakeDate;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 35) return -1; // Too old
    
    return 34 - diffDays; // Reverse order (most recent at end)
}

// Modal functionality
function initializeModal() {
    const modal = document.getElementById('mistakeModal');
    const closeButtons = document.querySelectorAll('.close-mistake-modal');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            modal.classList.add('hidden');
        });
    });
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });
}

function showMistakeModal(mistake) {
    const modal = document.getElementById('mistakeModal');
    
    // Update modal content
    document.getElementById('mistakeType').textContent = mistake.name;
    document.getElementById('mistakeCategory').textContent = mistake.category;
    document.getElementById('mistakeSeverity').textContent = mistake.severity;
    document.getElementById('mistakeFrequency').textContent = mistake.count + ' times';
    document.getElementById('mistakeImpact').textContent = mistake.impact;
    
    // Format last occurrence
    if (mistake.last_occurrence) {
        const date = new Date(mistake.last_occurrence);
        document.getElementById('mistakeTime').textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } else {
        document.getElementById('mistakeTime').textContent = 'Never';
    }
    
    // Update icon
    const iconElement = document.getElementById('mistakeIcon');
    iconElement.className = `flex-shrink-0 h-12 w-12 rounded-lg bg-${mistake.color_class}-100 dark:bg-${mistake.color_class}-900/50 flex items-center justify-center`;
    iconElement.innerHTML = `<i class="${mistake.icon} text-${mistake.color_class}-600 dark:text-${mistake.color_class}-300 text-xl"></i>`;
    
    // Show modal
    modal.classList.remove('hidden');
}

// Custom Mistake Management Functions
function loadCustomMistakes() {
    fetch('/getCustomMistakes')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading custom mistakes:', data.error);
                return;
            }

            const container = document.getElementById('customMistakesContainer');
            const loadingDiv = document.getElementById('customMistakesLoading');

            if (loadingDiv) {
                loadingDiv.remove();
            }

            if (data.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-lightbulb text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                        <p class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">No custom mistakes yet</p>
                        <p class="text-sm text-gray-500 dark:text-gray-500">Create your own mistake types to track specific trading errors.</p>
                    </div>
                `;
                return;
            }

            // Create custom mistakes grid
            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';

            data.forEach(mistake => {
                const mistakeCard = createCustomMistakeCard(mistake);
                grid.appendChild(mistakeCard);
            });

            container.innerHTML = '';
            container.appendChild(grid);
        })
        .catch(error => {
            console.error('Error loading custom mistakes:', error);
        });
}

function createCustomMistakeCard(mistake) {
    const card = document.createElement('div');
    card.className = 'bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600';

    const severityColor = getSeverityColor(mistake.severity);
    const impactColor = getImpactColor(mistake.impact);

    card.innerHTML = `
        <div class="flex items-start justify-between mb-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8 rounded-full bg-${mistake.color_class}-100 dark:bg-${mistake.color_class}-900/50 flex items-center justify-center mr-3">
                    <i class="${mistake.icon} text-${mistake.color_class}-600 dark:text-${mistake.color_class}-300 text-sm"></i>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">${mistake.name}</h4>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${mistake.category}</p>
                </div>
            </div>
            <div class="flex space-x-1">
                <button onclick="editCustomMistake(${mistake.id})" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    <i class="fas fa-edit text-xs"></i>
                </button>
                <button onclick="deleteCustomMistake(${mistake.id})" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                    <i class="fas fa-trash text-xs"></i>
                </button>
            </div>
        </div>
        <div class="flex space-x-2 mb-2">
            <span class="px-2 py-1 text-xs rounded-full ${severityColor}">${mistake.severity}</span>
            <span class="px-2 py-1 text-xs rounded-full ${impactColor}">${mistake.impact}</span>
        </div>
        ${mistake.description ? `<p class="text-xs text-gray-600 dark:text-gray-400">${mistake.description}</p>` : ''}
    `;

    return card;
}

function initializeCustomMistakeModal() {
    const modal = document.getElementById('customMistakeModal');
    const addBtn = document.getElementById('addCustomMistakeBtn');
    const closeButtons = document.querySelectorAll('.close-custom-mistake-modal');
    const form = document.getElementById('customMistakeForm');

    if (!addBtn || !modal) {
        console.log('Custom mistake elements not found, skipping initialization');
        return;
    }

    // Add button click
    addBtn.addEventListener('click', (e) => {
        e.preventDefault();
        openCustomMistakeModal();
    });

    // Close button clicks
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            modal.style.display = 'none';
            resetCustomMistakeForm();
        });
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
            resetCustomMistakeForm();
        }
    });

    // Form submission
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        submitCustomMistakeForm();
    });
}

function openCustomMistakeModal(mistakeData = null) {
    const modal = document.getElementById('customMistakeModal');
    const title = document.getElementById('customMistakeModalTitle');
    const submitBtn = document.getElementById('submitButtonText');

    if (!modal) {
        return;
    }

    if (mistakeData) {
        // Edit mode
        if (title) title.textContent = 'Edit Custom Mistake';
        if (submitBtn) submitBtn.textContent = 'Update Mistake';
        populateCustomMistakeForm(mistakeData);
        document.getElementById('customMistakeForm').dataset.mistakeId = mistakeData.id;
    } else {
        // Create mode
        if (title) title.textContent = 'Add Custom Mistake';
        if (submitBtn) submitBtn.textContent = 'Create Mistake';
        resetCustomMistakeForm();
        delete document.getElementById('customMistakeForm').dataset.mistakeId;
    }

    modal.style.display = 'flex';
}

// Make openCustomMistakeModal available globally
window.openCustomMistakeModal = openCustomMistakeModal;

function populateCustomMistakeForm(mistake) {
    document.getElementById('mistakeName').value = mistake.name;
    document.getElementById('mistakeCategory').value = mistake.category;
}

function resetCustomMistakeForm() {
    document.getElementById('customMistakeForm').reset();
}

function submitCustomMistakeForm() {
    const form = document.getElementById('customMistakeForm');
    const formData = new FormData(form);
    const mistakeId = form.dataset.mistakeId;

    const url = mistakeId ? `/updateCustomMistake/${mistakeId}` : '/createCustomMistake';
    const method = 'POST';

    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('customMistakeModal').style.display = 'none';
            resetCustomMistakeForm();
            loadCustomMistakes(); // Reload the list

            // Show success message (you can implement a toast notification here)
            console.log(data.message);
        } else {
            alert(data.error || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        alert('An error occurred while saving the mistake');
    });
}

function editCustomMistake(mistakeId) {
    // Fetch mistake data and open modal for editing
    fetch(`/getMistakeDetails?id=${mistakeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.mistake) {
                openCustomMistakeModal(data.mistake);
            } else {
                alert('Error loading mistake data');
            }
        })
        .catch(error => {
            console.error('Error loading mistake:', error);
            alert('Error loading mistake data');
        });
}

function deleteCustomMistake(mistakeId) {
    if (confirm('Are you sure you want to delete this custom mistake? This action cannot be undone.')) {
        fetch(`/deleteCustomMistake/${mistakeId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadCustomMistakes(); // Reload the list
                console.log(data.message);
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error deleting mistake:', error);
            alert('An error occurred while deleting the mistake');
        });
    }
}

// Initialize period buttons for mistake distribution
function initializePeriodButtons() {
    const thisMonthBtn = document.getElementById('thisMonthBtn');
    const allTimeBtn = document.getElementById('allTimeBtn');

    if (!thisMonthBtn || !allTimeBtn) {
        console.error('Period buttons not found');
        return;
    }

    console.log('Initializing period buttons');

    // Function to set active button
    function setActiveButton(activeBtn, inactiveBtn) {
        // Add loading state briefly
        activeBtn.style.opacity = '0.7';

        // Set active button styles
        activeBtn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-gray-200');
        activeBtn.classList.add('bg-primary-500', 'text-white');

        // Set inactive button styles
        inactiveBtn.classList.remove('bg-primary-500', 'text-white');
        inactiveBtn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-gray-200');

        // Remove loading state
        setTimeout(() => {
            activeBtn.style.opacity = '1';
        }, 200);
    }

    // Set initial active state (This Month)
    setActiveButton(thisMonthBtn, allTimeBtn);

    // Add click handlers
    thisMonthBtn.addEventListener('click', () => {
        console.log('This Month button clicked');
        currentPeriod = 'month';
        setActiveButton(thisMonthBtn, allTimeBtn);
        loadMistakeDistribution('month');
    });

    allTimeBtn.addEventListener('click', () => {
        console.log('All Time button clicked');
        currentPeriod = 'all';
        setActiveButton(allTimeBtn, thisMonthBtn);
        loadMistakeDistribution('all');
    });

    console.log('Period buttons initialized successfully');
}


