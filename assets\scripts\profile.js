$(document).ready(function () {
    fetchConnectedBroker()
});

// DOM Elements
const editProfileBtn = document.getElementById('edit-profile-btn');
const editProfilePopup = document.getElementById('edit-profile-popup');
const closePopupBtn = document.getElementById('close-popup');
const cancelEditBtn = document.getElementById('cancel-edit');
const editProfileForm = document.getElementById('edit-profile-form');
const saveSpinner = document.getElementById('save-spinner');

// Display elements
const displayName = document.getElementById('display-name');
const displayEmail = document.getElementById('display-email');

// Form elements
const nameInput = document.getElementById('name');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirm-password');

// Error elements
const nameError = document.getElementById('name-error');
const emailError = document.getElementById('email-error');
const passwordError = document.getElementById('password-error');
const confirmPasswordError = document.getElementById('confirm-password-error');

// Toggle popup
function togglePopup() {
    editProfilePopup.classList.toggle('active');
}

// Validate form
function validateForm() {
    let isValid = true;

    // Reset errors
    nameError.classList.add('hidden');
    emailError.classList.add('hidden');
    passwordError.classList.add('hidden');
    confirmPasswordError.classList.add('hidden');
    nameInput.classList.remove('input-error');
    emailInput.classList.remove('input-error');
    passwordInput.classList.remove('input-error');
    confirmPasswordInput.classList.remove('input-error');

    // Validate name
    if (!nameInput.value.trim()) {
        nameError.classList.remove('hidden');
        nameInput.classList.add('input-error');
        isValid = false;
    }

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailInput.value)) {
        emailError.classList.remove('hidden');
        emailInput.classList.add('input-error');
        isValid = false;
    }

    // Validate passwords if provided
    if (passwordInput.value || confirmPasswordInput.value) {
        if (passwordInput.value.length < 8) {
            passwordError.classList.remove('hidden');
            passwordInput.classList.add('input-error');
            isValid = false;
        }

        if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordError.classList.remove('hidden');
            confirmPasswordInput.classList.add('input-error');
            isValid = false;
        }
    }

    return isValid;
}

// Event Listeners
editProfileBtn.addEventListener('click', togglePopup);
closePopupBtn.addEventListener('click', togglePopup);
cancelEditBtn.addEventListener('click', togglePopup);

editProfileForm.addEventListener('submit', function (e) {
    e.preventDefault();

    if (validateForm()) {
        saveSpinner.classList.remove('hidden');

        $.ajax({
            url: base_url + 'updateProfile', // 🔁 Change to your actual endpoint
            method: 'POST',
            data: {
                name: nameInput.value,
                email: emailInput.value,
                password: passwordInput.value,
                confirm_password: confirmPasswordInput.value
            },
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    displayName.textContent = nameInput.value;
                    displayEmail.textContent = emailInput.value;

                    showToast('Profile updated successfully!', 'success');
                    togglePopup();
                } else {
                    showToast(response.message || 'Something went wrong', 'error');
                }
            },
            error: function () {
                showToast('Server error occurred', 'error');
            },
            complete: function () {
                saveSpinner.classList.add('hidden');
                passwordInput.value = '';
                confirmPasswordInput.value = '';
            }
        });
    }

});

// Close popup when clicking outside
editProfilePopup.addEventListener('click', function (e) {
    if (e.target === editProfilePopup) {
        togglePopup();
    }
});

// Toast notification function
function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-md shadow-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

function closeModal(id) {
    $(id).removeClass('active').addClass('hidden')
}

function openBModal(id) {
    $(id).removeClass('hidden').addClass('active')
}

$('#connect-broker-btn').click(function () {
    $('#broker-selection-popup').removeClass('hidden').addClass('active');
})

function copyUrl(input) {
    const redirectUrl = document.getElementById(input);
    redirectUrl.select();
    document.execCommand('copy');
    showToast('Redirect URL copied to clipboard!', 'success');
}


function saveBrokerDetails(broker, postUrl, requiredClass, form) {
    var isValid = true;
    var errorText = '';

    $(requiredClass).each(function () {
        var value = $(this).val();
        if (!value) {
            errorText = $(this).closest('div').find('.error-message').text() || 'Required field is missing';
            isValid = false;
            return false;
        }
    });

    if (!isValid) {
        createToast('error', 'Validation Error', errorText);
        return false;
    }

    // Create FormData and append broker
    var formData = new FormData($(form)[0]);
    formData.append('broker', broker);

    $.ajax({
        type: "POST",
        url: postUrl,
        data: formData,
        dataType: "JSON",
        processData: false,
        contentType: false,
        success: function (response) {
            if (response.success) {
                createToast('success', 'Success', response.message || 'Broker saved successfully');

                closeModal(`#${broker}-popup`);
                closeModal('#broker-selection-popup');
                $(requiredClass).val('')
                fetchConnectedBroker()
            } else {
                createToast('error', 'Error', response.message || 'Something went wrong');
                if (response.errors) {
                    console.log('Validation Errors:', response.errors);
                }
            }
        },
        error: function (xhr, status, error) {
            console.error("AJAX Error:", error);
            createToast('error', 'Server Error', 'Could not connect to server. Please try again later.');
        }
    });
}

function fetchConnectedBroker() {
    $.ajax({
        type: "POST",
        url: base_url + "fetchConnectedBroker",
        data: {},
        dataType: "JSON",
        success: function (response) {
            let html = '';

            if (response.success && response.data) {
                const broker = response.data.broker;
                const connectedOn = response.data.connected_on;

                html = `
                    <div class="flex items-center justify-between p-4 bg-white dark:bg-gray-600 rounded-lg shadow">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-gray-500 flex items-center justify-center">
                                <i class="fas fa-bolt text-blue-600 dark:text-blue-400 text-lg"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-800 dark:text-white">${capitalize(broker)}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-300">Connected on ${connectedOn}</p>
                            </div>
                        </div>
                        <button onclick="deleteBrokerConfirm()" class="text-red-500 hover:text-red-700 dark:hover:text-red-400 transition">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                `;
            } else {
                html = `
                    <div class="p-4 bg-white dark:bg-gray-600 rounded-lg text-center text-gray-500 dark:text-gray-300 italic">
                        No broker connected yet. Connect a broker to start syncing trades.
                    </div>
                `;
            }

            $('#connected-broker-section').html(html);
        },
        error: function () {
            $('#connected-broker-section').html(`
                <div class="p-4 bg-white dark:bg-gray-600 rounded-lg text-center text-red-500">
                    Failed to load broker data.
                </div>
            `);
        }
    });
}

// Capitalize first letter utility
function capitalize(text) {
    return text.charAt(0).toUpperCase() + text.slice(1);
}

function deleteBrokerConfirm() {
    showConfirmDialog({
        message: `Are you sure you want to delete this broker? This action cannot be undone.`,
        tradeId: '',
        action: 'delete',
        callback: deleteBroker
    });
}

function deleteBroker(id) {
    $.ajax({
        type: "POST",
        url: base_url + "deleteBroker",
        data: {},  // You can send the ID if needed
        dataType: "JSON",
        success: function (response) {
            if (response.success) {
                createToast('success', 'Broker Deleted', response.message || 'Broker connection removed successfully.');
                fetchConnectedBroker()
            } else {
                createToast('warning', 'Delete Failed', response.message || 'Could not delete broker connection.');
            }
        },
        error: function () {
            createToast('error', 'Server Error', 'Could not connect to server. Please try again later.');
        }
    });
}

// ANGEL ONE
let totpInterval = null;

// TOTP generation function
function generateTOTPCode() {
    const secretKey = document.getElementById('angel-totp-key').value.trim();

    if (!secretKey) {
        createToast('error', 'Error', 'Please enter your TOTP secret key first');
        return;
    }

    try {
        // Generate TOTP using the secret key
        const totp = generateTOTP(secretKey);

        // Update the TOTP input field
        document.getElementById('angel-totp').value = totp;

        // Show the TOTP display
        document.getElementById('totp-display').classList.remove('hidden');
        document.getElementById('current-totp').textContent = totp;

        // Start countdown timer
        startTOTPCountdown();

        createToast('success', 'Success', 'TOTP code generated successfully');

    } catch (error) {
        console.error('TOTP Generation Error:', error);
        createToast('error', 'Error', 'Failed to generate TOTP. Please check your secret key.');
    }
}

// TOTP countdown timer
function startTOTPCountdown() {
    if (totpInterval) {
        clearInterval(totpInterval);
    }

    totpInterval = setInterval(function () {
        const now = Math.floor(Date.now() / 1000);
        const timeStep = 30;
        const timeLeft = timeStep - (now % timeStep);

        document.getElementById('totp-countdown').textContent = timeLeft + 's';

        // Update progress bar
        const progressPercent = (timeLeft / timeStep) * 100;
        document.getElementById('totp-progress-bar').style.width = progressPercent + '%';

        // Auto-regenerate when expired
        if (timeLeft <= 1) {
            const secretKey = document.getElementById('angel-totp-key').value.trim();
            if (secretKey) {
                generateTOTPCode();
            }
        }
    }, 1000);
}

// Simple TOTP implementation
function generateTOTP(secret, window = 0) {
    const epoch = Math.floor(new Date().getTime() / 1000.0);
    const time = leftpad(dec2hex(Math.floor(epoch / 30) + window), 16, '0');

    const key = CryptoJS.enc.Hex.parse(base32ToHex(secret));
    const msg = CryptoJS.enc.Hex.parse(time);

    const hmac = CryptoJS.HmacSHA1(msg, key);
    const hmacHex = hmac.toString();

    const offset = hex2dec(hmacHex.substring(hmacHex.length - 1));
    const binary = (hex2dec(hmacHex.substr(offset * 2, 8)) & 0x7fffffff).toString();
    return binary.substr(binary.length - 6, 6);
}

// Helper functions for TOTP
function dec2hex(s) { return (s < 15.5 ? '0' : '') + Math.round(s).toString(16); }
function hex2dec(s) { return parseInt(s, 16); }
function leftpad(str, len, pad) {
    return str.length >= len ? str : new Array(len - str.length + 1).join(pad) + str;
}

function base32ToHex(base32) {
    const base32chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
    let bits = "";
    let hex = "";

    for (let i = 0; i < base32.length; i++) {
        const val = base32chars.indexOf(base32.charAt(i).toUpperCase());
        bits += leftpad(val.toString(2), 5, '0');
    }

    for (let i = 0; i + 4 <= bits.length; i += 4) {
        const chunk = bits.substr(i, 4);
        hex = hex + parseInt(chunk, 2).toString(16);
    }
    return hex;
}

function hexToBytes(hex) {
    const bytes = [];
    for (let c = 0; c < hex.length; c += 2) {
        bytes.push(parseInt(hex.substr(c, 2), 16));
    }
    return CryptoJS.lib.WordArray.create(bytes);
}

// Clear error messages on input
document.querySelectorAll('.angelReq').forEach(input => {
    input.addEventListener('input', function () {
        const errorMsg = this.parentNode.querySelector('.error-message');
        if (errorMsg && this.value.trim()) {
            errorMsg.classList.add('hidden');
        }
    });
});

// Auto-generate TOTP when secret key is entered
document.getElementById('angel-totp-key').addEventListener('input', function () {
    const secretKey = this.value.trim();
    if (secretKey.length >= 16) { // Minimum length for TOTP secret
        setTimeout(generateTOTPCode, 500); // Delay to avoid rapid generation
    }
});



// Add validation for TOTP before form submission
document.querySelector('button[onclick*="saveBrokerDetails"]').addEventListener('click', function (e) {
    const totpValue = document.getElementById('angel-totp').value.trim();
    const secretKey = document.getElementById('angel-totp-key').value.trim();

    if (secretKey && totpValue) {
        // Verify the TOTP is current
        const currentTOTP = generateTOTP(secretKey);
        if (totpValue !== currentTOTP) {
            e.preventDefault();
            createToast('warning', 'Warning', 'TOTP code may be expired. Generating new code...');
            generateTOTPCode();
            return false;
        }
    }
});

// Enhanced modal close function for Angel One popup
const originalCloseModal = window.closeModal;
window.closeModal = function (modalId) {
    if (modalId === '#angel-popup') {
        // Clear TOTP interval
        if (totpInterval) {
            clearInterval(totpInterval);
        }

        // Reset form
        document.getElementById('angel-form').reset();

        // Hide TOTP display
        document.getElementById('totp-display').classList.add('hidden');

        // Clear all error messages
        document.querySelectorAll('#angel-form .error-message').forEach(msg => {
            msg.classList.add('hidden');
        });

        // Reset field styling
        document.querySelectorAll('#angel-form input').forEach(input => {
            input.classList.remove('border-red-500', 'border-green-500');
        });
    }

    // Call original closeModal function if it exists
    if (originalCloseModal) {
        originalCloseModal(modalId);
    } else {
        // Fallback modal close
        document.querySelector(modalId).style.display = 'none';
    }
};