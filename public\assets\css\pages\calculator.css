/* .glow-card {
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.glow-card:hover {
    border-color: rgba(56, 189, 248, 0.3);
    box-shadow: 0 8px 32px rgba(6, 182, 212, 0.2);
} */

.accent-border {
    position: relative;
}

.accent-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 1) 50%, rgba(56, 189, 248, 0) 100%);
    z-index: 10;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.radio-btn input:checked+span {
    background-color: #0ea5e9;
    border-color: #0ea5e9;
}

.radio-btn input:checked+span::after {
    opacity: 1;
}

.toggle-checkbox:checked {
    right: 0;
    border-color: #0ea5e9;
    background-color: #0ea5e9;
}

.toggle-checkbox:checked+.toggle-label {
    background-color: #0ea5e9;
}

.result-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(6, 182, 212, 0.15);
}

.risk-gauge {
    height: 6px;
    box-shadow: 0 2px 10px rgba(6, 182, 212, 0.2);
}

.badge {
    background: rgba(8, 145, 178, 0.2);
    border: 1px solid rgba(6, 182, 212, 0.3);
}