<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;
use \App\Models\ChallengeModel;

use ResponseTrait;

helper('cookie');

class ChallengeController extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
        $this->challengemodel = new ChallengeModel();
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Take a Challenge';
        $data['active'] = 'challenge';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'challenge';
        $data['main_content'] = 'pages/challenge';

        // Get challenge analytics data
        // $data['challengeData'] = $this->getChallengeAnalytics($userId);

        return view('includes/template', $data);
    }

    private function getChallengeAnalytics($userId)
    {
        $db = \Config\Database::connect();

        // Get all trades for the user (last 6 months for challenge)
        $sixMonthsAgo = date('Y-m-d', strtotime('-6 months'));
        $trades = $this->trademodel->where('user_id', $userId)
            ->where('datetime >=', $sixMonthsAgo)
            ->where('deleted_at IS NULL')
            ->orderBy('datetime', 'DESC')
            ->findAll();

        // Calculate analytics
        $analytics = [
            'totalTrades' => count($trades),
            'winningTrades' => 0,
            'losingTrades' => 0,
            'totalPnl' => 0,
            'highestProfitDay' => 0,
            'highestProfitDate' => '',
            'maxDrawdown' => 0,
            'avgRiskReward' => 0,
            'winRate' => 0,
            'recentTrades' => array_slice($trades, 0, 10),
            'topTrades' => [],
            'mostTradedSymbols' => [],
            'equityData' => [],
            'dailyPnl' => [],
            'hasChallenge' => false,
            'challengeSettings' => null
        ];

        if (empty($trades)) {
            return $analytics;
        }

        // Calculate basic metrics
        $totalRR = 0;
        $rrCount = 0;
        $dailyPnlMap = [];
        $symbolCount = [];
        $runningEquity = 100000; // Starting capital
        $equityData = [];
        $maxEquity = $runningEquity;
        $maxDrawdownValue = 0;

        foreach ($trades as $trade) {
            $pnl = floatval($trade['pnl_amount'] ?? 0);
            $analytics['totalPnl'] += $pnl;

            if ($pnl > 0) {
                $analytics['winningTrades']++;
            } elseif ($pnl < 0) {
                $analytics['losingTrades']++;
            }

            // Risk-reward ratio
            if (!empty($trade['rr_ratio']) && $trade['rr_ratio'] > 0) {
                $totalRR += floatval($trade['rr_ratio']);
                $rrCount++;
            }

            // Daily PnL tracking
            $tradeDate = date('Y-m-d', strtotime($trade['datetime']));
            if (!isset($dailyPnlMap[$tradeDate])) {
                $dailyPnlMap[$tradeDate] = 0;
            }
            $dailyPnlMap[$tradeDate] += $pnl;

            // Symbol frequency
            $symbol = $trade['symbol'];
            if (!isset($symbolCount[$symbol])) {
                $symbolCount[$symbol] = 0;
            }
            $symbolCount[$symbol]++;

            // Equity curve calculation
            $runningEquity += $pnl;
            $equityData[] = [
                'date' => $tradeDate,
                'equity' => $runningEquity,
                'pnl' => $pnl
            ];

            // Max drawdown calculation
            if ($runningEquity > $maxEquity) {
                $maxEquity = $runningEquity;
            }
            $currentDrawdown = (($maxEquity - $runningEquity) / $maxEquity) * 100;
            if ($currentDrawdown > $maxDrawdownValue) {
                $maxDrawdownValue = $currentDrawdown;
            }
        }

        // Calculate win rate
        if ($analytics['totalTrades'] > 0) {
            $analytics['winRate'] = round(($analytics['winningTrades'] / $analytics['totalTrades']) * 100, 1);
        }

        // Calculate average risk-reward
        if ($rrCount > 0) {
            $analytics['avgRiskReward'] = round($totalRR / $rrCount, 1);
        }

        // Find highest profit day
        if (!empty($dailyPnlMap)) {
            $maxDailyPnl = max($dailyPnlMap);
            $maxDailyDate = array_search($maxDailyPnl, $dailyPnlMap);
            $analytics['highestProfitDay'] = $maxDailyPnl;
            $analytics['highestProfitDate'] = date('M j, Y', strtotime($maxDailyDate));
        }

        // Set max drawdown
        $analytics['maxDrawdown'] = round($maxDrawdownValue, 1);

        // Get top 3 trades
        usort($trades, function ($a, $b) {
            return floatval($b['pnl_amount'] ?? 0) <=> floatval($a['pnl_amount'] ?? 0);
        });
        $analytics['topTrades'] = array_slice($trades, 0, 3);

        // Get most traded symbols
        arsort($symbolCount);
        $analytics['mostTradedSymbols'] = array_slice($symbolCount, 0, 5, true);

        // Prepare equity curve data (group by month for chart)
        $monthlyEquity = [];
        foreach ($equityData as $point) {
            $month = date('M', strtotime($point['date']));
            $monthlyEquity[$month] = $point['equity'];
        }
        $analytics['equityData'] = $monthlyEquity;

        // Daily PnL for recent days
        $analytics['dailyPnl'] = array_slice($dailyPnlMap, -30, 30, true);

        return $analytics;
    }

    public function getChallengeData()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $challengeData = $this->getChallengeAnalytics($userId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $challengeData
        ]);
    }

    public function getChallengeEquityData()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $db = \Config\Database::connect();

        // Get trades for equity curve (last 12 months)
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));
        $trades = $this->trademodel->select('datetime, pnl_amount')
            ->where('user_id', $userId)
            ->where('datetime >=', $twelveMonthsAgo)
            ->where('deleted_at IS NULL')
            ->orderBy('datetime', 'ASC')
            ->findAll();

        $equityData = [];
        $runningEquity = 100000; // Starting capital
        $monthlyData = [];

        foreach ($trades as $trade) {
            $pnl = floatval($trade['pnl_amount'] ?? 0);
            $runningEquity += $pnl;
            $month = date('M Y', strtotime($trade['datetime']));
            $monthlyData[$month] = $runningEquity;
        }

        // Fill in missing months with previous value
        $labels = [];
        $data = [];
        $currentEquity = 100000;

        for ($i = 11; $i >= 0; $i--) {
            $month = date('M Y', strtotime("-$i months"));
            $labels[] = date('M', strtotime("-$i months"));

            if (isset($monthlyData[$month])) {
                $currentEquity = $monthlyData[$month];
            }
            $data[] = $currentEquity;
        }

        return $this->response->setJSON([
            'success' => true,
            'labels' => $labels,
            'data' => $data
        ]);
    }

    public function saveChallengeData()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request'
            ]);
        }

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $data = [
            'start_amount' => $this->request->getPost('startAmount'),
            'target_amount' => $this->request->getPost('targetAmount'),
            'timeframe' => $this->request->getPost('timeframe'),
            'custom_days' => $this->request->getPost('customDays'),
            'risk_per_trade' => $this->request->getPost('riskPerTrade'),
            'user_id' => $userId,
        ];

        // Check if a challenge already exists for this user
        $existing = $this->challengemodel->where('user_id', $userId)->first();

        if ($existing) {
            // Update the existing challenge
            if ($this->challengemodel->update($existing['id'], $data)) {
                return $this->response->setJSON(['success' => true, 'message' => 'Challenge updated successfully']);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update challenge'
                ]);
            }
        } else {
            // Insert a new challenge
            if ($this->challengemodel->insert($data)) {
                return $this->response->setJSON(['success' => true, 'message' => 'Challenge saved successfully']);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save challenge'
                ]);
            }
        }
    }

    public function getProgressData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();

        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $challengeStartDate = date('Y-m-d', strtotime($challenge['created_at']));

        // Only sum trades made during the challenge
        $totalPnLRow = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $challengeStartDate)
            ->selectSum('pnl_amount')
            ->first();

        $totalPnL = floatval($totalPnLRow['pnl_amount'] ?? 0);

        // Progress calculated from 0
        $growthNeeded = $challenge['target_amount'] - $challenge['start_amount'];
        $progressPercent = $growthNeeded > 0 ? ($totalPnL / $growthNeeded) * 100 : 0;
        $progressPercent = round(max(min($progressPercent, 100), 0), 2); // Ensure 0 <= x <= 100

        return $this->response->setJSON([
            'success' => true,
            'progressPercent' => $progressPercent
        ]);
    }

    public function getChallengeMeta()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();

        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $totalDays = $challenge['custom_days'] > 0 ? intval($challenge['custom_days']) : intval($challenge['timeframe']);

        $createdAt = new \DateTime(date('Y-m-d', strtotime($challenge['created_at'])));
        $today = new \DateTime();

        $elapsedDays = $createdAt->diff($today)->days;
        $daysRemaining = max(0, $totalDays - $elapsedDays);

        $projectedDate = $createdAt->add(new \DateInterval("P{$totalDays}D"))->format('d M y');

        return $this->response->setJSON([
            'success' => true,
            'daysRemaining' => $daysRemaining,
            'projectedDate' => $projectedDate
        ]);
    }


    public function getChallengeStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();

        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'No challenge found']);
        }

        $challengeStartDate = date('Y-m-d', strtotime($challenge['created_at'])); // Format as Y-m-d
        $today = date('Y-m-d');

        $allTrades = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $challengeStartDate)
            ->findAll();

        $currency = '₹';

        $totalPnL = 0;
        $wins = 0;
        $losses = 0;
        $todayPnl = 0;

        foreach ($allTrades as $trade) {
            $pnl = floatval($trade['pnl_amount']);
            $totalPnL += $pnl;

            if ($pnl > 0)
                $wins++;
            elseif ($pnl < 0)
                $losses++;

            if (substr($trade['datetime'], 0, 10) === $today) {
                $todayPnl += $pnl;
            }
        }

        $startAmount = floatval($challenge['start_amount']);
        $targetAmount = floatval($challenge['target_amount']);
        $currentCapital = $startAmount + $totalPnL;
        $capitalProgressPercent = ($currentCapital / $targetAmount) * 100;

        $totalDays = $challenge['custom_days'] > 0 ? intval($challenge['custom_days']) : intval($challenge['timeframe']);
        $dailyTarget = ($targetAmount - $startAmount) / $totalDays;

        $winRate = ($wins + $losses) > 0 ? round(($wins / ($wins + $losses)) * 100, 2) : 0;

        return $this->response->setJSON([
            'success' => true,
            'currency' => $currency,
            'startAmount' => $startAmount,
            'currentAmount' => round($currentCapital, 2),
            'targetAmount' => $targetAmount,
            'capitalProgressPercent' => round(min($capitalProgressPercent, 100), 2),
            'dailyTarget' => round($dailyTarget, 2),
            'todayPnl' => round($todayPnl, 2),
            'winRate' => $winRate
        ]);
    }

    public function getChallengeStatsV2()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $startAmount = floatval($challenge['start_amount']);
        $targetAmount = floatval($challenge['target_amount']);
        $challengeStartDate = date('Y-m-d', strtotime($challenge['created_at']));

        $trades = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $challengeStartDate)
            ->orderBy('datetime', 'asc')
            ->findAll();

        $totalPnL = 0;
        $drawdown = 0;
        $peak = $startAmount;
        $lowestCapital = $startAmount;

        $rrs = [];
        $pnlByDate = [];

        foreach ($trades as $trade) {
            $pnl = floatval($trade['pnl_amount']);
            $totalPnL += $pnl;

            $capital = $startAmount + $totalPnL;
            if ($capital > $peak)
                $peak = $capital;
            if ($capital < $lowestCapital)
                $lowestCapital = $capital;

            $date = date('Y-m-d', strtotime($trade['datetime']));
            if (!isset($pnlByDate[$date]))
                $pnlByDate[$date] = 0;
            $pnlByDate[$date] += $pnl;

            if ($trade['stop_loss'] != 0 && $trade['target'] != 0) {
                $risk = abs($trade['entry_price'] - $trade['stop_loss']);
                $reward = abs($trade['exit_price'] - $trade['entry_price']);
                if ($risk > 0)
                    $rrs[] = $reward / $risk;
            }
        }

        // Progress to Target
        $currentCapital = $startAmount + $totalPnL;
        $progressPercent = ($targetAmount - $startAmount) > 0
            ? round(($totalPnL / ($targetAmount - $startAmount)) * 100, 2)
            : 0;

        // Clamp between 0 and 100
        $progressPercent = max(min($progressPercent, 100), 0);

        // Avg RR
        $avgRR = count($rrs) > 0 ? round(array_sum($rrs) / count($rrs), 2) : null;

        // Highest Profit Day
        $highestDayAmount = 0;
        $highestDayDate = null;

        foreach ($pnlByDate as $date => $pnl) {
            if ($pnl > $highestDayAmount) {
                $highestDayAmount = $pnl;
                $highestDayDate = $date;
            }
        }

        // Max Drawdown
        $drawdownPercent = $peak > 0 ? round((($peak - $lowestCapital) / $peak) * 100, 2) : 0;

        return $this->response->setJSON([
            'success' => true,
            'progressPercent' => $progressPercent,
            'avgRR' => $avgRR,
            'highestProfit' => $highestDayAmount,
            'highestProfitDate' => $highestDayDate ? date('d M y', strtotime($highestDayDate)) : null,
            'maxDrawdown' => $drawdownPercent
        ]);
    }

    public function getConfidenceLevel()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $startDate = date('Y-m-d', strtotime($challenge['created_at']));

        // Only consider confidence values from trades during challenge
        $trades = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $startDate)
            ->where('confidence IS NOT NULL')
            ->select('confidence')
            ->findAll();

        if (empty($trades)) {
            return $this->response->setJSON([
                'success' => true,
                'confidenceScore' => 0,
                'label' => 'N/A',
                'percentage' => 0
            ]);
        }

        $sum = 0;
        foreach ($trades as $trade) {
            $sum += intval($trade['confidence']);
        }

        $avg = $sum / count($trades); // Usually between 1 to 5
        $percent = round(($avg / 5) * 100); // Convert to percentage for meter position

        // Determine label
        $label = 'Low';
        if ($avg >= 4)
            $label = 'High';
        elseif ($avg >= 2.5)
            $label = 'Medium';

        return $this->response->setJSON([
            'success' => true,
            'confidenceScore' => round($avg, 1),
            'label' => $label,
            'percentage' => $percent
        ]);
    }


    public function getChallengeTradesPaginated()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON([
                'success' => true,
                'trades' => [],
                'total' => 0,
                'page' => 1,
                'limit' => 5
            ]);
        }

        $startDate = date('Y-m-d', strtotime($challenge['created_at']));
        $page = (int) $this->request->getPost('page');
        $limit = (int) $this->request->getPost('limit');

        if ($page <= 0)
            $page = 1;
        if ($limit <= 0)
            $limit = 5;

        $offset = ($page - 1) * $limit;

        // Base query
        $baseQuery = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $startDate);

        // Clone the query for count
        $totalTrades = $baseQuery->countAllResults(false);

        // Fetch paginated trades
        $trades = $baseQuery
            ->orderBy('datetime', 'desc')
            ->findAll($limit, $offset);

        return $this->response->setJSON([
            'success' => true,
            'trades' => $trades,
            'total' => $totalTrades,
            'page' => $page,
            'limit' => $limit
        ]);
    }


    public function getTopTrades()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $startDate = date('Y-m-d', strtotime($challenge['created_at']));

        $topTrades = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $startDate)
            ->orderBy('pnl_amount', 'DESC')
            ->limit(3)
            ->findAll();

        return $this->response->setJSON([
            'success' => true,
            'trades' => $topTrades
        ]);
    }

    public function getEquityCurve()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $startDate = new \DateTime($challenge['created_at']);
        $today = new \DateTime(); // today
        $totalDays = $challenge['custom_days'] > 0 ? intval($challenge['custom_days']) : intval($challenge['timeframe']);
        $endDate = clone $startDate;
        $endDate->modify("+$totalDays days");

        $labels = [];
        $equity = [];
        $startAmount = floatval($challenge['start_amount']);
        $currentEquity = $startAmount;

        // Cache cumulative PnL per date for performance
        $pnlByDate = [];

        // Preload all trades once for performance
        $trades = $this->trademodel
            ->where('user_id', $userId)
            ->where('DATE(datetime) >=', $startDate->format('Y-m-d'))
            ->orderBy('datetime', 'asc')
            ->findAll();

        foreach ($trades as $trade) {
            $date = (new \DateTime($trade['datetime']))->format('Y-m-d');
            if (!isset($pnlByDate[$date])) {
                $pnlByDate[$date] = 0;
            }
            $pnlByDate[$date] += floatval($trade['pnl_amount']);
        }

        $runningPnL = 0;
        $dateCursor = clone $startDate;

        while ($dateCursor <= $endDate) {
            $dateStr = $dateCursor->format('Y-m-d');
            $labels[] = $dateCursor->format('d M');

            if ($dateCursor <= $today) {
                if (isset($pnlByDate[$dateStr])) {
                    $runningPnL += $pnlByDate[$dateStr];
                }
                $currentEquity = $startAmount + $runningPnL;
                $equity[] = $currentEquity;
            } else {
                // Future date: show null (no bar)
                $equity[] = null;
            }

            $dateCursor->modify('+1 day');
        }

        // Calculate stats
        $validEquities = array_filter($equity, fn($val) => !is_null($val));
        $highest = !empty($validEquities) ? max($validEquities) : $startAmount;
        $lowest = !empty($validEquities) ? min($validEquities) : $startAmount;
        $volatility = $startAmount > 0 ? round((($highest - $lowest) / $startAmount) * 100, 2) : 0;
        $trend = end($validEquities) >= $startAmount ? 'Bullish' : 'Bearish';

        return $this->response->setJSON([
            'success' => true,
            'labels' => $labels,
            'equity' => $equity,
            'highest' => $highest,
            'lowest' => $lowest,
            'volatility' => $volatility,
            'trend' => $trend
        ]);
    }


    public function getMostTradedSymbols()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        // Get challenge to determine start date
        $challenge = $this->challengemodel->where('user_id', $userId)->first();
        if (!$challenge) {
            return $this->response->setJSON(['success' => false, 'message' => 'Challenge not found']);
        }

        $startDate = date('Y-m-d', strtotime($challenge['created_at']));

        // Fetch most traded symbols
        $db = \Config\Database::connect();
        $builder = $db->table('trades');
        $builder->select('symbol, COUNT(*) as trade_count');
        $builder->where('user_id', $userId);
        $builder->where('DATE(datetime) >=', $startDate);
        $builder->groupBy('symbol');
        $builder->orderBy('trade_count', 'DESC');
        $builder->limit(5);

        $results = $builder->get()->getResultArray();

        $maxCount = !empty($results) ? $results[0]['trade_count'] : 1;

        foreach ($results as &$row) {
            $row['percent'] = round(($row['trade_count'] / $maxCount) * 100);
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $results
        ]);
    }
}
