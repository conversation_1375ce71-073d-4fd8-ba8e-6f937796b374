<?php
/**
 * Simple Angel One API Test
 * Direct test without CodeIgniter framework
 */

header('Content-Type: application/json');

// Get parameters
$apiKey = $_POST['api_key'] ?? $_GET['api_key'] ?? 'demo_key';
$clientCode = $_POST['client_code'] ?? $_GET['client_code'] ?? 'demo_client';
$mpin = $_POST['mpin'] ?? $_GET['mpin'] ?? '123456';
$totp = $_POST['totp'] ?? $_GET['totp'] ?? '123456';

$debugInfo = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'credentials' => [
        'api_key' => substr($apiKey, 0, 10) . '...',
        'client_code' => $clientCode,
        'mpin' => str_repeat('*', strlen($mpin)),
        'totp' => $totp
    ],
    'tests' => []
];

try {
    // Test 1: Basic connectivity to Angel One
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://apiconnect.angelbroking.com',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_NOBODY => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'TradeDiary/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    $debugInfo['tests']['connectivity'] = [
        'success' => !$error && $httpCode < 400,
        'http_code' => $httpCode,
        'error' => $error ?: 'None',
        'message' => $error ? 'Failed to connect' : 'Connection successful'
    ];

    // Test 2: Authentication endpoint test
    $authUrl = 'https://apiconnect.angelbroking.com/rest/auth/angelbroking/user/v1/loginByPassword';
    $authData = [
        'clientcode' => $clientCode,
        'password' => $mpin,
        'totp' => $totp
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $authUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($authData),
        CURLOPT_HTTPHEADER => [
            'X-PrivateKey: ' . $apiKey,
            'Content-Type: application/json',
            'Accept: application/json',
            'X-UserType: USER',
            'X-SourceID: WEB',
            'User-Agent: TradeDiary/1.0'
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_FOLLOWLOCATION => true
    ]);

    $authResponse = curl_exec($ch);
    $authHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $authError = curl_error($ch);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);

    $debugInfo['tests']['authentication'] = [
        'success' => !$authError,
        'http_code' => $authHttpCode,
        'error' => $authError ?: 'None',
        'content_type' => $contentType,
        'response_length' => strlen($authResponse),
        'response_preview' => substr($authResponse, 0, 300),
        'is_json' => false,
        'json_error' => 'Not tested yet'
    ];

    // Try to decode the response
    if ($authResponse) {
        $decodedResponse = json_decode($authResponse, true);
        $debugInfo['tests']['authentication']['is_json'] = $decodedResponse !== null;
        $debugInfo['tests']['authentication']['json_error'] = json_last_error_msg();
        
        if ($decodedResponse !== null) {
            $debugInfo['tests']['authentication']['parsed_response'] = $decodedResponse;
            $debugInfo['tests']['authentication']['message'] = 'Response parsed successfully';
        } else {
            // Check if it's HTML
            if (strpos($authResponse, '<html') !== false || strpos($authResponse, '<!DOCTYPE') !== false) {
                $debugInfo['tests']['authentication']['message'] = 'Server returned HTML instead of JSON';
                $debugInfo['tests']['authentication']['response_type'] = 'HTML';
                
                // Try to extract title
                if (preg_match('/<title>(.*?)<\/title>/i', $authResponse, $matches)) {
                    $debugInfo['tests']['authentication']['html_title'] = trim($matches[1]);
                }
            } else {
                $debugInfo['tests']['authentication']['message'] = 'Invalid JSON response';
                $debugInfo['tests']['authentication']['response_type'] = 'Unknown';
            }
        }
    } else {
        $debugInfo['tests']['authentication']['message'] = 'Empty response from server';
    }

    // Test 3: Check if it's a server error
    if ($authHttpCode >= 500) {
        $debugInfo['tests']['server_status'] = [
            'success' => false,
            'message' => 'Angel One servers are experiencing issues (HTTP ' . $authHttpCode . ')',
            'recommendation' => 'Try again later'
        ];
    } elseif ($authHttpCode == 401) {
        $debugInfo['tests']['server_status'] = [
            'success' => false,
            'message' => 'Authentication failed - Invalid credentials',
            'recommendation' => 'Check API Key, Client Code, M-PIN, and TOTP'
        ];
    } elseif ($authHttpCode == 403) {
        $debugInfo['tests']['server_status'] = [
            'success' => false,
            'message' => 'Access forbidden - API access may not be enabled',
            'recommendation' => 'Enable API access in Angel One account'
        ];
    } elseif ($authHttpCode >= 400) {
        $debugInfo['tests']['server_status'] = [
            'success' => false,
            'message' => 'Client error (HTTP ' . $authHttpCode . ')',
            'recommendation' => 'Check request parameters'
        ];
    } else {
        $debugInfo['tests']['server_status'] = [
            'success' => true,
            'message' => 'Server responded normally (HTTP ' . $authHttpCode . ')',
            'recommendation' => 'Check response format'
        ];
    }

} catch (Exception $e) {
    $debugInfo['error'] = $e->getMessage();
}

// Return results
echo json_encode([
    'success' => true,
    'debug_info' => $debugInfo
], JSON_PRETTY_PRINT);

?>
