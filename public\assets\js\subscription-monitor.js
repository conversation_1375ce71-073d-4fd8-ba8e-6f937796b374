/**
 * Subscription Monitor
 * Monitors user subscription status and handles automatic logout when subscription expires
 */

class SubscriptionMonitor {
    constructor() {
        this.checkInterval = 5 * 60 * 1000; // Check every 5 minutes
        this.warningInterval = 24 * 60 * 60 * 1000; // Show warning 24 hours before expiry
        this.intervalId = null;
        this.warningShown = false;
        this.isChecking = false;
        
        this.init();
    }

    init() {
        // Start monitoring
        this.startMonitoring();
        
        // Check immediately on page load
        this.checkSubscriptionStatus();
        
        // Listen for visibility change to check when user returns to tab
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkSubscriptionStatus();
            }
        });
        
        // Check on window focus
        window.addEventListener('focus', () => {
            this.checkSubscriptionStatus();
        });
    }

    startMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            this.checkSubscriptionStatus();
        }, this.checkInterval);
    }

    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    async checkSubscriptionStatus() {
        if (this.isChecking) {
            return; // Prevent multiple simultaneous checks
        }
        
        this.isChecking = true;
        
        try {
            const response = await fetch('/check-subscription-status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                if (!data.authenticated) {
                    // User is not authenticated, redirect to login
                    this.handleLogout('Authentication required. Please log in again.');
                }
                return;
            }

            // Handle subscription status
            this.handleSubscriptionStatus(data);

        } catch (error) {
            console.error('Error checking subscription status:', error);
            // Don't logout on network errors, just log them
        } finally {
            this.isChecking = false;
        }
    }

    handleSubscriptionStatus(data) {
        const { subscription_status, subscription_expired, days_remaining, subscription_end } = data;

        if (subscription_expired) {
            // Subscription has expired, logout immediately
            this.handleExpiredSubscription();
            return;
        }

        if (subscription_status === 'active' && days_remaining !== null) {
            // Check if we should show expiry warning
            if (days_remaining <= 1 && !this.warningShown) {
                this.showExpiryWarning(days_remaining, subscription_end);
            }
        }
    }

    handleExpiredSubscription() {
        this.stopMonitoring();
        
        // Show expiry modal
        this.showExpiryModal();
        
        // Auto-logout after 10 seconds
        setTimeout(() => {
            this.performLogout('Your subscription has expired. Please renew to continue using the service.');
        }, 10000);
    }

    showExpiryWarning(daysRemaining, subscriptionEnd) {
        this.warningShown = true;
        
        const message = daysRemaining === 0 
            ? 'Your subscription expires today!' 
            : `Your subscription expires in ${daysRemaining} day${daysRemaining > 1 ? 's' : ''}!`;

        this.showNotification('warning', message, {
            persistent: true,
            action: {
                text: 'Renew Now',
                callback: () => {
                    // Redirect to subscription/payment page
                    window.location.href = '/subscription';
                }
            }
        });
    }

    showExpiryModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        Subscription Expired
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Your subscription has expired. You will be logged out in <span id="countdown">10</span> seconds.
                        Please renew your subscription to continue using the service.
                    </p>
                    <div class="flex space-x-3 justify-center">
                        <button onclick="window.location.href='/subscription'" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            Renew Now
                        </button>
                        <button onclick="subscriptionMonitor.performLogout('Subscription expired')" 
                                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors">
                            Logout Now
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Countdown timer
        let countdown = 10;
        const countdownElement = modal.querySelector('#countdown');
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    }

    showNotification(type, message, options = {}) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 translate-x-full`;
        
        const iconClass = type === 'warning' ? 'fa-exclamation-triangle text-yellow-500' : 'fa-info-circle text-blue-500';
        
        notification.innerHTML = `
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas ${iconClass} text-xl"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</p>
                        ${options.action ? `
                            <div class="mt-3">
                                <button class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded transition-colors" 
                                        onclick="this.closest('.fixed').remove(); (${options.action.callback})()">
                                    ${options.action.text}
                                </button>
                            </div>
                        ` : ''}
                    </div>
                    ${!options.persistent ? `
                        <div class="ml-4 flex-shrink-0 flex">
                            <button class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" 
                                    onclick="this.closest('.fixed').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto-remove after 10 seconds if not persistent
        if (!options.persistent) {
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 10000);
        }
    }

    handleLogout(message) {
        this.performLogout(message);
    }

    performLogout(message) {
        this.stopMonitoring();
        
        // Clear any stored session data
        if (typeof(Storage) !== "undefined") {
            localStorage.clear();
            sessionStorage.clear();
        }
        
        // Redirect to login with message
        const loginUrl = new URL('/login', window.location.origin);
        if (message) {
            loginUrl.searchParams.set('message', message);
        }
        
        window.location.href = loginUrl.toString();
    }
}

// Initialize subscription monitor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on authenticated pages (not login, register, etc.)
    const publicPages = ['/login', '/register', '/forgot-password', '/reset-password'];
    const currentPath = window.location.pathname;
    
    const isPublicPage = publicPages.some(page => currentPath.includes(page));
    
    if (!isPublicPage) {
        window.subscriptionMonitor = new SubscriptionMonitor();
    }
});
