<style>
    .tab-button {
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        outline: none;
        padding: 14px 24px;
        font-weight: 500;
        border-radius: 0;
        margin: 0;
        color: #6b7280;
        border-bottom: 2px solid transparent;
        white-space: nowrap;
    }

    .tab-button::after {
        content: '';
        position: absolute;
        background: transparent;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%) scale(0);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .tab-button:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.05);
    }

    .tab-button.active {
        color: #3b82f6;
        font-weight: 600;
        border-bottom: 2px solid #3b82f6;
    }

    .tab-button.active::after {
        background: #3b82f6;
        transform: translateX(-50%) scale(1);
    }

    .dark .tab-button {
        color: #9ca3af;
    }

    .dark .tab-button:hover {
        color: #60a5fa;
        background-color: rgba(96, 165, 250, 0.05);
    }

    .dark .tab-button.active {
        color: #60a5fa;
        border-bottom-color: #60a5fa;
    }

    .dark .tab-button.active::after {
        background: #60a5fa;
    }

    @media (max-width: 640px) {
        .tab-button {
            padding: 12px 16px;
            font-size: 0.875rem;
            flex: 1;
            text-align: center;
        }

        .inline-flex {
            display: flex;
            width: 100%;
        }
    }

    .performance-card {
        transition: all 0.3s ease;
    }

    .performance-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .dark .performance-card:hover {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }

    .positive {
        color: #10B981;
    }

    .dark .positive {
        color: #34D399;
    }

    .negative {
        color: #EF4444;
    }

    .dark .negative {
        color: #F87171;
    }

    .neutral {
        color: #6B7280;
    }

    .dark .neutral {
        color: #9CA3AF;
    }

    .stat-card {
        border-radius: 12px;
        border: 1px solid rgba(209, 213, 219, 0.5);
    }

    .dark .stat-card {
        border-color: rgba(75, 85, 99, 0.5);
    }

    .psychology-point {
        @apply flex items-start space-x-2 text-sm;
    }

    .psychology-point i {
        @apply mt-0.5;
    }
</style>

<div class="container mx-auto px-4 py-8">

    <div class="flex justify-between items-center mb-6">
        <div>

        </div>
        <div class="flex space-x-3">
            <div class="relative">
                <select id="marketTypeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">Indian</option>
                    <option value="2">Forex</option>
                    <option value="3">Crypto</option>
                </select>
            </div>
            <div class="relative">
                
                <select id="rangeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">Last 30 Days</option>
                    <option value="3">Last 90 Days</option>
                    <option value="12">Year to Date</option>
                    <!-- <option value="">All Time</option> -->
                </select>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="mb-8 px-4">
        <div class="inline-flex sm:space-x-1 border-b border-gray-100 dark:border-gray-700">
            <button class="tab-button active" data-tab="performance">
                <i class="fas fa-chart-line text-sm sm:mr-2"></i>
                <span class="hidden sm:inline">Performance</span>
            </button>
            <button class="tab-button" data-tab="psychology">
                <i class="fas fa-brain text-sm sm:mr-2"></i>
                <span class="hidden sm:inline">Psychology</span>
            </button>
            <button class="tab-button" data-tab="risk">
                <i class="fas fa-shield-alt text-sm sm:mr-2"></i>
                <span class="hidden sm:inline">Risk</span>
            </button>
            <button class="tab-button" data-tab="journal">
                <i class="fas fa-book text-sm sm:mr-2"></i>
                <span class="hidden sm:inline">Journal</span>
            </button>
        </div>
    </div>

    <!-- Content sections -->
    <div class="tab-content grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-tab-content="performance">
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Trade Performance</p>
                    <div class="flex items-center mt-1 mb-3">
                        <div class="text-3xl font-bold positive">0</div>
                        <div class="mx-2 text-lg">/</div>
                        <div class="text-3xl font-bold negative">0</div>
                        <div class="mx-2 text-lg">/</div>
                        <div class="text-3xl font-bold neutral">0</div>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Win / Loss / Break Even</p>
                </div>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <i class="fas fa-trophy text-blue-500 dark:text-blue-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-2">
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Avg Win</p>
                    <p class="font-medium positive">₹0.00</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Avg Loss</p>
                    <p class="font-medium negative">₹0.00</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Win Rate</p>
                    <p class="font-medium">0.00%</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Expectancy</p>
                    <p class="font-medium">₹0.00</p>
                </div>
            </div>
        </div>

        <!-- Daily Performance -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Daily Performance</p>
                    <div class="flex items-center mt-1 mb-3">
                        <div id="dailyPositive" class="text-3xl font-bold positive">0</div>
                        <div class="mx-2 text-lg">/</div>
                        <div id="dailyNegative" class="text-3xl font-bold negative">0</div>
                        <div class="mx-2 text-lg">/</div>
                        <div id="dailyBreakeven" class="text-3xl font-bold neutral">0</div>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Win / Loss / Break Even Days</p>
                </div>
                <div class="bg-green-100 dark:bg-green-900/50 p-2 rounded-lg">
                    <i class="fas fa-calendar-alt text-green-500 dark:text-green-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-2">
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Best Day</p>
                    <p class="font-medium positive">₹0.00</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Worst Day</p>
                    <p class="font-medium negative">₹0.00</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Avg Win Day</p>
                    <p class="font-medium">₹0.00</p>
                </div>
                <div class="stat-card p-2">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Avg Loss Day</p>
                    <p class="font-medium">₹0.00</p>
                </div>
            </div>
        </div>
        <!-- aisa comment daaal section -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Trade Execution</h3>
                <div class="bg-purple-100 dark:bg-purple-900/50 p-2 rounded-lg">
                    <i class="fas fa-exchange-alt text-purple-500 dark:text-purple-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Total Trades</span>
                    <span class="font-medium" id="totalTrades">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg Capital Used</span>
                    <span class="font-medium" id="avgCapitalUsed">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Most Profitable Strategy</span>
                    <span class="font-medium" id="mostProfitableStrategy">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Consecutive Wins</span>
                    <span class="font-medium positive" id="consecutiveWins">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Consecutive Losses</span>
                    <span class="font-medium negative" id="consecutiveLosses">-</span>
                </div>
            </div>
        </div>


        <!-- Time Metrics -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Time Metrics</h3>
                <div class="bg-yellow-100 dark:bg-yellow-900/50 p-2 rounded-lg">
                    <i class="fas fa-clock text-yellow-500 dark:text-yellow-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Trading Days</span>
                    <span class="font-medium text-default trading-days">0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Consecutive Win Days</span>
                    <span class="font-medium text-default positive consec-win">0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Consecutive Loss Days</span>
                    <span class="font-medium text-default negative consec-loss">0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Most Profitable Day</span>
                    <span class="font-medium text-default positive most-day">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Least Profitable Day</span>
                    <span class="font-medium text-default negative least-day">-</span>
                </div>
            </div>
        </div>



        <!-- Setup Effectiveness -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Setup Effectiveness</h3>
                <div class="bg-green-100 dark:bg-green-900/50 p-2 rounded-lg">
                    <i class="fas fa-chart-line text-green-500 dark:text-green-400"></i>
                </div>
            </div>
            <div class="setup-effectiveness-container space-y-3">
                <!-- Items will be injected here via jQuery -->
            </div>
        </div>

        <!-- Exit Types -->
        <div class="hidden performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Exit Types</h3>
                <div class="bg-orange-100 dark:bg-orange-900/50 p-2 rounded-lg">
                    <i class="fas fa-sign-out-alt text-orange-500 dark:text-orange-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Manual Exits</span>
                    <span class="font-medium">54%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Stop Hit</span>
                    <span class="font-medium">26%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Target Hit</span>
                    <span class="font-medium">18%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Trailing Stop</span>
                    <span class="font-medium">2%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg Exit Efficiency</span>
                    <span class="font-medium">6.2/10</span>
                </div>
            </div>
        </div>

        <!-- Symbol Frequency & Performance -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Symbol Frequency</h3>
                <div class="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-lg">
                    <i class="fas fa-chart-pie text-indigo-500 dark:text-indigo-400"></i>
                </div>
            </div>
            <div class="symbol-frequency-container space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Most Traded Symbol</span>
                    <span class="font-medium">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Most Profitable Symbol</span>
                    <span class="font-medium positive">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Least Profitable Symbol</span>
                    <span class="font-medium negative">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Highest Win Rate</span>
                    <span class="font-medium positive">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Lowest Win Rate</span>
                    <span class="font-medium negative">--</span>
                </div>
            </div>
        </div>

        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Capital Usage</h3>
                <div class="bg-green-100 dark:bg-green-900/50 p-2 rounded-lg">
                    <i class="fas fa-money-bill-wave text-green-500 dark:text-green-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Maximum</span>
                    <span class="font-medium" id="cu-max">$0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Minimum</span>
                    <span class="font-medium" id="cu-min">$0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Average</span>
                    <span class="font-medium" id="cu-avg">$0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">P&L at Max Capital</span>
                    <span class="font-medium" id="cu-max-pnl">-$0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">P&L at Min Capital</span>
                    <span class="font-medium" id="cu-min-pnl">+$0</span>
                </div>
            </div>
        </div>

        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Quantity Analysis</h3>
                <div class="bg-yellow-100 dark:bg-yellow-900/50 p-2 rounded-lg">
                    <i class="fas fa-boxes text-yellow-500 dark:text-yellow-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Maximum</span>
                    <span class="font-medium" id="qa-max-qty">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Minimum</span>
                    <span class="font-medium" id="qa-min-qty">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Average</span>
                    <span class="font-medium" id="qa-avg-qty">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">P&L at Max Qty</span>
                    <span class="font-medium" id="qa-max-pnl">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">P&L at Min Qty</span>
                    <span class="font-medium" id="qa-min-pnl">-</span>
                </div>
            </div>
        </div>

        <!-- Avg R:R Card -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Weekday Avg R:R</h3>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <i class="fas fa-chart-line text-blue-500 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Monday</span>
                    <span class="font-medium text-default rr-monday">--R</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tuesday</span>
                    <span class="font-medium text-default rr-tuesday">--R</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Wednesday</span>
                    <span class="font-medium text-default rr-wednesday">--R</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Thursday</span>
                    <span class="font-medium text-default rr-thursday">--R</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Friday</span>
                    <span class="font-medium text-default rr-friday">--R</span>
                </div>
            </div>
        </div>

        <!-- Win Rate Card -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Weekday Win Rate</h3>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <i class="fas fa-calendar-week text-blue-500 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Monday</span>
                    <span class="font-medium win-monday">--%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tuesday</span>
                    <span class="font-medium win-tuesday">--%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Wednesday</span>
                    <span class="font-medium win-wednesday">--%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Thursday</span>
                    <span class="font-medium win-thursday">--%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Friday</span>
                    <span class="font-medium win-friday">--%</span>
                </div>
            </div>
        </div>



        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Daily Trade Activity</h3>
                <div class="bg-purple-100 dark:bg-purple-900/50 p-2 rounded-lg">
                    <i class="fas fa-clock text-purple-500 dark:text-purple-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg Trades Per Day</span>
                    <span class="font-medium" id="avgTradesPerDay">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Max Trades in a Day</span>
                    <span class="font-medium" id="maxTradesInDay">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Days With Only 1 Trade</span>
                    <span class="font-medium" id="oneTradeDays">-</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Overtrading Days (&gt;7 trades)</span>
                    <span class="font-medium negative" id="overtradingDays">-</span>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-content grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 hidden" data-tab-content="psychology">

        <!-- Psychology Insights -->
        <div class="hidden performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Psychology Insights</h3>
                <div class="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-lg">
                    <i class="fas fa-brain text-indigo-500 dark:text-indigo-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Mistakes</span>
                    <span class="font-medium">32%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Confidence</span>
                    <span class="font-medium">8/10</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Impulsivity</span>
                    <span class="font-medium">45%</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Focus</span>
                    <span class="font-medium">7/10</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Discipline</span>
                    <span class="font-medium">6/10</span>
                </div>
            </div>
        </div>

        <!-- Emotional State During Trades -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Emotional State</h3>
                <div class="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-lg">
                    <i class="fas fa-heart text-pink-500 dark:text-pink-400"></i>
                </div>
            </div>
            <div id="emotionsContainer" class="space-y-3"></div>
        </div>

        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Avg R:R by Emotional State</h3>
                <div class="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-lg">
                    <i class="fas fa-heartbeat text-pink-500 dark:text-pink-400"></i>
                </div>
            </div>
            <div class="avg-rr-by-emotion-container space-y-3">
                <!-- Content will be injected here -->
            </div>
        </div>
    </div>
    <div class="tab-content grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 hidden" data-tab-content="risk">
        <!-- Risk Management -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Risk Management</h3>
                <div class="bg-red-100 dark:bg-red-900/50 p-2 rounded-lg">
                    <i class="fas fa-shield-alt text-red-500 dark:text-red-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Planned Risk Reward Ratio</span>
                    <span class="font-medium positive planned-r">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Realized Risk Reward Ratio</span>
                    <span class="font-medium negative realized-r">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg Loss</span>
                    <span class="font-medium avg-loss">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Max Drawdown</span>
                    <span class="font-medium max-drawdown">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Expectancy ($ per R)</span>
                    <span class="font-medium expectancy">--</span>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-content grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 hidden" data-tab-content="journal">
        <!-- Trading Habits -->
        <div class="hidden performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Trading Habits</h3>
                <div class="bg-teal-100 dark:bg-teal-900/50 p-2 rounded-lg">
                    <i class="fas fa-tasks text-teal-500 dark:text-teal-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Journal Completion</span>
                    <span class="font-medium">85% of trades</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg Preparation</span>
                    <span class="font-medium">32 mins</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Plan Deviations</span>
                    <span class="font-medium">14% of trades</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Break Frequency</span>
                    <span class="font-medium">Every 1h 40m</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Post-Trade Review</span>
                    <span class="font-medium">68% of trades</span>
                </div>
            </div>
        </div>

        <!-- Target Outcome Metrics -->
        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Target Outcome Metrics</h3>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <i class="fas fa-bullseye text-blue-500 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Target Achieved</span>
                    <span class="font-medium positive target-achieved">0% of trades</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Target Missed</span>
                    <span class="font-medium negative target-missed">0% of trades</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Stopped Before Target</span>
                    <span class="font-medium negative stopped-before-target">0% of trades</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg R on Achieved Targets</span>
                    <span class="font-medium positive avg-r-achieved">+0.0R</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Avg R on Missed Targets</span>
                    <span class="font-medium negative avg-r-missed">-0.0R</span>
                </div>
            </div>
        </div>

        <div class="performance-card bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold">Trading Outcomes</h3>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <i class="fas fa-chart-pie text-blue-500 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="trading-outcomes-container space-y-3">
                <!-- JS will inject outcomes here -->
            </div>
        </div>
    </div>

</div>