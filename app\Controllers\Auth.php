<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\KiteBrokerModel;
use Google_Client;
use Google_Service_Oauth2;

// Load all exception classes manually
require_once APPPATH . 'Libraries/KiteConnect/Exception/KiteException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/TokenException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/PermissionException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/InputException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/OrderException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/NetworkException.php';
require_once APPPATH . 'Libraries/KiteConnect/Exception/DataException.php';

// Then load KiteConnect main class
require_once APPPATH . 'Libraries/KiteConnect/KiteConnect.php';

helper('cookie');

class Auth extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->kitemodel = new KiteBrokerModel();
    }

    public function googleLogin()
    {
        $client = new Google_Client();
        $client->setClientId('564203561371-sj66237nskju41k964p5i2c1u8i86jpq.apps.googleusercontent.com');
        $client->setClientSecret('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');
        $client->setRedirectUri(base_url('googleCallback'));
        $client->addScope('email');
        $client->addScope('profile');

        return redirect()->to($client->createAuthUrl());
    }

    public function googleCallback()
    {
        $db = \Config\Database::connect();

        $client = new \Google_Client();
        $client->setClientId('564203561371-sj66237nskju41k964p5i2c1u8i86jpq.apps.googleusercontent.com');
        $client->setClientSecret('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');
        $client->setRedirectUri(base_url('googleCallback'));

        if ($this->request->getVar('code')) {
            $token = $client->fetchAccessTokenWithAuthCode($this->request->getVar('code'));
            $client->setAccessToken($token['access_token']);

            $googleService = new \Google_Service_Oauth2($client);
            $userData = $googleService->userinfo->get();

            $userModel = new \App\Models\UserModel();
            $user = $userModel->where('email', $userData->email)->first();

            $isNewUser = false;

            if ($user) {
                $userId = $user['id'];
            } else {
                // New user
                $isNewUser = true;

                // Check if 'payment_id' cookie exists
                if (!$this->request->getCookie('osrgdthfudtrshf')) {
                    return redirect()->to(base_url() . '#pricing')->with('error', 'Payment not completed. Please complete payment to proceed.');
                }
                $payment_id = $this->request->getCookie('osrgdthfudtrshf');

                $paymentDetails = $db->query("SELECT * FROM transactions WHERE payment_id = '$payment_id' ")->getRowArray();

                $period = $paymentDetails['period'];

                $sub_start = date('Y-m-d H:i:s');

                if ($period == 'monthly') {
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
                } elseif ($period == 'annual') {
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 year'));
                } else {
                    // Default/fallback if period is not recognized
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
                }

                $name = $userData->name;

                $referralCode = $this->generateReferralCode($name);

                // Insert user
                $userModel->insert([
                    'full_name' => $userData->name,
                    'email' => $userData->email,
                    'refer_code' => $referralCode,
                    'google_id' => $userData->id,
                    'profile' => $userData->picture,
                    'sub_start' => $sub_start,
                    'sub_end' => $sub_end,
                    'payment_id' => $payment_id,
                    'is_verified' => 1
                ]);
                $userId = $userModel->insertID();
            }
            $this->response->deleteCookie('osrgdthfudtrshf');

            // Encrypt user ID
            $encryptedId = $this->encrypt_cookie_value($userId);

            if ($encryptedId !== false) {
                $response = service('response');

                $response->setCookie([
                    'name' => 'user_session',
                    'value' => $encryptedId,
                    'expire' => 60 * 60 * 24 * 30, // 30 days
                    'secure' => false, // set true for HTTPS
                    'httponly' => true,
                    'path' => '/',
                    'samesite' => 'Lax',
                ]);

                return $response->redirect(base_url('dashboard'));
            }

            // Fallback if encryption fails
            return redirect()->to('/')->with('error', 'Failed to login, try again.');
        }

        return redirect()->to('/');
    }

    private function generateReferralCode($name)
    {
        // Clean and lowercase the name
        $cleanName = strtolower(preg_replace('/[^a-zA-Z]/', '', $name));

        // Pad the name with "x" if less than 4 characters
        $prefix = str_pad(substr($cleanName, 0, 4), 4, 'x');

        // Generate a random 4-digit number
        $randomNumber = rand(1000, 9999);

        // Combine and return uppercase code
        return strtoupper($prefix . $randomNumber); // e.g., ALEx5732 or JOxX2183
    }

    public function MyProfile()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'My Profile';
        $data['active'] = 'dashboard';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'profile';
        $data['main_content'] = 'pages/profile';

        return view('includes/template', $data);
    }

    public function updateProfile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $name = $this->request->getPost('name');
        $password = $this->request->getPost('password');
        $confirmPassword = $this->request->getPost('confirm_password');

        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[2]',
            'password' => 'permit_empty|min_length[8]',
            'confirm_password' => 'matches[password]',
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $updateData = [
            'full_name' => $name
        ];

        if (!empty($password)) {
            $updateData['password'] = password_hash($password, PASSWORD_BCRYPT);
        }

        $this->usermodel->update($userId, $updateData);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
    }

    public function saveBrokerDetails()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type'
            ]);
        }

        log_message('debug', 'Received POST data: ' . json_encode($this->request->getPost()));

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $broker = trim($this->request->getPost('broker'));

        if ($broker === 'angel_one') {
            $apiKey = trim($this->request->getPost('api_key'));
            $clientCode = trim($this->request->getPost('client_code'));
            $mpin = trim($this->request->getPost('mpin'));
            $totp = trim($this->request->getPost('totp'));
            $totpKey = trim($this->request->getPost('totp_key'));

            if (empty($apiKey) || empty($clientCode) || empty($mpin) || empty($totp)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'API Key, Client Code, M-PIN, and TOTP are required for Angel One.'
                ]);
            }

            return $this->handleAngelOneAuth($userId, $apiKey, $clientCode, $mpin, $totp, $totpKey);

        } else if ($broker === 'kite') {
            // Zerodha Kite specific handling
            $apiKey = trim($this->request->getPost('kiteApiKey'));
            $apiSecret = trim($this->request->getPost('kiteSecretKey'));

            if (empty($apiKey) || empty($apiSecret)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'API Key and API Secret are required for Zerodha.'
                ]);
            }

            // Save in separate table: `kite_brokers`
            $kiteModel = new \App\Models\KiteBrokerModel();

            $data = [
                'user_id' => $userId,
                'api_key' => $apiKey,
                'api_secret' => $apiSecret,
                'created_at' => date('Y-m-d H:i:s')
            ];

            try {
                // Upsert: Update if exists, else insert
                $existing = $kiteModel->where('user_id', $userId)->first();
                if ($existing) {
                    $kiteModel->update($existing['id'], $data);
                } else {
                    $kiteModel->insert($data);
                }

                $this->usermodel->update($userId, ['broker' => 'kite']);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Zerodha (Kite) broker details saved successfully.'
                ]);
            } catch (\Exception $e) {
                log_message('error', 'Failed to save Kite broker: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error saving Kite broker details.'
                ]);
            }

        } else {
            // Generic broker (like Dhan)
            $clientId = trim($this->request->getPost('clientID'));
            $accessToken = trim($this->request->getPost('accessToken'));

            if (empty($clientId) || empty($accessToken)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Client ID and Access Token are required.'
                ]);
            }

            $data = [
                'broker' => $broker,
                'client_id' => $clientId,
                'connected_on' => date('Y-m-d H:i:s'),
                'access_token' => $accessToken
            ];

            try {
                if (!$this->usermodel->update($userId, $data)) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Failed to update broker details.',
                        'errors' => $this->usermodel->errors()
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'Broker update failed for user ' . $userId . ': ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'An unexpected error occurred while saving broker details.'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Broker configured successfully.'
            ]);
        }
    }


    public function fetchConnectedBroker()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $details = $this->usermodel->find($userId);

        // Check for any connected broker (legacy or Angel One)
        $hasBroker = false;
        $brokerData = [];

        if (!empty($details['broker']) && !empty($details['client_id'])) {
            // Legacy broker (Dhan, etc.)
            $hasBroker = true;
            $brokerData = [
                'broker' => $details['broker'],
                'client_id' => $details['client_id'],
                'access_token' => $details['access_token'],
                'connected_on' => $details['connected_on'] ?? date('Y-m-d H:i:s', strtotime($details['updated_at'] ?? $details['created_at'] ?? 'now'))
            ];
        } elseif (!empty($details['angel_client_id'])) {
            // Angel One broker
            $hasBroker = true;
            $brokerData = [
                'broker' => 'angel_one',
                'client_id' => $details['angel_client_id'],
                'access_token' => 'encrypted',
                'connected_on' => $details['angel_connected_on'] ?? date('Y-m-d H:i:s', strtotime($details['updated_at'] ?? $details['created_at'] ?? 'now'))
            ];
        }

        if (!$hasBroker) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No broker connected'
            ]);
        }

        // Return broker data
        return $this->response->setJSON([
            'success' => true,
            'data' => array_merge($brokerData, [
                'sub_start' => $details['sub_start'] ?? null,
                'sub_end' => $details['sub_end'] ?? null
            ])
        ]);
    }

    public function deleteBroker()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Clear all broker-related fields
        $data = [
            // Legacy broker fields
            'broker' => null,
            'client_id' => null,
            'connected_on' => null,
            'access_token' => null,
            // Angel One fields
            'angel_client_id' => null,
            'angel_api_key' => null,
            'angel_access_token' => null,
            'angel_refresh_token' => null,
            'angel_feed_token' => null,
            'angel_connected_on' => null
        ];

        $updated = $this->usermodel->update($userId, $data);

        if ($updated) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Broker connection removed successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to remove broker connection'
            ]);
        }
    }

    public function syncTrades()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session')); // or wherever you store it

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $userDetails = $this->usermodel->find($userId);

        $broker = $userDetails['broker'];

        if ($broker == null) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You have not connected any broker yet!',
            ]);
        } elseif ($broker == 'dhan') {

            $scriptPath = '/home/<USER>/htdocs/tradediary.in/python/dhan_connect.py';
            $pythonPath = '/home/<USER>/htdocs/tradediary.in/python/venv/bin/python';

            $command = escapeshellcmd("$pythonPath $scriptPath $userId");
            $output = shell_exec($command);

            $decoded = json_decode($output, true);

            if (!is_array($decoded)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid response from broker script.',
                ]);
            }

            if (!isset($decoded['data']) || empty($decoded['data'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No new trades found to sync.',
                ]);
            }

            if ($this->saveDhanData($decoded, $userId)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Trades have been fetched and saved successfully.',
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save trades. Please verify the broker details added or update the details.',
                ]);
            }
        } elseif ($broker == 'angel_one') {
            // Handle Angel One trade sync
            return $this->syncAngelOneTrades($userId);
        } elseif ($broker == 'kite') {
            // Handle Angel One trade sync
            return $this->syncKiteTrades($userId);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request!',
            ]);
        }
    }

    public function saveDhanData($orders, $userId)
    {
        $db = \Config\Database::connect();
        if (!isset($orders['status']) || $orders['status'] !== 'success' || !isset($orders['data']) || empty($orders['data'])) {
            return false;
        }

        $builder = $db->table('trades');

        // Filter only TRADED orders
        $tradedOrders = array_filter($orders['data'], fn($o) => $o['orderStatus'] === 'TRADED');

        // Group by tradingSymbol and transactionType (buy/sell)
        $groupedOrders = [];
        foreach ($tradedOrders as $order) {
            $symbol = $order['tradingSymbol'];
            $side = strtolower($order['transactionType']); // buy or sell
            $groupedOrders[$symbol][$side][] = $order;
        }

        foreach ($groupedOrders as $symbol => $sides) {
            $buyOrders = $sides['buy'] ?? [];
            $sellOrders = $sides['sell'] ?? [];

            $minCount = min(count($buyOrders), count($sellOrders));

            for ($i = 0; $i < $minCount; $i++) {
                $buy = $buyOrders[$i];
                $sell = $sellOrders[$i];

                $quantity = min($buy['filledQty'], $sell['filledQty']);
                if ($quantity == 0)
                    continue;

                $entryPrice = $buy['averageTradedPrice'];
                $exitPrice = $sell['averageTradedPrice'];
                $entryAmount = $entryPrice * $quantity;
                $pnlAmount = ($exitPrice - $entryPrice) * $quantity;
                $pnlPercent = $entryAmount > 0 ? round(($pnlAmount / $entryAmount) * 100, 2) : 0;

                $datetime = date('Y-m-d', strtotime($sell['exchangeTime']));
                $orderId = $sell['orderId']; // use SELL order ID as unique identifier

                // Avoid duplicates using unique broker_order_id
                $exists = $builder->where('broker_order_id', $orderId)->countAllResults();
                if ($exists > 0)
                    continue;

                // Insert into trades table
                $builder->insert([
                    'user_id' => $userId,
                    'symbol' => $symbol,
                    'datetime' => $datetime,
                    'entry_price' => $entryPrice,
                    'entry_quantity' => $quantity,
                    'entry_amount' => $entryAmount,
                    'exit_price' => $exitPrice,
                    'pnl_amount' => $pnlAmount,
                    'pnl_percent' => $pnlPercent,
                    'trade_type' => 1,
                    'broker' => 'dhan',
                    'broker_order_id' => $orderId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            }
        }

        return true;
    }

    /**
     * Handle Angel One authentication
     */
    private function handleAngelOneAuth($userId, $apiKey, $clientCode, $mpin, $totp, $totpKey = null)
    {
        try {
            // Load Angel One API library
            $angelApi = new \App\Libraries\AngelOneApi($apiKey, $clientCode, $mpin, $totp);

            // Attempt to generate session
            $authResult = $angelApi->generateSession();

            if (!$authResult['success']) {
                $errorMessage = $authResult['message'] ?? 'Authentication failed';

                // Provide more user-friendly error messages
                if (strpos($errorMessage, 'Invalid JSON') !== false) {
                    $errorMessage = 'Angel One server returned an invalid response. Please try again later.';
                } elseif (strpos($errorMessage, 'Network error') !== false) {
                    $errorMessage = 'Unable to connect to Angel One servers. Please check your internet connection.';
                } elseif (strpos($errorMessage, 'Server error') !== false) {
                    $errorMessage = 'Angel One servers are currently unavailable. Please try again later.';
                } elseif (isset($authResult['http_code']) && $authResult['http_code'] >= 500) {
                    $errorMessage = 'Angel One servers are experiencing issues. Please try again later.';
                } elseif (isset($authResult['http_code']) && $authResult['http_code'] == 401) {
                    $errorMessage = 'Invalid credentials. Please check your API Key, Client Code, M-PIN, and TOTP.';
                } elseif (isset($authResult['http_code']) && $authResult['http_code'] == 403) {
                    $errorMessage = 'Access denied. Please ensure your API access is enabled in Angel One account.';
                } elseif (strpos(strtolower($errorMessage), 'totp') !== false || strpos(strtolower($errorMessage), 'invalid') !== false) {
                    $errorMessage = 'Invalid TOTP code. Please ensure your TOTP secret key is correct and generate a fresh code.';
                }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Angel One authentication failed: ' . $errorMessage
                ]);
            }

            // Encrypt sensitive tokens and API key
            $encryptedApiKey = $this->encrypt_data($apiKey);
            $encryptedMpin = $this->encrypt_data($mpin);
            $encryptedAccessToken = $this->encrypt_data($angelApi->getAccessToken());
            $encryptedRefreshToken = $this->encrypt_data($angelApi->getRefreshToken());
            $encryptedFeedToken = $this->encrypt_data($angelApi->getFeedToken());

            // Encrypt TOTP key if provided
            $encryptedTotpKey = null;
            if (!empty($totpKey)) {
                $encryptedTotpKey = $this->encrypt_data($totpKey);
            }

            // Prepare data for database
            $data = [
                'broker' => 'angel_one',
                'angel_client_id' => $clientCode,
                'angel_mpin' => $encryptedMpin,
                'angel_api_key' => $encryptedApiKey,
                'angel_access_token' => $encryptedAccessToken,
                'angel_refresh_token' => $encryptedRefreshToken,
                'angel_feed_token' => $encryptedFeedToken,
                'angel_totp_key' => $encryptedTotpKey,
                'angel_connected_on' => date('Y-m-d H:i:s')
            ];

            // Update user record
            if (!$this->usermodel->update($userId, $data)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save Angel One connection details.',
                    'errors' => $this->usermodel->errors()
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Angel One connected successfully! You can now sync your trades.'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Angel One authentication failed for user ' . $userId . ': ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Angel One connection failed. Please check your credentials and try again.'
            ]);
        }
    }

    /**
     * Encrypt sensitive data
     */
    private function encrypt_data($data)
    {
        $key = getenv('encryption.key') ?: 'your-secret-key-here';
        return base64_encode(openssl_encrypt($data, 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16)));
    }

    /**
     * Decrypt sensitive data
     */
    private function decrypt_data($encryptedData)
    {
        $key = getenv('encryption.key') ?: 'your-secret-key-here';
        return openssl_decrypt(base64_decode($encryptedData), 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16));
    }

    /**
     * Sync Angel One trades
     */
    private function syncAngelOneTrades($userId)
    {
        try {
            $user = $this->usermodel->find($userId);

            if (!$user || !$user['angel_client_id'] || !$user['angel_access_token'] || !$user['angel_api_key']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Angel One connection not found. Please reconnect your Angel One account.'
                ]);
            }

            // Decrypt credentials
            $accessToken = $this->decrypt_data($user['angel_access_token']);
            $apiKey = $this->decrypt_data($user['angel_api_key']);

            $angelApi = new \App\Libraries\AngelOneApi();
            $angelApi->setAccessToken($accessToken);
            $angelApi->setClientCode($user['angel_client_id']);
            $angelApi->setApiKey($apiKey);

            // Attempt to fetch trade book
            $tradeBookResponse = $angelApi->getTradeBook();

            // Handle expired token (AG8001)
            if (isset($tradeBookResponse['errorCode']) && $tradeBookResponse['errorCode'] === 'AG8001') {
                $newAccessToken = $this->refreshAngelOneTokenViaAuth($user);

                if (!$newAccessToken) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Your Angel One token expired and could not be refreshed. Please reconnect your broker account.'
                    ]);
                }

                // Retry with new token
                $angelApi->setAccessToken($newAccessToken);
                $tradeBookResponse = $angelApi->getTradeBook();
            }

            // echo '<pre>';print_r($tradeBookResponse);exit;

            // Check if tradeBookResponse is valid
            if (!$tradeBookResponse || !isset($tradeBookResponse['data'])) {
                $errorMessage = 'No new trades found to sync.';
                // if (isset($tradeBookResponse['message'])) {
                //     $errorMessage .= ' Error: ' . $tradeBookResponse['message'];
                // }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => $errorMessage
                ]);
            }

            $trades = $tradeBookResponse['data'];

            if (empty($trades)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No new trades found to sync.'
                ]);
            }

            // Save and process trades
            $savedCount = $this->saveAngelOneTrades($trades, $userId, $user['angel_client_id']);
            $processedCount = $this->processAngelOneTradesForAnalysis($trades, $userId);

            $message = "Successfully synced {$savedCount} raw trades from Angel One.";
            $message .= $processedCount > 0
                ? " {$processedCount} completed trades processed for analysis."
                : " No completed trade pairs found for processing.";

            return $this->response->setJSON([
                'success' => true,
                'message' => $message,
                'raw_trades_count' => $savedCount,
                'processed_trades_count' => $processedCount
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Angel One trade sync failed for user ' . $userId . ': ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to sync trades from Angel One. Please try again later.'
            ]);
        }
    }

    private function refreshAngelOneTokenViaAuth($user)
    {
        try {
            $apiKey = $this->decrypt_data($user['angel_api_key']);
            $clientCode = $user['angel_client_id'];
            $mpin = $this->decrypt_data($user['angel_mpin']); // Must be stored encrypted
            $totpKey = $this->decrypt_data($user['angel_totp_key']);
            $totp = $this->generateTOTP($totpKey);

            $angelApi = new \App\Libraries\AngelOneApi($apiKey, $clientCode, $mpin, $totp);
            $authResult = $angelApi->generateSession();

            if (!$authResult['success']) {
                log_message('error', 'Angel token auto-refresh failed: ' . json_encode($authResult));
                return false;
            }

            $updatedData = [
                'angel_access_token' => $this->encrypt_data($angelApi->getAccessToken()),
                'angel_refresh_token' => $this->encrypt_data($angelApi->getRefreshToken()),
                'angel_feed_token' => $this->encrypt_data($angelApi->getFeedToken()),
            ];

            $this->usermodel->update($user['id'], $updatedData);

            return $angelApi->getAccessToken();
        } catch (\Exception $e) {
            log_message('error', 'Angel One token refresh failed: ' . $e->getMessage());
            return false;
        }
    }

    private function generateTOTP($secret, $timeStep = 30)
    {
        $url = "https://totpapi.com/api/" . urlencode($secret);

        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // optional timeout

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            curl_close($ch);
            return false; // handle error appropriately
        }

        curl_close($ch);

        $data = json_decode($response, true);

        // return 877788;

        return $data['data']['token'] ?? false;
    }


    /**
     * Save Angel One trades to database
     */
    private function saveAngelOneTrades($trades, $userId, $clientId)
    {
        $db = \Config\Database::connect();
        $angelTradesBuilder = $db->table('angel_trades');
        $tradesBuilder = $db->table('trades');

        $savedCount = 0;

        foreach ($trades as $trade) {
            try {
                // Check if trade already exists (handle different possible key names)
                $exchangeTradeId = $trade['exchangeTradeId'] ?? $trade['exchange_trade_id'] ?? $trade['tradeId'] ?? $trade['fillId'] ?? $trade['fillid'] ?? null;

                if (!$exchangeTradeId) {
                    log_message('error', 'Angel One trade missing exchange trade ID: ' . json_encode($trade));
                    continue;
                }

                $existingTrade = $angelTradesBuilder
                    ->where('exchange_trade_id', $exchangeTradeId)
                    ->get()
                    ->getRowArray();

                if ($existingTrade) {
                    continue; // Skip if already exists
                }

                // Prepare Angel One trade data with correct field mapping
                $angelTradeData = [
                    'user_id' => $userId,
                    'angel_client_id' => $clientId,
                    'order_id' => $trade['orderid'] ?? $trade['orderId'] ?? $trade['order_id'] ?? null,
                    'exchange_order_id' => $trade['exchangeOrderId'] ?? $trade['exchange_order_id'] ?? null,
                    'exchange_trade_id' => $exchangeTradeId,
                    'transaction_type' => $trade['transactiontype'] ?? $trade['transactionType'] ?? $trade['transaction_type'] ?? 'UNKNOWN',
                    'exchange_segment' => $trade['exchange'] ?? $trade['exchangeSegment'] ?? $trade['exchange_segment'] ?? 'UNKNOWN',
                    'product_type' => $trade['producttype'] ?? $trade['productType'] ?? $trade['product_type'] ?? $trade['product'] ?? 'UNKNOWN',
                    'order_type' => $trade['orderType'] ?? $trade['order_type'] ?? null,
                    'trading_symbol' => $trade['tradingsymbol'] ?? $trade['tradingSymbol'] ?? $trade['trading_symbol'] ?? $trade['symbol'] ?? 'UNKNOWN',
                    'quantity' => $trade['fillsize'] ?? $trade['tradedQuantity'] ?? $trade['traded_quantity'] ?? $trade['quantity'] ?? 0,
                    'price' => $trade['fillprice'] ?? $trade['tradedPrice'] ?? $trade['traded_price'] ?? $trade['price'] ?? 0,
                    'trade_value' => $trade['tradevalue'] ?? (($trade['fillsize'] ?? $trade['quantity'] ?? 0) * ($trade['fillprice'] ?? $trade['price'] ?? 0)),
                    'trade_date' => date('Y-m-d'), // Angel One doesn't provide trade date in response, use current date
                    'trade_time' => isset($trade['filltime']) ? $trade['filltime'] :
                        (isset($trade['tradeTime']) ? date('H:i:s', strtotime($trade['tradeTime'])) :
                            (isset($trade['trade_time']) ? date('H:i:s', strtotime($trade['trade_time'])) : date('H:i:s'))),
                    'fill_id' => $trade['fillid'] ?? $trade['fillId'] ?? $trade['fill_id'] ?? null,
                    'fill_time' => isset($trade['filltime']) ? date('Y-m-d H:i:s', strtotime(date('Y-m-d') . ' ' . $trade['filltime'])) :
                        (isset($trade['fillTime']) ? date('Y-m-d H:i:s', strtotime($trade['fillTime'])) :
                            (isset($trade['fill_time']) ? date('Y-m-d H:i:s', strtotime($trade['fill_time'])) : null)),
                    'raw_data' => json_encode($trade),
                    'processed' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Insert into angel_trades table
                if ($angelTradesBuilder->insert($angelTradeData)) {
                    $savedCount++;
                }

            } catch (\Exception $e) {
                log_message('error', 'Failed to save Angel One trade: ' . $e->getMessage());
                continue;
            }
        }



        return $savedCount;
    }

    /**
     * Process Angel One trades for analysis (batch processing like Dhan)
     */
    private function processAngelOneTradesForAnalysis($trades, $userId)
    {
        $db = \Config\Database::connect();
        $tradesBuilder = $db->table('trades');
        $processedCount = 0;
        $processedCount = 0;

        // Group by tradingSymbol and transactionType (buy/sell) - same as Dhan
        $groupedTrades = [];
        foreach ($trades as $trade) {
            $symbol = $trade['tradingsymbol'] ?? $trade['tradingSymbol'] ?? $trade['trading_symbol'] ?? $trade['symbol'] ?? 'UNKNOWN';
            $side = strtolower($trade['transactiontype'] ?? $trade['transactionType'] ?? $trade['transaction_type'] ?? 'unknown');

            if ($side === 'unknown')
                continue;

            $groupedTrades[$symbol][$side][] = $trade;
        }

        foreach ($groupedTrades as $symbol => $sides) {
            $buyTrades = $sides['buy'] ?? [];
            $sellTrades = $sides['sell'] ?? [];

            $minCount = min(count($buyTrades), count($sellTrades));

            for ($i = 0; $i < $minCount; $i++) {
                $buy = $buyTrades[$i];
                $sell = $sellTrades[$i];

                // Get quantities with correct Angel One field mapping
                $buyQty = $buy['fillsize'] ?? $buy['tradedQuantity'] ?? $buy['traded_quantity'] ?? $buy['quantity'] ?? 0;
                $sellQty = $sell['fillsize'] ?? $sell['tradedQuantity'] ?? $sell['traded_quantity'] ?? $sell['quantity'] ?? 0;

                $quantity = min($buyQty, $sellQty);
                if ($quantity == 0)
                    continue;

                // Get prices with correct Angel One field mapping
                $entryPrice = $buy['fillprice'] ?? $buy['tradedPrice'] ?? $buy['traded_price'] ?? $buy['price'] ?? 0;
                $exitPrice = $sell['fillprice'] ?? $sell['tradedPrice'] ?? $sell['traded_price'] ?? $sell['price'] ?? 0;

                $entryAmount = $entryPrice * $quantity;
                $pnlAmount = ($exitPrice - $entryPrice) * $quantity;
                $pnlPercent = $entryAmount > 0 ? round(($pnlAmount / $entryAmount) * 100, 2) : 0;

                // Get trade date (Angel One doesn't provide trade date, use current date)
                $datetime = date('Y-m-d');

                // Use fillid as unique identifier (like Dhan uses orderId)
                $exchangeTradeId = $sell['fillid'] ?? $sell['exchangeTradeId'] ?? $sell['exchange_trade_id'] ?? $sell['tradeId'] ?? $sell['fillId'] ?? null;

                if (!$exchangeTradeId)
                    continue;

                // Avoid duplicates using unique broker_order_id (same as Dhan)
                $exists = $tradesBuilder->where('broker_order_id', $exchangeTradeId)->countAllResults();
                if ($exists > 0)
                    continue;

                // Insert into trades table (same format as Dhan)
                $tradesBuilder->insert([
                    'user_id' => $userId,
                    'symbol' => $symbol,
                    'market_type' => $this->getMarketTypeFromSegment($buy['exchange'] ?? $buy['exchangeSegment'] ?? $buy['exchange_segment'] ?? 'NSE'),
                    'datetime' => $datetime,
                    'entry_price' => $entryPrice,
                    'entry_quantity' => $quantity,
                    'entry_amount' => $entryAmount,
                    'exit_price' => $exitPrice,
                    'pnl_amount' => $pnlAmount,
                    'pnl_percent' => $pnlPercent,
                    'trade_type' => 1, // Intraday
                    'broker' => 'angel_one',
                    'broker_order_id' => $exchangeTradeId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

                $processedCount++;
            }
        }

        return $processedCount;
    }

    /**
     * Convert Angel One exchange segment to market type (integer values)
     */
    private function getMarketTypeFromSegment($segment)
    {
        $segmentMap = [
            'NSE' => 1, // Equity
            'BSE' => 1, // Equity
            'NFO' => 3, // Futures/Options
            'BFO' => 3, // Futures/Options
            'CDS' => 2, // Currency (assuming)
            'MCX' => 4  // Commodity (assuming)
        ];

        return $segmentMap[$segment] ?? 1; // Default to equity
    }



    /**
     * Get Angel One raw trades data
     */
    public function getAngelOneTrades()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        try {
            $db = \Config\Database::connect();
            $angelTradesBuilder = $db->table('angel_trades');

            $trades = $angelTradesBuilder
                ->where('user_id', $userId)
                ->orderBy('trade_date', 'DESC')
                ->orderBy('trade_time', 'DESC')
                ->limit(50)
                ->get()
                ->getResultArray();

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Angel One trades retrieved successfully',
                'data' => $trades,
                'count' => count($trades)
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Failed to get Angel One trades for user ' . $userId . ': ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to retrieve Angel One trades'
            ]);
        }
    }

    /**
     * Get Angel One processing statistics
     */
    public function getAngelOneStats()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $processor = new \App\Libraries\AngelOneTradeProcessor();
        $stats = $processor->getProcessingStats($userId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Process unprocessed Angel One trades
     */
    public function processAngelOneTrades()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $processor = new \App\Libraries\AngelOneTradeProcessor();
        $result = $processor->processUserTrades($userId);

        return $this->response->setJSON($result);
    }

    /**
     * Disconnect Angel One broker
     */
    public function disconnectAngelOne()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        // Clear Angel One specific fields only
        $data = [
            'angel_client_id' => null,
            'angel_api_key' => null,
            'angel_access_token' => null,
            'angel_refresh_token' => null,
            'angel_feed_token' => null,
            'angel_connected_on' => null
        ];

        $updated = $this->usermodel->update($userId, $data);

        if ($updated) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Angel One disconnected successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to disconnect Angel One'
            ]);
        }
    }

    /**
     * Debug method to check received parameters
     */
    public function debugBrokerParams()
    {
        $allParams = [
            'POST' => $this->request->getPost(),
            'GET' => $this->request->getGet(),
            'RAW_INPUT' => $this->request->getBody(),
            'HEADERS' => $this->request->getHeaders()
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => $allParams
        ]);
    }

    /**
     * Debug Angel One API connectivity and authentication
     */
    public function debugAngelOne()
    {
        // Handle both GET and POST requests
        $apiKey = $this->request->getPost('api_key') ?? $this->request->getGet('api_key') ?? 'demo_key';
        $clientCode = $this->request->getPost('client_code') ?? $this->request->getGet('client_code') ?? 'demo_client';
        $mpin = $this->request->getPost('mpin') ?? $this->request->getGet('mpin') ?? '123456';
        $totp = $this->request->getPost('totp') ?? $this->request->getGet('totp') ?? '123456';

        $debugInfo = [
            'timestamp' => date('Y-m-d H:i:s'),
            'credentials' => [
                'api_key' => substr($apiKey, 0, 10) . '...',
                'client_code' => $clientCode,
                'mpin' => str_repeat('*', strlen($mpin)),
                'totp' => $totp
            ],
            'tests' => []
        ];

        try {
            // Test 1: Basic connectivity
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://apiconnect.angelone.in',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_NOBODY => true,
                CURLOPT_SSL_VERIFYPEER => false
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            $debugInfo['tests']['connectivity'] = [
                'success' => !$error && $httpCode < 400,
                'http_code' => $httpCode,
                'error' => $error ?: 'None'
            ];

            // Test 2: Authentication endpoint test
            $authUrl = 'https://apiconnect.angelone.in/rest/auth/angelbroking/user/v1/loginByPassword';
            $authData = [
                'clientcode' => $clientCode,
                'password' => $mpin,
                'totp' => $totp
            ];

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $authUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($authData),
                CURLOPT_HTTPHEADER => [
                    'X-PrivateKey: ' . $apiKey,
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'X-UserType: USER',
                    'X-SourceID: WEB',
                    'User-Agent: TradeDiary/1.0'
                ],
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => false
            ]);

            $authResponse = curl_exec($ch);
            $authHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $authError = curl_error($ch);
            curl_close($ch);

            $debugInfo['tests']['authentication'] = [
                'success' => !$authError,
                'http_code' => $authHttpCode,
                'error' => $authError ?: 'None',
                'response_length' => strlen($authResponse),
                'response_preview' => substr($authResponse, 0, 200),
                'is_json' => json_decode($authResponse) !== null,
                'json_error' => json_last_error_msg()
            ];

            // Try to decode the response
            if ($authResponse) {
                $decodedResponse = json_decode($authResponse, true);
                if ($decodedResponse !== null) {
                    $debugInfo['tests']['authentication']['parsed_response'] = $decodedResponse;
                }
            }

        } catch (\Exception $e) {
            $debugInfo['error'] = $e->getMessage();
        }

        return $this->response->setJSON([
            'success' => true,
            'debug_info' => $debugInfo
        ]);
    }

    /**
     * Test Angel One tradebook API
     */
    public function testAngelOneTradeBook()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        try {
            // Get user's Angel One credentials
            $user = $this->usermodel->find($userId);

            if (!$user || !$user['angel_client_id'] || !$user['angel_access_token'] || !$user['angel_api_key']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Angel One connection not found. Please reconnect your Angel One account.'
                ]);
            }

            // Decrypt credentials
            $accessToken = $this->decrypt_data($user['angel_access_token']);
            $apiKey = $this->decrypt_data($user['angel_api_key']);

            // Initialize Angel One API
            $angelApi = new \App\Libraries\AngelOneApi();
            $angelApi->setAccessToken($accessToken);
            $angelApi->setClientCode($user['angel_client_id']);
            $angelApi->setApiKey($apiKey);

            // Test tradebook API
            $tradeBookResponse = $angelApi->getTradeBook();

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Angel One tradebook API test completed',
                'response' => $tradeBookResponse,
                'has_data' => isset($tradeBookResponse['data']),
                'data_count' => isset($tradeBookResponse['data']) ? count($tradeBookResponse['data']) : 0
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test Angel One API connectivity
     */
    public function testAngelOneConnectivity()
    {
        try {
            // Test basic connectivity to Angel One API
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://apiconnect.angelone.in',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_USERAGENT => 'TradeDiary/1.0'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Cannot connect to Angel One servers: ' . $error
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Angel One servers are reachable',
                'http_code' => $httpCode
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Connectivity test failed: ' . $e->getMessage()
            ]);
        }
    }


    public function syncKiteTrades($userId)
    {
        $user = $this->kitemodel->where('user_id', $userId)->first();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Kite not linked'
            ]);
        }

        require_once APPPATH . 'Libraries/KiteConnect/KiteConnect.php';

        // Check token validity (24h)
        if ($user['access_token'] && strtotime($user['token_generated_at']) + 86400 > time()) {
            try {
                $kite = new \KiteConnect\KiteConnect($user['api_key']);
                $kite->setAccessToken($user['access_token']);
                $trades = $kite->getTrades();

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Trades synced',
                    'trades' => $trades
                ]);
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Token error: ' . $e->getMessage()
                ]);
            }
        }

        // Token expired
        return $this->response->setJSON([
            'success' => false,
            'broker' => 'kite',
            'login_required' => true,
            'message' => 'Access token expired. Please login again.'
        ]);
    }

    public function generateKiteToken()
    {
        $requestToken = $this->request->getVar('access_token');
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$requestToken || !$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Missing request token or user'
            ]);
        }

        $userModel = new KiteBrokerModel();
        $user = $userModel->where('user_id', $userId)->first();

        if (!$user || empty($user['api_key']) || empty($user['api_secret'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Kite credentials not found for user'
            ]);
        }

        require_once APPPATH . 'Libraries/KiteConnect/KiteConnect.php';

        try {
            $kite = new \KiteConnect\KiteConnect($user['api_key']);
            $data = $kite->generateSession($requestToken, $user['api_secret']);
            $accessToken = $data['access_token'];

            $userModel->update($user['id'], [
                'access_token' => $accessToken,
                'token_generated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Access token saved successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error generating access token: ' . $e->getMessage()
            ]);
        }
    }



}
