// AI Test Page JavaScript - Enhanced for Modern Design
console.log('AI Test page loaded with enhanced design');

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Test page ready with modern UI');

    // Keyboard shortcut for analysis (Ctrl+Enter or Cmd+Enter)
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const analyzeBtn = document.getElementById('analyze-btn');
            if (analyzeBtn && !analyzeBtn.disabled) {
                analyzeBtn.click();
            }
        }
    });

    // Enhanced button hover effects
    const analyzeBtn = document.getElementById('analyze-btn');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('mouseenter', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        analyzeBtn.addEventListener('mouseleave', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(0)';
            }
        });
    }

    // Add subtle animations to summary cards when they appear
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const target = mutation.target;

                // Animate summary cards
                if (target.id === 'reports-summary' && !target.classList.contains('hidden')) {
                    const summaryItems = target.querySelectorAll('.summary-item');
                    summaryItems.forEach((item, index) => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(20px)';
                        setTimeout(() => {
                            item.style.transition = 'all 0.3s ease';
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }

                // Animate analysis results
                if (target.id === 'analysis-container' && !target.classList.contains('hidden')) {
                    const analysisResult = target.querySelector('#analysis-result');
                    if (analysisResult) {
                        analysisResult.style.opacity = '0';
                        analysisResult.style.transform = 'translateY(20px)';
                        setTimeout(() => {
                            analysisResult.style.transition = 'all 0.5s ease';
                            analysisResult.style.opacity = '1';
                            analysisResult.style.transform = 'translateY(0)';
                        }, 200);
                    }
                }
            }
        });
    });

    // Observe summary and analysis containers
    const reportsummary = document.getElementById('reports-summary');
    const analysisContainer = document.getElementById('analysis-container');

    if (reportsummary) {
        observer.observe(reportsummary, { attributes: true });
    }

    if (analysisContainer) {
        observer.observe(analysisContainer, { attributes: true });
    }

    // Add loading state visual enhancements
    const loadingState = document.getElementById('loading-state');
    if (loadingState) {
        observer.observe(loadingState, { attributes: true });
    }
});
