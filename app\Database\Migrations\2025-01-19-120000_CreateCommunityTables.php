<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCommunityTables extends Migration
{
    public function up()
    {
        // Create posts table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who created the post'
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Post title'
            ],
            'content' => [
                'type' => 'TEXT',
                'comment' => 'Post content/description'
            ],
            'post_type' => [
                'type' => 'ENUM',
                'constraint' => ['setup', 'pnl', 'analysis', 'educational', 'general'],
                'default' => 'general',
                'comment' => 'Type of post'
            ],
            'asset_class' => [
                'type' => 'ENUM',
                'constraint' => ['equity', 'options', 'futures', 'crypto', 'forex', 'all'],
                'default' => 'all',
                'comment' => 'Asset class for the post'
            ],
            'tags' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Hashtags and mentions as JSON array'
            ],
            'image_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'URL to attached image/chart'
            ],
            'likes_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached likes count'
            ],
            'comments_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached comments count'
            ],
            'shares_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached shares count'
            ],
            'is_pinned' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => 'Whether post is pinned'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('post_type');
        $this->forge->addKey('asset_class');
        $this->forge->addKey('created_at');
        $this->forge->addKey('is_pinned');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_posts');

        // Create comments table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'post_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'Post this comment belongs to'
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who made the comment'
            ],
            'parent_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Parent comment ID for replies'
            ],
            'content' => [
                'type' => 'TEXT',
                'comment' => 'Comment content'
            ],
            'likes_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached likes count for comment'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('post_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('parent_id');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('post_id', 'community_posts', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('parent_id', 'community_comments', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_comments');

        // Create likes table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who liked'
            ],
            'likeable_type' => [
                'type' => 'ENUM',
                'constraint' => ['post', 'comment'],
                'comment' => 'Type of item being liked'
            ],
            'likeable_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'ID of the item being liked'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey(['likeable_type', 'likeable_id']);
        $this->forge->addUniqueKey(['user_id', 'likeable_type', 'likeable_id'], 'unique_like');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_likes');

        // Create follows table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'follower_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User who is following'
            ],
            'following_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'User being followed'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('follower_id');
        $this->forge->addKey('following_id');
        $this->forge->addUniqueKey(['follower_id', 'following_id'], 'unique_follow');
        $this->forge->addForeignKey('follower_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('following_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_follows');

        // Add community-related fields to users table
        $fields = [
            'followers_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached followers count'
            ],
            'following_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached following count'
            ],
            'posts_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Cached posts count'
            ],
            'bio' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'User bio for community profile'
            ],
            'badge' => [
                'type' => 'ENUM',
                'constraint' => ['pro', 'gainer', 'master', 'analyst', 'none'],
                'default' => 'none',
                'comment' => 'User badge/achievement'
            ],
        ];

        $this->forge->addColumn('users', $fields);
    }

    public function down()
    {
        // Drop community tables
        $this->forge->dropTable('community_follows');
        $this->forge->dropTable('community_likes');
        $this->forge->dropTable('community_comments');
        $this->forge->dropTable('community_posts');

        // Remove community fields from users table
        $this->forge->dropColumn('users', [
            'followers_count',
            'following_count',
            'posts_count',
            'bio',
            'badge'
        ]);
    }
}
