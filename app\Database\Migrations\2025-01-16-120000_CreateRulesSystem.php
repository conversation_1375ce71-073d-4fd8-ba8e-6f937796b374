<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateRulesSystem extends Migration
{
    public function up()
    {
        // Create rules table for storing trading rules
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'NULL for global rules, user_id for custom rules'
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Rule name/title'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Detailed rule description'
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'Rule category (risk_management, entry, exit, etc.)'
            ],
            'is_active' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 1,
                'comment' => 'Whether rule is active'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('is_active');
        $this->forge->addKey('category');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('rules');

        // Create trade_rules junction table if it doesn't exist
        if (!$this->db->tableExists('trade_rules')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'trade_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                ],
                'rule_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                ],
                'followed' => [
                    'type' => 'TINYINT',
                    'constraint' => 1,
                    'default' => 1,
                    'comment' => '1 = followed, 0 = violated'
                ],
                'notes' => [
                    'type' => 'TEXT',
                    'null' => true,
                    'comment' => 'Additional notes about rule adherence'
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('trade_id');
            $this->forge->addKey('rule_id');
            $this->forge->addKey('followed');
            $this->forge->addUniqueKey(['trade_id', 'rule_id']);
            $this->forge->addForeignKey('trade_id', 'trades', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('rule_id', 'rules', 'id', 'CASCADE', 'CASCADE');
            $this->forge->createTable('trade_rules');
        }

        // Insert default trading rules
        $defaultRules = [
            [
                'name' => 'Wait for confirmation',
                'description' => 'Wait for candle close confirmation before entering trades',
                'category' => 'entry',
                'user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Avoid revenge trading',
                'description' => 'Never enter a trade to recover losses from previous trade',
                'category' => 'psychology',
                'user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Respect stop loss',
                'description' => 'Always set and respect stop loss levels before entering trades',
                'category' => 'risk_management',
                'user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Check higher timeframe',
                'description' => 'Always analyze higher timeframe context before entering trades',
                'category' => 'analysis',
                'user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Risk management',
                'description' => 'Never risk more than 2% of capital on a single trade',
                'category' => 'risk_management',
                'user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('rules')->insertBatch($defaultRules);
    }

    public function down()
    {
        // Drop tables in reverse order due to foreign key constraints
        $this->forge->dropTable('trade_rules', true);
        $this->forge->dropTable('rules', true);
    }
}
