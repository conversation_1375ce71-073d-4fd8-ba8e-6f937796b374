"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Home, BookOpen, Bo<PERSON>, <PERSON><PERSON><PERSON>, Settings, Menu, TrendingUp } from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarProps {
  collapsed: boolean
  onToggle: () => void
}

const navigation = [
  { name: "Dashboard", icon: Home, current: false },
  { name: "Trade Journal", icon: BookOpen, current: false },
  { name: "AI Summary", icon: Bo<PERSON>, current: true },
  { name: "Analytics", icon: <PERSON><PERSON><PERSON>, current: false },
  { name: "Setting<PERSON>", icon: Settings, current: false },
]

export function Sidebar({ collapsed, onToggle }: SidebarProps) {
  return (
    <div
      className={cn(
        "fixed left-0 top-0 z-40 h-screen bg-slate-900/95 backdrop-blur-xl border-r border-slate-800 transition-all duration-300",
        collapsed ? "w-20" : "w-72",
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-800">
        <div className={cn("flex items-center space-x-3", collapsed && "justify-center")}>
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          {!collapsed && (
            <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              TradeSync Pro
            </span>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="text-slate-400 hover:text-white hover:bg-slate-800"
        >
          <Menu className="w-5 h-5" />
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon
          return (
            <Button
              key={item.name}
              variant={item.current ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start h-12 text-slate-300 hover:text-white hover:bg-slate-800/50",
                item.current && "bg-slate-800 text-white shadow-md",
                collapsed && "justify-center px-0",
              )}
            >
              <div
                className={cn(
                  "w-8 h-8 rounded-lg flex items-center justify-center",
                  item.current ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white" : "bg-slate-800",
                )}
              >
                <Icon className="w-4 h-4" />
              </div>
              {!collapsed && <span className="ml-3 font-medium">{item.name}</span>}
            </Button>
          )
        })}
      </nav>

      {/* User Profile */}
      {!collapsed && (
        <div className="p-4 border-t border-slate-800">
          <div className="flex items-center space-x-3 p-3 rounded-xl bg-slate-800/50">
            <Avatar className="w-10 h-10">
              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white font-semibold">
                TU
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">Trade User</p>
              <p className="text-xs text-slate-400">Pro Member</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
