document.addEventListener('DOMContentLoaded', function () {

    updateGoalStatuses()
    getPnlGoalDashboardStats()
    createPnlGoalsCards()

    // Rotate motivational quotes
    const quotes = [
        { text: "The stock market is a device for transferring money from the impatient to the patient.", author: "<PERSON>" },
        { text: "Risk comes from not knowing what you're doing.", author: "<PERSON> Buffett" },
        { text: "In trading, you have to be defensive and aggressive at the same time.", author: "<PERSON>" },
        { text: "The most important quality for an investor is temperament, not intellect.", author: "<PERSON>" },
        { text: "Plan your trade and trade your plan.", author: "Unknown" },
        { text: "Discipline equals freedom.", author: "<PERSON><PERSON><PERSON>" },
        { text: "The four most dangerous words in investing: 'This time it's different.'", author: "Sir <PERSON>" }
    ];

    let currentQuote = 0;
    const quoteElement = document.querySelector('quoteElement');
    const quoteText = document.getElementById('quoteText');
    const quoteAuthor = document.getElementById('quoteAuthor');

    function rotateQuote() {
        currentQuote = (currentQuote + 1) % quotes.length;
        quoteElement.classList.remove('fade-in');
        setTimeout(() => {
            quoteText.textContent = `"${quotes[currentQuote].text}"`;
            quoteAuthor.textContent = `— ${quotes[currentQuote].author}`;
            quoteElement.classList.add('fade-in');
        }, 500);
    }

    // setInterval(rotateQuote, 6000);

    // Modal handling
    const addGoalBtn = document.getElementById('addGoalBtn');
    const addGoalModal = document.getElementById('addGoalModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const cancelAddGoal = document.getElementById('cancelAddGoal');

    addGoalBtn.addEventListener('click', () => {
        document.querySelectorAll('.step-content').forEach(step => {
            step.classList.add('hidden');
        });
        step1.classList.remove('hidden');
        addGoalModal.classList.remove('hidden');
    });

    closeModalBtn.addEventListener('click', () => {
        addGoalModal.classList.add('hidden');
    });

    // Close modal when clicking outside
    addGoalModal.addEventListener('click', (e) => {
        if (e.target === addGoalModal) {
            addGoalModal.classList.add('hidden');
        }
    });

    // Goal tab switching
    const goalTabs = document.querySelectorAll('.goal-tab');
    const tabIndicator = document.querySelector('.tab-indicator');

    goalTabs.forEach((tab, index) => {
        tab.addEventListener('click', () => {
            // Update active tab
            goalTabs.forEach(t => t.classList.remove('active', 'text-blue-400'));
            tab.classList.add('active', 'text-blue-400');

            // Move indicator
            const tabWidth = tab.offsetWidth;
            const tabLeft = tab.offsetLeft;
            tabIndicator.style.width = `${tabWidth}px`;
            tabIndicator.style.left = `${tabLeft}px`;

            // In a real app, you would filter goals here
        });
    });

    // Add animation to progress bars on page load
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const originalWidth = bar.style.width;
        bar.style.width = '0';
        setTimeout(() => {
            bar.style.width = originalWidth;
        }, 300);
    });

    // Multi-step form handling
    const nextStep1 = document.getElementById('nextStep1');
    const prevButtons = document.querySelectorAll('.prev-step');
    const step1 = document.getElementById('step1');
    const step2Pnl = document.getElementById('step2-pnl');
    const step2Risk = document.getElementById('step2-risk');
    const step2Behavior = document.getElementById('step2-behavior');
    const step2Execution = document.getElementById('step2-execution');
    const goalTypeOptions = document.querySelectorAll('.goal-type-option');
    const timeframeOptions = document.querySelectorAll('.timeframe-option');
    const riskTypeOptions = document.querySelectorAll('.risk-type-option');

    // Goal type selection
    goalTypeOptions.forEach(option => {
        option.addEventListener('click', () => {
            goalTypeOptions.forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
        });
    });

    // Risk type selection
    riskTypeOptions.forEach(option => {
        option.addEventListener('click', () => {
            riskTypeOptions.forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
        });
    });

    // Timeframe selection
    timeframeOptions.forEach(option => {
        option.addEventListener('click', () => {
            timeframeOptions.forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');

            // Show/hide custom timeframe input
            const customTimeframeInput = document.getElementById('customTimeframeInput');
            if (option.dataset.timeframe === 'custom') {
                customTimeframeInput.classList.add('active');
                $('#custom_number').addClass('gReq');
                $('#custom_period').addClass('gReq');
            } else {
                customTimeframeInput.classList.remove('active');
                $('#custom_number').removeClass('gReq');
                $('#custom_period').removeClass('gReq');
            }
            $('#timeframe').val(option.dataset.timeframe)
        });
    });

    // Next step button
    nextStep1.addEventListener('click', () => {
        const selectedType = document.querySelector('.goal-type-option.selected').dataset.type;

        step1.classList.add('hidden');

        if (selectedType === 'pnl') {
            step2Pnl.classList.remove('hidden');
        } else if (selectedType === 'risk') {
            step2Risk.classList.remove('hidden');
        } else if (selectedType === 'behavior') {
            step2Behavior.classList.remove('hidden');
        } else if (selectedType === 'execution') {
            step2Execution.classList.remove('hidden');
        }
    });

    // Previous step buttons
    prevButtons.forEach(button => {
        button.addEventListener('click', () => {
            document.querySelectorAll('.step-content').forEach(step => {
                step.classList.add('hidden');
            });
            step1.classList.remove('hidden');
        });
    });

    // Form submission
    const goalForm = document.getElementById('goalForm');
    goalForm.addEventListener('submit', (e) => {
        e.preventDefault();
        addGoalModal.classList.add('hidden');

        // In a real app, you would handle form submission here
        alert('Goal created successfully!');
    });

    // P&L Goal Type Radio Button Changes
    const pnlTypeRadios = document.querySelectorAll('input[name="pnlType"]');
    const targetValueInput = document.getElementById('targetValueInput');
    const targetValueSymbol = document.getElementById('targetValueSymbol');

    pnlTypeRadios.forEach(radio => {
        radio.addEventListener('change', function () {
            if (this.value === 'winrate') {
                targetValueInput.placeholder = "e.g., 70%";
                targetValueSymbol.textContent = "%";
            } else {
                targetValueInput.placeholder = "e.g., ₹50,000";
                targetValueSymbol.textContent = "₹";
            }
        });
    });

    // Initialize custom timeframe functionality
    const customTimeframeBtn = document.getElementById('customTimeframeBtn');
    const customTimeframeInput = document.getElementById('customTimeframeInput');

    customTimeframeBtn.addEventListener('click', function () {
        customTimeframeInput.classList.add('active');
    });
});



// submit pnl goal form

function submitPnlGoalForm(postUrl) {
    var isValid = true;
    var inputname = "";

    $('.gReq').each(function () {
        var value = $(this).val();
        if (!value) {
            inputname = $(this).data('inputname');
            isValid = false;
            return false; // break out of loop
        }
    });

    if (!isValid) {
        createToast('error', 'Validation Error', `${inputname} is required!`);
        return false;
    }

    $.ajax({
        type: "POST",
        url: postUrl,
        data: $('#pnlGoalForm').serialize(),
        dataType: "JSON",
        success: function (response) {
            if (response.status) {
                createToast('success', 'Success', response.message || 'Goal saved successfully!');

                addGoalModal.classList.add('hidden');
                resetPnlGoalForm()
                updateGoalStatuses()
                getPnlGoalDashboardStats()
                createPnlGoalsCards()
            } else {
                createToast('error', 'Error', response.message || 'Something went wrong.');
            }
        },
        error: function (xhr, status, error) {
            createToast('error', 'Server Error', xhr.responseText || error || 'An unexpected error occurred.');
        }
    });
}

function resetPnlGoalForm() {
    $('#goal_name').val('');
    $('#monthlyBtn').trigger('click');
    $('#custom_number').val('');
    $('#targetValueInput').val('');
    $('#custom_period').val('days');
}

function updateGoalStatuses() {
    $.ajax({
        type: "POST",
        url: base_url + "updateGoalStatuses",
        data: {},
        dataType: "JSON",
        success: function (response) {
            console.log(response)
        }
    });
}

function getPnlGoalDashboardStats() {
    $.ajax({
        type: "POST",
        url: base_url + "getPnlGoalDashboardStats",
        data: {},
        dataType: "JSON",
        success: function (res) {
            $('#activeGoals').text(res.active_goals);
            $('#newGoalsThisWeek').text(`${res.new_this_week} new this week`);
            $('#completionRate').text(`${res.completion_rate}%`);
            $('#avgDailyPnl').text(`₹${res.avg_daily_pnl}`);
            $('#currentStreak').text(`${res.streak_days} days`);
            $('#toBestStreak').text(`${res.to_best_streak} days to best streak`);
        }
    });
}


function createPnlGoalsCards() {
    $.ajax({
        url: base_url + 'getActiveGoals',
        type: 'POST',
        dataType: 'json',
        success: function (res) {
            const container = $('#goalsContainer');
            container.empty();

            if (res.goals.length === 0) {
                container.append(`
                <div class="flex flex-col justify-center items-center w-full py-16 text-center text-gray-500 dark:text-gray-400 col-span-full">
                    <i class="fas fa-flag-checkered text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
                    <p class="text-lg font-medium">No active goals yet</p>
                    <p class="text-sm">Start by creating your first goal to track your performance</p>
                </div>
            `);
            } else {
                res.goals.forEach(goal => {
                    var symbol = '₹';
                    if (goal.goal_type == 'Winrate') {
                        symbol = '';
                    }
                    container.append(`
                    <div class="modern-card glass-card card-pnl">
                        <div class="card-content">
                            <div class="card-header">
                                <div>
                                    <span class="type-badge">
                                        <i class="fas fa-rupee-sign mr-1 text-xs"></i> ${goal.goal_type}
                                    </span>
                                    <h3 class="text-lg font-semibold text-white mt-2">${goal.goal_name}</h3>
                                </div>
                                <span class="status-badge ${goal.status_class}">
                                    <i class="fas fa-check-circle mr-1"></i> ${goal.status_label}
                                </span>
                            </div>
                            <div class="metric-value">${symbol}${goal.target}</div>
                            <p class="text-slate-400 text-sm">Target ${goal.timeframe} goal</p>
                            <div class="mt-4">
                                <div class="progress-info">
                                    <span>${symbol}${goal.actual}</span>
                                    <span>${goal.progress}%</span>
                                </div>
                                <div class="thick-progress">
                                    <div class="thick-progress-bar" style="width: ${goal.progress}%"></div>
                                </div>
                            </div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <p class="stat-label">Current</p>
                                    <p class="stat-value">${symbol}${goal.actual}</p>
                                </div>
                                <div class="stat-item">
                                    <p class="stat-label">Days Left</p>
                                    <p class="stat-value">${goal.days_left}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
                });
            }
        }
    });
}