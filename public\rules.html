<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rules | Stock Journal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0E172A;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .glass-card {
            background: rgba(28, 36, 57, 0.8);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid #2A334F;
        }
        
        .glow-effect {
            box-shadow: 0 0 12px rgba(46, 255, 122, 0.3);
        }
        
        .glow-effect-blue {
            box-shadow: 0 0 12px rgba(59, 130, 246, 0.3);
        }
        
        .glow-effect-purple {
            box-shadow: 0 0 12px rgba(167, 139, 250, 0.3);
        }
        
        .glow-effect-orange {
            box-shadow: 0 0 12px rgba(251, 191, 36, 0.3);
        }
        
        .input-glow:focus {
            box-shadow: 0 0 0 3px rgba(46, 255, 122, 0.3);
            border-color: #2EFF7A;
        }
        
        .btn-ripple {
            position: relative;
            overflow: hidden;
        }
        
        .btn-ripple:after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform .5s, opacity 1s;
        }
        
        .btn-ripple:active:after {
            transform: scale(0, 0);
            opacity: .3;
            transition: 0s;
        }
        
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            background: linear-gradient(90deg, #2EFF7A, #3B82F6);
        }
        
        .chart-container {
            position: relative;
            width: 100%;
            height: 200px;
        }
        
        .chart-bar {
            position: absolute;
            bottom: 0;
            width: 30px;
            background: linear-gradient(to top, #3B82F6, #A78BFA);
            border-radius: 4px 4px 0 0;
            transition: height 0.5s ease;
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-animation {
            animation: modalFadeIn 0.3s ease-out forwards;
        }
        
        .rule-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .rule-card {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white">Trading Rules</h1>
                <p class="text-[#94A3B8] mt-2">Define and track your trading rules to improve consistency</p>
            </div>
            <button id="createRuleBtn" class="btn-ripple mt-4 md:mt-0 flex items-center px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-blue-500 text-white font-medium hover:glow-effect transition-all duration-300">
                <i data-lucide="plus" class="w-5 h-5 mr-2"></i> Create New Rule
            </button>
        </div>
        
        <!-- Rule Selector -->
        <div class="glass-card rounded-2xl p-6 mb-8">
            <div class="flex flex-col md:flex-row items-start md:items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-white mb-2 md:mb-0">Your Rule Collection</h2>
                <div class="relative w-full md:w-64">
                    <input type="text" placeholder="Search rules..." class="w-full bg-gray-900 bg-opacity-50 border border-gray-700 rounded-xl py-2 px-4 text-white focus:outline-none focus:input-glow transition-all duration-300">
                    <i data-lucide="search" class="absolute right-3 top-2.5 text-gray-400"></i>
                </div>
            </div>
            
            <div class="flex flex-wrap gap-2 mb-6">
                <span class="inline-flex items-center px-3 py-1 rounded-full bg-[#1C2439] text-sm text-white border border-[#2A334F]">
                    All Rules <span class="ml-1 text-gray-400">(24)</span>
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full bg-blue-900 bg-opacity-30 text-sm text-blue-300 border border-blue-700">
                    Most Used <i data-lucide="trending-up" class="w-4 h-4 ml-1"></i>
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full bg-purple-900 bg-opacity-30 text-sm text-purple-300 border border-purple-700">
                    High Impact <i data-lucide="zap" class="w-4 h-4 ml-1"></i>
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full bg-green-900 bg-opacity-30 text-sm text-green-300 border border-green-700">
                    New <i data-lucide="sparkles" class="w-4 h-4 ml-1"></i>
                </span>
            </div>
            
            <!-- Rules Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Rule Card 1 -->
                <div class="rule-card glass-card rounded-xl p-5 border border-gray-800 hover:border-green-500 transition-all duration-300 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-white flex items-center">
                                Waited for confirmation
                                <span class="ml-2 px-2 py-0.5 bg-[#2EFF7A] bg-opacity-20 text-[#2EFF7A] text-xs rounded-full flex items-center">
                                    <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i> +12%
                                </span>
                            </h3>
                            <p class="text-gray-400 text-sm mt-2">Wait for candle close confirmation before entering trades</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-blue-900 hover:text-blue-300 text-gray-400 transition-colors">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-red-900 hover:text-red-300 text-gray-400 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex items-center text-xs text-gray-500">
                            <i data-lucide="calendar" class="w-3 h-3 mr-1"></i> Used 18 times this month
                        </div>
                        <div class="w-8 h-8 rounded-full bg-blue-900 bg-opacity-50 flex items-center justify-center text-blue-300 text-xs font-bold">
                            87%
                        </div>
                    </div>
                </div>
                
                <!-- Rule Card 2 -->
                <div class="rule-card glass-card rounded-xl p-5 border border-gray-800 hover:border-purple-500 transition-all duration-300 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-white">Avoided revenge trading</h3>
                            <p class="text-gray-400 text-sm mt-2">Never enter a trade to recover losses from previous trade</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-blue-900 hover:text-blue-300 text-gray-400 transition-colors">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-red-900 hover:text-red-300 text-gray-400 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex items-center text-xs text-gray-500">
                            <i data-lucide="calendar" class="w-3 h-3 mr-1"></i> Used 12 times this month
                        </div>
                        <div class="w-8 h-8 rounded-full bg-purple-900 bg-opacity-50 flex items-center justify-center text-purple-300 text-xs font-bold">
                            92%
                        </div>
                    </div>
                </div>
                
                <!-- Rule Card 3 -->
                <div class="rule-card glass-card rounded-xl p-5 border border-gray-800 hover:border-orange-500 transition-all duration-300 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-white flex items-center">
                                Respect stop loss
                                <span class="ml-2 px-2 py-0.5 bg-orange-900 bg-opacity-30 text-orange-300 text-xs rounded-full flex items-center">
                                    <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i> Needs work
                                </span>
                            </h3>
                            <p class="text-gray-400 text-sm mt-2">Always set and respect stop loss levels before entering trades</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-blue-900 hover:text-blue-300 text-gray-400 transition-colors">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-red-900 hover:text-red-300 text-gray-400 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex items-center text-xs text-gray-500">
                            <i data-lucide="calendar" class="w-3 h-3 mr-1"></i> Used 9 times this month
                        </div>
                        <div class="w-8 h-8 rounded-full bg-orange-900 bg-opacity-50 flex items-center justify-center text-orange-300 text-xs font-bold">
                            64%
                        </div>
                    </div>
                </div>
                
                <!-- Rule Card 4 -->
                <div class="rule-card glass-card rounded-xl p-5 border border-gray-800 hover:border-blue-500 transition-all duration-300 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-white">Checked higher timeframe</h3>
                            <p class="text-gray-400 text-sm mt-2">Always analyze higher timeframe context before entering trades</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-blue-900 hover:text-blue-300 text-gray-400 transition-colors">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-red-900 hover:text-red-300 text-gray-400 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex items-center text-xs text-gray-500">
                            <i data-lucide="calendar" class="w-3 h-3 mr-1"></i> Used 15 times this month
                        </div>
                        <div class="w-8 h-8 rounded-full bg-blue-900 bg-opacity-50 flex items-center justify-center text-blue-300 text-xs font-bold">
                            78%
                        </div>
                    </div>
                </div>
                
                <!-- Rule Card 5 -->
                <div class="rule-card glass-card rounded-xl p-5 border border-gray-800 hover:border-purple-500 transition-all duration-300 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-white">Risk management</h3>
                            <p class="text-gray-400 text-sm mt-2">Never risk more than 2% of capital on a single trade</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-blue-900 hover:text-blue-300 text-gray-400 transition-colors">
                                <i data-lucide="edit-2" class="w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-800 hover:bg-red-900 hover:text-red-300 text-gray-400 transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex items-center text-xs text-gray-500">
                            <i data-lucide="calendar" class="w-3 h-3 mr-1"></i> Used 20 times this month
                        </div>
                        <div class="w-8 h-8 rounded-full bg-purple-900 bg-opacity-50 flex items-center justify-center text-purple-300 text-xs font-bold">
                            95%
                        </div>
                    </div>
                </div>
                
                <!-- Add New Rule Card -->
                <div class="flex items-center justify-center glass-card rounded-xl p-5 border-2 border-dashed border-gray-700 hover:border-green-500 transition-all duration-300 cursor-pointer" id="addRuleCard">
                    <div class="text-center">
                        <i data-lucide="plus-circle" class="w-8 h-8 mx-auto text-gray-400 mb-2"></i>
                        <p class="text-gray-400">Add new rule</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Rules Analysis Section -->
        <div class="glass-card rounded-2xl p-6 mb-8">
            <h2 class="text-xl font-semibold text-white mb-6">Rules Performance Analysis</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Rules Followed -->
                <div class="glass-card rounded-xl p-5 border border-gray-800 hover:glow-effect-blue transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Total Rules Followed</p>
                            <h3 class="text-3xl font-bold text-white mt-1">142</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-blue-900 bg-opacity-30 flex items-center justify-center text-blue-300">
                            <i data-lucide="check-circle" class="w-6 h-6"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400 flex items-center">
                            <i data-lucide="trending-up" class="w-4 h-4 mr-1"></i> +8.5%
                        </span>
                        <span class="text-gray-400 ml-2">vs last month</span>
                    </div>
                </div>
                
                <!-- Consistency Meter -->
                <div class="glass-card rounded-xl p-5 border border-gray-800 hover:glow-effect transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Rules Consistency</p>
                            <h3 class="text-3xl font-bold text-white mt-1">84%</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-green-900 bg-opacity-30 flex items-center justify-center text-green-300">
                            <i data-lucide="activity" class="w-6 h-6"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 84%"></div>
                        </div>
                        <p class="text-gray-400 text-xs mt-2">You're consistent with 84% of your rules</p>
                    </div>
                </div>
                
                <!-- Most Followed Rule -->
                <div class="glass-card rounded-xl p-5 border border-gray-800 hover:glow-effect-purple transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Most Followed Rule</p>
                            <h3 class="text-lg font-semibold text-white mt-1">Risk Management</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-purple-900 bg-opacity-30 flex items-center justify-center text-purple-300">
                            <i data-lucide="award" class="w-6 h-6"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex items-center">
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                            <span class="text-purple-300 text-sm font-medium ml-2">95%</span>
                        </div>
                        <p class="text-gray-400 text-xs mt-2">Used in 20 trades this month</p>
                    </div>
                </div>
                
                <!-- Impact Score -->
                <div class="glass-card rounded-xl p-5 border border-gray-800 hover:glow-effect-orange transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Average Impact Score</p>
                            <h3 class="text-3xl font-bold text-white mt-1">7.8</h3>
                        </div>
                        <div class="w-12 h-12 rounded-full bg-orange-900 bg-opacity-30 flex items-center justify-center text-orange-300">
                            <i data-lucide="bar-chart-2" class="w-6 h-6"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="text-green-400 flex items-center">
                            <i data-lucide="arrow-up" class="w-4 h-4 mr-1"></i> 1.2 pts
                        </span>
                        <span class="text-gray-400 ml-2">vs last month</span>
                    </div>
                </div>
            </div>
            
            <!-- Top Rules Chart -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="glass-card rounded-xl p-5 border border-gray-800">
                    <h3 class="font-semibold text-white mb-4">Top 5 Most Followed Rules</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300">Risk Management</span>
                                <span class="text-purple-300 font-medium">95%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300">Confirmation Wait</span>
                                <span class="text-blue-300 font-medium">87%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 87%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300">No Revenge Trading</span>
                                <span class="text-green-300 font-medium">92%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300">Higher TF Analysis</span>
                                <span class="text-blue-300 font-medium">78%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300">Stop Loss Respect</span>
                                <span class="text-orange-300 font-medium">64%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 64%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="glass-card rounded-xl p-5 border border-gray-800">
                    <h3 class="font-semibold text-white mb-4">Rules Impact on P&L</h3>
                    <div class="chart-container" id="impactChart">
                        <!-- Chart bars will be added via JavaScript -->
                    </div>
                    <div class="flex justify-between mt-4 text-xs text-gray-400">
                        <span>Risk Mgmt</span>
                        <span>Confirmation</span>
                        <span>No Revenge</span>
                        <span>TF Analysis</span>
                        <span>Stop Loss</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create Rule Modal -->
    <div id="createRuleModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 hidden">
        <div class="glass-card rounded-2xl p-6 w-full max-w-md modal-animation">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-white">Create New Rule</h2>
                <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            
            <form id="ruleForm">
                <div class="mb-4">
                    <label for="ruleName" class="block text-sm font-medium text-gray-300 mb-2">Rule Name</label>
                    <input type="text" id="ruleName" class="w-full bg-gray-900 bg-opacity-50 border border-gray-700 rounded-xl py-2 px-4 text-white focus:outline-none focus:input-glow transition-all duration-300" placeholder="e.g., Wait for confirmation" required>
                </div>
                
                <div class="mb-6">
                    <label for="ruleDescription" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea id="ruleDescription" rows="4" class="w-full bg-gray-900 bg-opacity-50 border border-gray-700 rounded-xl py-2 px-4 text-white focus:outline-none focus:input-glow transition-all duration-300" placeholder="Describe the rule in detail..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelRuleBtn" class="btn-ripple px-4 py-2 rounded-xl bg-gray-800 text-gray-300 hover:bg-gray-700 transition-all duration-300">
                        Cancel
                    </button>
                    <button type="submit" class="btn-ripple px-6 py-2 rounded-xl bg-gradient-to-r from-green-500 to-blue-500 text-white font-medium hover:glow-effect transition-all duration-300">
                        Save Rule
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Modal functionality
        const createRuleBtn = document.getElementById('createRuleBtn');
        const addRuleCard = document.getElementById('addRuleCard');
        const createRuleModal = document.getElementById('createRuleModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelRuleBtn = document.getElementById('cancelRuleBtn');
        
        [createRuleBtn, addRuleCard].forEach(el => {
            el.addEventListener('click', () => {
                createRuleModal.classList.remove('hidden');
            });
        });
        
        [closeModalBtn, cancelRuleBtn].forEach(el => {
            el.addEventListener('click', () => {
                createRuleModal.classList.add('hidden');
            });
        });
        
        // Form submission
        const ruleForm = document.getElementById('ruleForm');
        ruleForm.addEventListener('submit', (e) => {
            e.preventDefault();
            // Here you would typically handle the form submission to your backend
            alert('Rule created successfully!');
            createRuleModal.classList.add('hidden');
            ruleForm.reset();
        });
        
        // Create impact chart
        const impactValues = [8.7, 7.2, 6.8, 5.5, 4.2];
        const impactChart = document.getElementById('impactChart');
        
        impactValues.forEach((value, index) => {
            const barHeight = (value / 10) * 180; // Scale to fit chart height
            const bar = document.createElement('div');
            bar.className = 'chart-bar';
            bar.style.height = `${barHeight}px`;
            bar.style.left = `${20 + index * 60}px`;
            
            // Different colors for each bar
            if (index === 0) bar.style.background = 'linear-gradient(to top, #A78BFA, #3B82F6)';
            else if (index === 1) bar.style.background = 'linear-gradient(to top, #3B82F6, #2EFF7A)';
            else if (index === 2) bar.style.background = 'linear-gradient(to top, #2EFF7A, #FBBF24)';
            else if (index === 3) bar.style.background = 'linear-gradient(to top, #FBBF24, #A78BFA)';
            else bar.style.background = 'linear-gradient(to top, #FF5C8D, #FBBF24)';
            
            // Add tooltip
            bar.title = `${value.toFixed(1)} impact score`;
            
            impactChart.appendChild(bar);
        });
        
        // Add hover effects to rule cards
        const ruleCards = document.querySelectorAll('.rule-card');
        ruleCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                const borderColor = card.classList.contains('hover:border-green-500') ? 'green' :
                                   card.classList.contains('hover:border-blue-500') ? 'blue' :
                                   card.classList.contains('hover:border-purple-500') ? 'purple' : 'orange';
                
                card.style.boxShadow = `0 0 20px rgba(${
                    borderColor === 'green' ? '37, 211, 102' : 
                    borderColor === 'blue' ? '63, 140, 255' : 
                    borderColor === 'purple' ? '165, 108, 255' : '255, 181, 71'
                }, 0.3)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.boxShadow = '';
            });
        });
    </script>
</body>
</html>