<?php
/**
 * Upload Directory Setup Script
 * 
 * This script ensures that the upload directories exist with proper permissions
 * Run this once on your server after deployment
 */

// Define upload directories (relative to document root for public, absolute for writable)
$uploadDirs = [
    'uploads' => __DIR__ . '/uploads',
    'uploads/community' => __DIR__ . '/uploads/community',
    'uploads/screenshots' => __DIR__ . '/uploads/screenshots'
];

// Also check writable directory
$writableUploadDirs = [
    'writable/uploads' => dirname(__DIR__) . '/writable/uploads',
    'writable/uploads/community' => dirname(__DIR__) . '/writable/uploads/community'
];

$errors = [];
$success = [];

echo "<h2>Upload Directory Setup</h2>\n";
echo "<pre>\n";

// Check all directories
$allDirs = array_merge($uploadDirs, $writableUploadDirs);

foreach ($allDirs as $dir => $fullPath) {
    echo "Checking directory: $dir\n";
    
    // Check if directory exists
    if (!is_dir($fullPath)) {
        echo "  - Directory doesn't exist, creating...\n";
        
        if (mkdir($fullPath, 0755, true)) {
            echo "  ✓ Directory created successfully\n";
            $success[] = $dir;
        } else {
            echo "  ✗ Failed to create directory\n";
            $errors[] = "Failed to create $dir";
            continue;
        }
    } else {
        echo "  ✓ Directory exists\n";
    }
    
    // Check if directory is writable
    if (is_writable($fullPath)) {
        echo "  ✓ Directory is writable\n";
        $success[] = $dir . " (writable)";
    } else {
        echo "  ✗ Directory is not writable\n";
        echo "  - Attempting to fix permissions...\n";
        
        if (chmod($fullPath, 0755)) {
            echo "  ✓ Permissions fixed\n";
            $success[] = $dir . " (permissions fixed)";
        } else {
            echo "  ✗ Failed to fix permissions\n";
            $errors[] = "Cannot write to $dir - check server permissions";
        }
    }
    
    // Create index.html for security
    $indexFile = $fullPath . '/index.html';
    if (!file_exists($indexFile)) {
        $indexContent = '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';
        if (file_put_contents($indexFile, $indexContent)) {
            echo "  ✓ Security index.html created\n";
        }
    }
    
    echo "\n";
}

echo "=== SUMMARY ===\n";

if (!empty($success)) {
    echo "✓ SUCCESS:\n";
    foreach ($success as $item) {
        echo "  - $item\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "✗ ERRORS:\n";
    foreach ($errors as $error) {
        echo "  - $error\n";
    }
    echo "\n";
    echo "Please fix the above errors manually or contact your hosting provider.\n";
} else {
    echo "🎉 All upload directories are ready!\n";
    echo "\nYou can now delete this file for security.\n";
}

echo "</pre>\n";

// Show PHP info for debugging
echo "<h3>Server Information</h3>\n";
echo "<pre>\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "Current Directory: " . __DIR__ . "\n";
echo "</pre>\n";
?>
