$(document).ready(function () {
    $('.copy-btn').on('click', function () {
        const $btn = $(this);
        const textToCopy = $btn.closest('.copy-container').find('.my-link').text().trim();

        // Copy to clipboard
        const tempInput = $('<input>');
        $('body').append(tempInput);
        tempInput.val(textToCopy).select();
        document.execCommand('copy');
        tempInput.remove();

        // Visual feedback
        const originalText = $btn.text();
        $btn.text('Copied!');
        $btn.removeClass('bg-blue-500 hover:bg-blue-600').addClass('bg-green-500 hover:bg-green-600');

        setTimeout(() => {
            $btn.text(originalText);
            $btn.removeClass('bg-green-500 hover:bg-green-600').addClass('bg-blue-500 hover:bg-blue-600');
        }, 2000);
    });
});