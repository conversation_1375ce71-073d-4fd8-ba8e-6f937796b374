<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\TradeModel;

helper('cookie');

class Mistake extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Mistake Analysis';
        $data['active'] = 'mistakes';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'mistakes';
        $data['main_content'] = 'pages/mistakes';

        return view('includes/template', $data);
    }

    public function getMistakeMetrics()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Total mistakes count
        $totalMistakes = $db->query("
            SELECT COUNT(*) as total 
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND t.deleted_at IS NULL
        ", [$userId])->getRow()->total ?? 0;

        // Most common mistake
        $mostCommon = $db->query("
            SELECT m.name, COUNT(*) as count
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            JOIN mistakes m ON tm.mistake_id = m.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL
            AND m.is_active = 1
            AND (m.user_id IS NULL OR m.user_id = ?)
            GROUP BY m.id, m.name
            ORDER BY count DESC
            LIMIT 1
        ", [$userId, $userId])->getRow();

        // Improvement rate (mistakes this week vs last week)
        $thisWeekStart = date('Y-m-d', strtotime('monday this week'));
        $lastWeekStart = date('Y-m-d', strtotime('monday last week'));
        $lastWeekEnd = date('Y-m-d', strtotime('sunday last week'));

        $thisWeekMistakes = $db->query("
            SELECT COUNT(*) as count 
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND t.deleted_at IS NULL 
            AND DATE(t.datetime) >= ?
        ", [$userId, $thisWeekStart])->getRow()->count ?? 0;

        $lastWeekMistakes = $db->query("
            SELECT COUNT(*) as count 
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND t.deleted_at IS NULL 
            AND DATE(t.datetime) BETWEEN ? AND ?
        ", [$userId, $lastWeekStart, $lastWeekEnd])->getRow()->count ?? 0;

        $improvementRate = 0;
        if ($lastWeekMistakes > 0) {
            $improvementRate = round((($lastWeekMistakes - $thisWeekMistakes) / $lastWeekMistakes) * 100, 1);
        }

        return $this->response
            ->setContentType('application/json; charset=utf-8')
            ->setJSON([
                'totalMistakes' => $totalMistakes,
                'mostCommon' => [
                    'name' => mb_convert_encoding($mostCommon->name ?? 'No mistakes', 'UTF-8', 'UTF-8'),
                    'count' => $mostCommon->count ?? 0
                ],
                'improvementRate' => $improvementRate,
                'thisWeekMistakes' => $thisWeekMistakes,
                'lastWeekMistakes' => $lastWeekMistakes
            ], JSON_UNESCAPED_UNICODE);
    }



    public function getRecentMistakes()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $recentMistakes = $db->query("
            SELECT
                m.name,
                m.category,
                m.severity,
                m.impact,
                m.icon,
                m.color_class,
                m.user_id as mistake_user_id,
                COUNT(*) as count,
                MAX(t.datetime) as last_occurrence
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            JOIN mistakes m ON tm.mistake_id = m.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL
            AND m.is_active = 1
            AND (m.user_id IS NULL OR m.user_id = ?)
            GROUP BY m.id, m.name, m.category, m.severity, m.impact, m.icon, m.color_class, m.user_id
            ORDER BY last_occurrence DESC
            LIMIT 10
        ", [$userId, $userId])->getResultArray();

        // Ensure proper UTF-8 encoding and sanitize data
        foreach ($recentMistakes as &$mistake) {
            // Clean and validate name
            $mistake['name'] = mb_convert_encoding($mistake['name'], 'UTF-8', 'UTF-8');
            $mistake['name'] = preg_replace('/[^\w\s\-\(\)]/u', '', $mistake['name']); // Remove special chars
            $mistake['name'] = trim($mistake['name']) ?: 'Unknown Mistake';

            // Clean and validate category
            $mistake['category'] = mb_convert_encoding($mistake['category'], 'UTF-8', 'UTF-8');
            $mistake['category'] = preg_replace('/[^\w\s]/u', '', $mistake['category']);
            $mistake['category'] = trim($mistake['category']) ?: 'Unknown';

            // Clean icon and color class
            $mistake['icon'] = trim($mistake['icon']);
            $mistake['color_class'] = trim($mistake['color_class']);

            // Ensure icon has proper Font Awesome format
            if (empty($mistake['icon']) || !preg_match('/^fa[srb]?\s+fa-/', $mistake['icon'])) {
                $mistake['icon'] = 'fas fa-question-circle'; // fallback
            }

            // Validate color class
            $validColors = ['red', 'blue', 'green', 'yellow', 'purple', 'indigo', 'pink', 'gray', 'orange'];
            if (!in_array($mistake['color_class'], $validColors)) {
                $mistake['color_class'] = 'gray';
            }
        }

        return $this->response
            ->setContentType('application/json; charset=utf-8')
            ->setJSON($recentMistakes, JSON_UNESCAPED_UNICODE);
    }

    public function getMistakeDetails()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $mistakeId = $this->request->getGet('id');
        if (!$mistakeId) {
            return $this->response->setJSON(['error' => 'Mistake ID required']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Get mistake details
        $mistake = $db->query("
            SELECT * FROM mistakes WHERE id = ?
        ", [$mistakeId])->getRow();

        if (!$mistake) {
            return $this->response->setJSON(['error' => 'Mistake not found']);
        }

        // Get frequency for this user
        $frequency = $db->query("
            SELECT COUNT(*) as count 
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND tm.mistake_id = ? AND t.deleted_at IS NULL
        ", [$userId, $mistakeId])->getRow()->count ?? 0;

        // Get last occurrence
        $lastOccurrence = $db->query("
            SELECT MAX(t.datetime) as last_time 
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND tm.mistake_id = ? AND t.deleted_at IS NULL
        ", [$userId, $mistakeId])->getRow()->last_time;

        return $this->response->setJSON([
            'mistake' => $mistake,
            'frequency' => $frequency,
            'lastOccurrence' => $lastOccurrence
        ]);
    }

    public function getMistakeHeatmap()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Get mistake frequency by day for the last 5 weeks
        $startDate = date('Y-m-d', strtotime('-5 weeks monday'));
        
        $heatmapData = $db->query("
            SELECT 
                DATE(t.datetime) as date,
                DAYOFWEEK(t.datetime) as day_of_week,
                COUNT(*) as mistake_count
            FROM trade_mistakes tm 
            JOIN trades t ON tm.trade_id = t.id 
            WHERE t.user_id = ? AND t.deleted_at IS NULL 
            AND DATE(t.datetime) >= ?
            GROUP BY DATE(t.datetime), DAYOFWEEK(t.datetime)
            ORDER BY date
        ", [$userId, $startDate])->getResultArray();

        return $this->response->setJSON($heatmapData);
    }

    public function getCustomMistakes()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Get user's custom mistakes
        $customMistakes = $db->query("
            SELECT id, name, category, description, severity, impact, icon, color_class, created_at
            FROM mistakes
            WHERE user_id = ? AND is_active = 1
            ORDER BY created_at DESC
        ", [$userId])->getResultArray();

        return $this->response->setJSON($customMistakes);
    }

    public function createCustomMistake()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Get form data
        $name = trim($this->request->getPost('name'));
        $category = trim($this->request->getPost('category'));

        // Validate required fields
        if (empty($name) || empty($category)) {
            return $this->response->setJSON(['error' => 'Name and category are required']);
        }

        // Validate category values
        $validCategories = ['Cognitive', 'Psychological', 'Behavioral'];

        if (!in_array($category, $validCategories)) {
            return $this->response->setJSON(['error' => 'Invalid category']);
        }

        // Set default values for simplified form
        $description = 'Custom user mistake';
        $severity = 'Medium';
        $impact = 'Moderate';
        $icon = 'fas fa-exclamation-circle';
        $colorClass = 'blue';

        // Check if user already has a mistake with this name
        $existingMistake = $db->query("
            SELECT id FROM mistakes
            WHERE user_id = ? AND name = ? AND is_active = 1
        ", [$userId, $name])->getRow();

        if ($existingMistake) {
            return $this->response->setJSON(['error' => 'You already have a mistake with this name']);
        }

        try {
            // Insert custom mistake
            $insertData = [
                'user_id' => $userId,
                'name' => $name,
                'category' => $category,
                'description' => $description,
                'severity' => $severity,
                'impact' => $impact,
                'icon' => $icon,
                'color_class' => $colorClass,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->table('mistakes')->insert($insertData);
            $mistakeId = $db->insertID();

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Custom mistake created successfully',
                'mistake_id' => $mistakeId
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON(['error' => 'Failed to create custom mistake: ' . $e->getMessage()]);
        }
    }

    public function updateCustomMistake($mistakeId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Check if mistake belongs to user
        $mistake = $db->query("
            SELECT id FROM mistakes
            WHERE id = ? AND user_id = ? AND is_active = 1
        ", [$mistakeId, $userId])->getRow();

        if (!$mistake) {
            return $this->response->setJSON(['error' => 'Mistake not found or access denied']);
        }

        // Get form data
        $name = trim($this->request->getPost('name'));
        $category = trim($this->request->getPost('category'));

        // Validate required fields
        if (empty($name) || empty($category)) {
            return $this->response->setJSON(['error' => 'Name and category are required']);
        }

        // Validate category values
        $validCategories = ['Cognitive', 'Psychological', 'Behavioral'];

        if (!in_array($category, $validCategories)) {
            return $this->response->setJSON(['error' => 'Invalid category']);
        }

        try {
            // Update custom mistake
            $updateData = [
                'name' => $name,
                'category' => $category,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->table('mistakes')->where('id', $mistakeId)->update($updateData);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Custom mistake updated successfully'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON(['error' => 'Failed to update custom mistake: ' . $e->getMessage()]);
        }
    }

    public function deleteCustomMistake($mistakeId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['error' => 'Unauthorized']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Check if mistake belongs to user
        $mistake = $db->query("
            SELECT id FROM mistakes
            WHERE id = ? AND user_id = ? AND is_active = 1
        ", [$mistakeId, $userId])->getRow();

        if (!$mistake) {
            return $this->response->setJSON(['error' => 'Mistake not found or access denied']);
        }

        try {
            // Soft delete the custom mistake
            $db->table('mistakes')->where('id', $mistakeId)->update([
                'is_active' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Custom mistake deleted successfully'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON(['error' => 'Failed to delete custom mistake: ' . $e->getMessage()]);
        }
    }

    public function getMistakeDistribution()
    {
        try {
            $period = $this->request->getGet('period') ?? 'month'; // 'month' or 'all'

            // Get all mistakes (predefined + custom)
            $predefinedMistakes = [
                ['name' => 'Risked Too Much', 'count' => 15, 'color' => 'red'],
                ['name' => 'Exited Too Late', 'count' => 12, 'color' => 'orange'],
                ['name' => 'FOMO Entry', 'count' => 10, 'color' => 'yellow'],
                ['name' => 'Exited Too Early', 'count' => 8, 'color' => 'blue'],
                ['name' => 'Overtrading', 'count' => 7, 'color' => 'purple'],
                ['name' => 'Ignored Signals', 'count' => 5, 'color' => 'green'],
                ['name' => 'Ignored Stop Loss', 'count' => 4, 'color' => 'pink'],
                ['name' => 'No Clear Plan', 'count' => 3, 'color' => 'indigo'],
                ['name' => 'Emotional Trading', 'count' => 2, 'color' => 'gray']
            ];

            // Adjust counts based on period
            if ($period === 'all') {
                foreach ($predefinedMistakes as &$mistake) {
                    $mistake['count'] = intval($mistake['count'] * 1.5); // Increase for "all time"
                }
            }

            // Get custom mistakes - simplified for now to avoid database issues
            $customMistakes = [];
            // TODO: Add custom mistakes from database when ready
            /*
            try {
                $db = \Config\Database::connect();
                $userId = session()->get('user_id');

                if ($userId) {
                    $customMistakeRecords = $db->table('mistakes')
                        ->where('user_id', $userId)
                        ->where('is_active', 1)
                        ->get()
                        ->getResultArray();

                    foreach ($customMistakeRecords as $mistake) {
                        $customMistakes[] = [
                            'name' => $mistake['name'],
                            'count' => rand(1, 8),
                            'color' => $mistake['color_class'] ?? 'blue'
                        ];
                    }
                }
            } catch (\Exception $e) {
                error_log('Failed to load custom mistakes: ' . $e->getMessage());
            }
            */

            // Combine and sort by count
            $allMistakes = array_merge($predefinedMistakes, $customMistakes);
            usort($allMistakes, function($a, $b) {
                return $b['count'] - $a['count'];
            });

            // Get top 7 mistakes
            $topMistakes = array_slice($allMistakes, 0, 7);

            // Calculate total for percentage
            $totalCount = array_sum(array_column($topMistakes, 'count'));

            // Add percentage to each mistake
            foreach ($topMistakes as &$mistake) {
                $mistake['percentage'] = $totalCount > 0 ? round(($mistake['count'] / $totalCount) * 100, 1) : 0;
            }

            return $this->response->setJSON([
                'mistakes' => $topMistakes,
                'total' => $totalCount,
                'period' => $period
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('Mistake Distribution Error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());

            return $this->response->setJSON([
                'error' => 'Failed to load mistake distribution: ' . $e->getMessage(),
                'debug' => ENVIRONMENT === 'development' ? $e->getTraceAsString() : null
            ]);
        }
    }
}
