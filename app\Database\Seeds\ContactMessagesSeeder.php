<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class ContactMessagesSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'subject' => 'support',
                'message' => 'I am having trouble with my account login. Can you please help me reset my password?',
                'status' => 'pending',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'subject' => 'feedback',
                'message' => 'I love the new features you have added to the platform. The user interface is much more intuitive now.',
                'status' => 'read',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'subject' => 'partnership',
                'message' => 'We are interested in exploring a partnership opportunity with your company. Could we schedule a meeting?',
                'status' => 'replied',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'name' => 'Sarah Wilson',
                'email' => '<EMAIL>',
                'subject' => 'support',
                'message' => 'The mobile app keeps crashing when I try to upload documents. This is very frustrating.',
                'status' => 'pending',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'subject' => 'other',
                'message' => 'I would like to know more about your pricing plans and available features for enterprise customers.',
                'status' => 'read',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-12 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-10 hours'))
            ],
            [
                'name' => 'Lisa Garcia',
                'email' => '<EMAIL>',
                'subject' => 'feedback',
                'message' => 'The recent update has some bugs. The dashboard is not loading properly and some buttons are not working.',
                'status' => 'pending',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
            ],
            [
                'name' => 'Robert Taylor',
                'email' => '<EMAIL>',
                'subject' => 'support',
                'message' => 'I cannot access my trading history. The page shows an error message every time I try to load it.',
                'status' => 'replied',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'subject' => 'partnership',
                'message' => 'Our company specializes in financial analytics and we believe there could be synergies between our platforms.',
                'status' => 'read',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
            ],
            [
                'name' => 'Chris Anderson',
                'email' => '<EMAIL>',
                'subject' => 'other',
                'message' => 'Is there an API available for third-party integrations? We would like to integrate your services with our existing system.',
                'status' => 'pending',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'name' => 'Amanda White',
                'email' => '<EMAIL>',
                'subject' => 'support',
                'message' => 'The email notifications are not working. I am not receiving any alerts for my trades.',
                'status' => 'closed',
                'ip_address' => '************',
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-4 days'))
            ]
        ];

        // Using Query Builder
        $this->db->table('contact_messages')->insertBatch($data);
    }
}
