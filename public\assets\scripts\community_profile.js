$(document).ready(function() {
    // Tab switching functionality
    const tabs = {
        postsTab: document.getElementById('postsTab'),
        followersTab: document.getElementById('followersTab'),
        followingTab: document.getElementById('followingTab')
    };
            
    const contents = {
        postsContent: document.getElementById('postsContent'),
        followersContent: document.getElementById('followersContent'),
        followingContent: document.getElementById('followingContent')
    };
            
    function switchTab(activeTab) {
        // Reset all tabs
        Object.values(tabs).forEach(tab => {
            tab.classList.remove('text-indigo-400', 'border-b-2', 'border-indigo-400');
            tab.classList.add('text-gray-400');
        });
                
        // Hide all contents
        Object.values(contents).forEach(content => {
            if (content) content.classList.add('hidden');
        });
                
        // Activate selected tab
        tabs[activeTab].classList.add('text-indigo-400', 'border-b-2', 'border-indigo-400');
        tabs[activeTab].classList.remove('text-gray-400');
                
        // Show corresponding content
        const contentId = activeTab.replace('Tab', 'Content');
        if (contents[contentId]) {
            contents[contentId].classList.remove('hidden');
        }

        // Load content based on tab
        switch(activeTab) {
            case 'postsTab':
                loadUserPosts();
                break;
            case 'followersTab':
                loadFollowers();
                break;
            case 'followingTab':
                loadFollowing();
                break;
        }
    }
            
    // Add event listeners for tabs
    Object.keys(tabs).forEach(tabId => {
        if (tabs[tabId]) {
            tabs[tabId].addEventListener('click', () => switchTab(tabId));
        }
    });
            
    // Modal functionality
    const editBtn = document.getElementById('editBtn');
    const editModal = document.getElementById('editModal');
    const closeModal = document.getElementById('closeModal');
    const profileForm = document.getElementById('profileForm');
    
    // Profile elements
    const profileName = document.getElementById('profileName');
    const profileBio = document.getElementById('profileBio');
    const profileTradingStyle = document.getElementById('profileTradingStyle');
    
    // Edit form elements
    const editName = document.getElementById('editName');
    const editBio = document.getElementById('editBio');
    const editBadge = document.getElementById('editBadge');
    
    // Badge configuration
    const badgeConfig = {
        'pro': { icon: 'fas fa-crown', color: 'text-yellow-400', label: 'Pro Trader' },
        'gainer': { icon: 'fas fa-chart-line', color: 'text-green-400', label: 'Top Gainer' },
        'master': { icon: 'fas fa-star', color: 'text-purple-400', label: 'Master Trader' },
        'analyst': { icon: 'fas fa-brain', color: 'text-blue-400', label: 'Market Analyst' },
        'none': { icon: 'fas fa-user', color: 'text-gray-400', label: 'Community Member' }
    };
    
    // Open modal
    editBtn.addEventListener('click', () => {
        editModal.classList.remove('hidden');
        setTimeout(() => {
            editModal.classList.add('modal-enter-active');
        }, 10);
    });
    
    // Close modal
    closeModal.addEventListener('click', () => {
        editModal.classList.remove('modal-enter-active');
        setTimeout(() => {
            editModal.classList.add('hidden');
        }, 300);
    });

    // Close modal when clicking outside
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) {
            closeModal.click();
        }
    });
    
    // Save profile changes
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const saveBtn = document.getElementById('saveProfile');
        const originalText = saveBtn.innerHTML;
        
        // Show loading state
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        saveBtn.disabled = true;
        
        $.ajax({
            url: base_url + 'community/updateProfile',
            method: 'POST',
            data: {
                name: editName.value,
                bio: editBio.value,
                badge: editBadge.value
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update profile with new values
                    profileName.textContent = editName.value;
                    profileBio.textContent = editBio.value || 'Welcome to my trading journey! Sharing insights and learning from the community.';
                    
                    // Update badge
                    const selectedBadge = badgeConfig[editBadge.value];
                    profileTradingStyle.innerHTML = `
                        <i class="${selectedBadge.icon} mr-1"></i> ${selectedBadge.label}
                    `;
                    profileTradingStyle.className = `bg-gray-700 ${selectedBadge.color} px-3 py-1 rounded-full text-sm inline-block mb-4`;
                    
                    // Close modal
                    closeModal.click();
                    
                    // Show success message
                    showToast('Profile updated successfully!', 'success');
                } else {
                    showToast(response.message || 'Failed to update profile', 'error');
                }
            },
            error: function() {
                showToast('Server error occurred', 'error');
            },
            complete: function() {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }
        });
    });

    // Load user posts
    function loadUserPosts() {
        const container = document.getElementById('postsContent');
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading your posts...</p>
            </div>
        `;

        $.ajax({
            url: base_url + 'community/getMyPosts',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.posts) {
                    if (response.posts.length > 0) {
                        let postsHtml = '';
                        response.posts.forEach(post => {
                            postsHtml += createPostHTML(post);
                        });
                        container.innerHTML = postsHtml;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <i class="fas fa-pen-alt text-4xl text-gray-600 mb-4"></i>
                                <h3 class="text-xl font-semibold text-gray-400 mb-2">No posts yet</h3>
                                <p class="text-gray-500">Start sharing your trading insights with the community!</p>
                                <a href="${base_url}community" class="inline-block mt-4 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Create Your First Post
                                </a>
                            </div>
                        `;
                    }
                } else {
                    container.innerHTML = `
                        <div class="text-center py-8">
                            <p class="text-red-400">Failed to load posts</p>
                        </div>
                    `;
                }
            },
            error: function() {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-red-400">Error loading posts</p>
                    </div>
                `;
            }
        });
    }

    // Load followers
    function loadFollowers() {
        const container = document.getElementById('followersList');
        container.innerHTML = `
            <div class="text-center py-8 col-span-full">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading followers...</p>
            </div>
        `;

        $.ajax({
            url: base_url + 'community/getFollowers',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.followers) {
                    if (response.followers.length > 0) {
                        let followersHtml = '';
                        response.followers.forEach(follower => {
                            followersHtml += createUserCardHTML(follower, 'follower');
                        });
                        container.innerHTML = followersHtml;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12 col-span-full">
                                <i class="fas fa-users text-4xl text-gray-600 mb-4"></i>
                                <h3 class="text-xl font-semibold text-gray-400 mb-2">No followers yet</h3>
                                <p class="text-gray-500">Share great content to attract followers!</p>
                            </div>
                        `;
                    }
                } else {
                    container.innerHTML = `
                        <div class="text-center py-8 col-span-full">
                            <p class="text-red-400">Failed to load followers</p>
                        </div>
                    `;
                }
            },
            error: function() {
                container.innerHTML = `
                    <div class="text-center py-8 col-span-full">
                        <p class="text-red-400">Error loading followers</p>
                    </div>
                `;
            }
        });
    }

    // Load following
    function loadFollowing() {
        const container = document.getElementById('followingList');
        container.innerHTML = `
            <div class="text-center py-8 col-span-full">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading following...</p>
            </div>
        `;

        $.ajax({
            url: base_url + 'community/getFollowing',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.following) {
                    if (response.following.length > 0) {
                        let followingHtml = '';
                        response.following.forEach(user => {
                            followingHtml += createUserCardHTML(user, 'following');
                        });
                        container.innerHTML = followingHtml;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12 col-span-full">
                                <i class="fas fa-user-friends text-4xl text-gray-600 mb-4"></i>
                                <h3 class="text-xl font-semibold text-gray-400 mb-2">Not following anyone yet</h3>
                                <p class="text-gray-500">Discover and follow interesting traders in the community!</p>
                            </div>
                        `;
                    }
                } else {
                    container.innerHTML = `
                        <div class="text-center py-8 col-span-full">
                            <p class="text-red-400">Failed to load following</p>
                        </div>
                    `;
                }
            },
            error: function() {
                container.innerHTML = `
                    <div class="text-center py-8 col-span-full">
                        <p class="text-red-400">Error loading following</p>
                    </div>
                `;
            }
        });
    }

    // Create user card HTML for followers/following
    function createUserCardHTML(user, type) {
        const badgeConfig = {
            'pro': { icon: 'fas fa-crown', color: 'text-yellow-400' },
            'gainer': { icon: 'fas fa-chart-line', color: 'text-green-400' },
            'master': { icon: 'fas fa-star', color: 'text-purple-400' },
            'analyst': { icon: 'fas fa-brain', color: 'text-blue-400' },
            'none': { icon: 'fas fa-user', color: 'text-gray-400' }
        };

        const userBadge = badgeConfig[user.badge || 'none'];
        const joinDate = new Date(user.created_at).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        const followDate = new Date(user.followed_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

        return `
            <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                <div class="flex items-center space-x-3 mb-3">
                    <img src="${user.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User" class="w-12 h-12 rounded-full">
                    <div class="flex-1 min-w-0">
                        <div class="font-bold truncate flex items-center">
                            ${user.full_name}
                            <i class="${userBadge.icon} ${userBadge.color} ml-1 text-sm"></i>
                        </div>
                        <div class="text-gray-400 text-sm truncate">@${user.full_name.toLowerCase().replace(/\s+/g, '_')}</div>
                    </div>
                </div>
                <div class="flex justify-between text-sm mb-2">
                    <div class="text-gray-400">Member since:</div>
                    <div>${joinDate}</div>
                </div>
                <div class="flex justify-between text-sm mb-3">
                    <div class="text-gray-400">${type === 'follower' ? 'Following since:' : 'Followed on:'}</div>
                    <div>${followDate}</div>
                </div>
                <div class="flex space-x-2">
                    <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center group-hover:bg-indigo-600 group-hover:text-white">
                        Message
                    </button>
                    ${type === 'following' ?
                        `<button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-full text-sm transition-colors">
                            Following
                        </button>` :
                        `<button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-green-500">
                            <i class="fas fa-user-plus text-xs"></i>
                        </button>`
                    }
                </div>
            </div>
        `;
    }

    // Create post HTML
    function createPostHTML(post) {
        const timeAgo = getTimeAgo(post.created_at);
        const postTypeColors = {
            'setup': 'text-purple-400',
            'pnl': 'text-green-400',
            'analysis': 'text-blue-400',
            'educational': 'text-yellow-400',
            'general': 'text-gray-400'
        };

        return `
            <div class="bg-gray-800 rounded-xl p-6 shadow-lg hover:glow-effect transition-all">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-3">
                        <img src="${post.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User" class="w-10 h-10 rounded-full">
                        <div>
                            <div class="font-bold">${post.full_name}</div>
                            <div class="text-gray-400 text-sm">${timeAgo}</div>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <span class="bg-gray-700 ${postTypeColors[post.post_type] || 'text-gray-400'} px-2 py-1 rounded text-xs">
                            ${post.post_type.charAt(0).toUpperCase() + post.post_type.slice(1)}
                        </span>
                    </div>
                </div>
                <div class="mb-4">
                    <h3 class="font-bold text-lg mb-2">${post.title}</h3>
                    <p class="text-gray-300">${post.content}</p>
                    ${post.image_url ? `<img src="${post.image_url}" alt="Post image" class="mt-3 rounded-lg max-w-full h-auto">` : ''}
                </div>
                <div class="flex justify-between items-center text-gray-400 text-sm">
                    <div class="flex space-x-4">
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-thumbs-up"></i>
                            <span>${post.likes_count || 0}</span>
                        </button>
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-comment"></i>
                            <span>${post.comments_count || 0}</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Utility function to get time ago
    function getTimeAgo(dateString) {
        const now = new Date();
        const postDate = new Date(dateString);
        const diffInSeconds = Math.floor((now - postDate) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        return Math.floor(diffInSeconds / 2592000) + ' months ago';
    }

    // Toast notification function
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        const bgColor = type === 'success' ? 'bg-green-600' : type === 'error' ? 'bg-red-600' : 'bg-blue-600';
        const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
        
        toast.className = `fixed bottom-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 z-50`;
        toast.innerHTML = `
            <i class="fas ${icon}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    }
            
    // Initialize with posts tab active
    switchTab('postsTab');
});

// Navigation function for sidebar links
function navigateToSection(section) {
    // Redirect to community page with section
    window.location.href = base_url + 'community#' + section;
}
