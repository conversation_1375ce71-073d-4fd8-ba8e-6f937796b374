<?php

namespace App\Libraries;

use Razorpay\Api\Api;

class MyRazorpay {
    protected $api;

    public function __construct() {
        $this->api = new Api(
            env('RAZORPAY_KEY_ID'),
            env('RAZORPAY_KEY_SECRET')
        );
    }

    public function createOrder($amount, $receipt, $currency = 'INR') {
        try {
            return $this->api->order->create([
                'amount'   => $amount * 100, // Convert to paise
                'currency' => $currency,
                'receipt'  => $receipt
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Razorpay Error: '.$e->getMessage());
            return false;
        }
    }

    public function verifyPayment($attributes) {
        try {
            $this->api->utility->verifyPaymentSignature($attributes);
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Payment Verification Failed: '.$e->getMessage());
            return false;
        }
    }
}