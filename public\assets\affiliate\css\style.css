* {
    font-family: 'Poppins';
}

/* Custom styles for scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Blur effect for sensitive data */
.blur-text {
    filter: blur(4px);
    transition: filter 0.3s ease;
}

.blur-text:hover {
    filter: blur(0);
}

/* Sidebar transition */
.sidebar {
    transition: all 0.3s ease;
}

/* Mobile menu button */
.mobile-menu-button {
    display: none;
}

@media (max-width: 768px) {
    .mobile-menu-button {
        display: block;
    }

    .sidebar {
        position: absolute;
        z-index: 50;
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
        height: 100%;
    }

    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
    }

    .sidebar-overlay.open {
        display: block;
    }
}

.progress-bar {
    height: 8px;
    border-radius: 4px;
    background-color: #e5e7eb;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    background-color: #4f46e5;
    width: 25%;
}

.tier-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.tier-bronze {
    background-color: #cd7f32;
}

.tier-silver {
    background-color: #c0c0c0;
}

.tier-gold {
    background-color: #ffd700;
}

.bank-input:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.popup-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 500px;
    animation: popupIn 0.3s ease-out forwards;
}

@keyframes popupIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.accordion-content.active {
    max-height: 500px;
    transition: max-height 0.3s ease-in;
}

.feature-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #3B82F6;
    color: white;
    border-radius: 9999px;
    padding: 2px 8px;
    font-size: 0.75rem;
    font-weight: bold;
}