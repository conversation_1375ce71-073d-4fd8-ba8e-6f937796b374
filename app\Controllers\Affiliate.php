<?php

namespace App\Controllers;

use \App\Models\UserModel;

helper('cookie');

class Affiliate extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
    }

    private function generateReferralCode($name)
    {
        // Clean and lowercase the name
        $cleanName = strtolower(preg_replace('/[^a-zA-Z]/', '', $name));

        // Pad the name with "x" if less than 4 characters
        $prefix = str_pad(substr($cleanName, 0, 4), 4, 'x');

        // Generate a random 4-digit number
        $randomNumber = rand(1000, 9999);

        // Combine and return uppercase code
        return strtoupper($prefix . $randomNumber); // e.g., ALEx5732 or JOxX2183
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $user = $this->usermodel->find($userId);

        // Generate and save referral code if not present
        if (empty($user['refer_code'])) {
            $newReferCode = $this->generateReferralCode($user['full_name']);
            $this->usermodel->update($userId, ['refer_code' => $newReferCode]);
            $user['refer_code'] = $newReferCode; // update locally too
        }

        $referCode = $user['refer_code'];

        // ---- Referral Metrics ----
        $referralsTable = $db->table('referrals');

        // Total referrals
        $totalReferrals = $referralsTable
            ->where('referrer_id', $userId)
            ->countAllResults();

        // Commission tier with influencer check
        if (!empty($user['is_influencer']) && $user['is_influencer'] == 1) {
            $commissionRate = 20;
        } else {
            if ($totalReferrals <= 50) {
                $commissionRate = 10;
            } elseif ($totalReferrals <= 100) {
                $commissionRate = 15;
            } else {
                $commissionRate = 20;
            }
        }

        // Lifetime earnings and revenue
        $lifetime = $referralsTable
            ->where('referrer_id', $userId)
            ->selectSum('reward_amount')
            ->selectSum('subscription_amount')
            ->get()
            ->getRowArray();

        $lifetimeEarnings = $lifetime['reward_amount'] ?? 0;
        $lifetimeRevenue = $lifetime['subscription_amount'] ?? 0;

        // Last 7 days
        $last7 = $referralsTable
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-7 days')))
            ->selectSum('reward_amount')
            ->selectSum('subscription_amount')
            ->get()
            ->getRowArray();

        $last7Earnings = $last7['reward_amount'] ?? 0;
        $last7Revenue = $last7['subscription_amount'] ?? 0;

        $last7Referrals = $referralsTable
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-7 days')))
            ->countAllResults();

        // Last 30 days
        $last30 = $referralsTable
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
            ->selectSum('reward_amount')
            ->selectSum('subscription_amount')
            ->get()
            ->getRowArray();

        $last30Earnings = $last30['reward_amount'] ?? 0;
        $last30Revenue = $last30['subscription_amount'] ?? 0;

        $last30Referrals = $referralsTable
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
            ->countAllResults();

        // ---- Pass to View ----
        $data = [
            'title' => 'Affiliate Dashboard',
            'active' => 'dashboard',
            'userDetails' => $user,
            'customScript' => 'dashboard',
            'main_content' => 'affiliate/dashboard',

            'commissionRate' => $commissionRate,
            'affiliateCode' => $referCode,
            'lifetimeEarnings' => $lifetimeEarnings,
            'lifetimeRevenue' => $lifetimeRevenue,

            'last7Earnings' => $last7Earnings,
            'last7Revenue' => $last7Revenue,
            'last30Earnings' => $last30Earnings,
            'last30Revenue' => $last30Revenue,

            'totalReferrals' => $totalReferrals,
            'last7Referrals' => $last7Referrals,
            'last30Referrals' => $last30Referrals,
        ];

        return view('affiliate/includes/template', $data);
    }


    public function Sales()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $user = $this->usermodel->find($userId);

        // Total referred users
        $totalSales = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->countAllResults();

        // Total commission earned
        $lifetimeCommission = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->selectSum('reward_amount')
            ->get()
            ->getRowArray();
        $lifetimeCommission = $lifetimeCommission['reward_amount'] ?? 0;

        // Last 7 days
        $last7Sales = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-7 days')))
            ->countAllResults();

        $last7Commission = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-7 days')))
            ->selectSum('reward_amount')
            ->get()
            ->getRowArray();
        $last7Commission = $last7Commission['reward_amount'] ?? 0;

        // Last 30 days
        $last30Sales = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
            ->countAllResults();

        $last30Commission = $db->table('referrals')
            ->where('referrer_id', $userId)
            ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
            ->selectSum('reward_amount')
            ->get()
            ->getRowArray();
        $last30Commission = $last30Commission['reward_amount'] ?? 0;

        // Tier logic with influencer override
        if (!empty($user['is_influencer']) && $user['is_influencer'] == 1) {
            $tier = 'Influencer';
            $nextTier = 'N/A';
            $salesNeeded = 0;
        } elseif ($totalSales <= 50) {
            $tier = 'Bronze';
            $nextTier = 'Silver';
            $salesNeeded = 51 - $totalSales;
        } elseif ($totalSales <= 100) {
            $tier = 'Silver';
            $nextTier = 'Gold';
            $salesNeeded = 101 - $totalSales;
        } else {
            $tier = 'Gold';
            $nextTier = 'N/A';
            $salesNeeded = 0;
        }


        // ---- Pass to View ----
        $data = [
            'title' => 'Sales Dashboard',
            'active' => 'sales',
            'userDetails' => $user,
            'customScript' => 'sales',
            'main_content' => 'affiliate/sales',

            // New metrics
            'totalSales' => $totalSales,
            'lifetimeCommission' => $lifetimeCommission,
            'last7Sales' => $last7Sales,
            'last30Sales' => $last30Sales,
            'last7Commission' => $last7Commission,
            'last30Commission' => $last30Commission,
            'tier' => $tier,
            'nextTier' => $nextTier,
            'salesNeeded' => $salesNeeded
        ];

        return view('affiliate/includes/template', $data);
    }

    public function PayoutDetails()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $user = $this->usermodel->find($userId);

        $data = [
            'title' => 'Payout Details',
            'active' => 'payout',
            'userDetails' => $user,
            'customScript' => 'payout',
            'main_content' => 'affiliate/payout',
        ];

        return view('affiliate/includes/template', $data);
    }

    public function AffiliateFAQ()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $user = $this->usermodel->find($userId);

        $data = [
            'title' => 'Affiliate FAQ',
            'active' => 'faq',
            'userDetails' => $user,
            'customScript' => 'faq',
            'main_content' => 'affiliate/faq',
        ];

        return view('affiliate/includes/template', $data);
    }

    public function referrals()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $perPage = 5;
        $page = $this->request->getGet('page') ?? 1;
        $offset = ($page - 1) * $perPage;

        $builder = $db->table('referrals')->where('referrer_id', $userId);

        $total = $builder->countAllResults(false);
        $referrals = $builder->orderBy('created_at', 'DESC')
            ->limit($perPage, $offset)
            ->get()
            ->getResultArray();

        // Build HTML rows
        $rows = '';
        foreach ($referrals as $ref) {
            $rows .= '<tr>
            <td class="px-6 py-4 text-sm text-gray-700">' . date('d M Y', strtotime($ref['created_at'])) . '</td>
            <td class="px-6 py-4 text-sm text-gray-700">₹' . number_format($ref['subscription_amount'], 2) . '</td>
            <td class="px-6 py-4 text-sm text-gray-700">₹' . number_format($ref['reward_amount'], 2) . '</td>
            <td class="px-6 py-4 text-sm text-gray-700">' . ($ref['status'] ?? 'Credited to wallet') . '</td>
        </tr>';
        }

        if (empty($referrals)) {
            $rows = '<tr><td colspan="4" class="text-center py-6 text-gray-400">No referral data available.</td></tr>';
        }

        $totalPages = ceil($total / $perPage);
        $pagination = '';

        if ($totalPages > 1) {
            $pagination .= '<div class="flex flex-wrap gap-1 justify-center sm:justify-end">';

            // Previous Button (always shown, disabled if on page 1)
            $pagination .= '<button data-page="' . max(1, $page - 1) . '" class="pagination-link px-3 py-1 rounded ' .
                ($page == 1 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-gray-100 hover:bg-gray-200') . '">&lt;</button>';

            // Pages
            $visiblePages = [];
            $visiblePages[] = 1;
            if ($page > 3)
                $visiblePages[] = '...';

            for ($i = max(2, $page - 1); $i <= min($totalPages - 1, $page + 1); $i++) {
                $visiblePages[] = $i;
            }

            if ($page < $totalPages - 2)
                $visiblePages[] = '...';
            if ($totalPages > 1)
                $visiblePages[] = $totalPages;

            foreach ($visiblePages as $p) {
                if ($p === '...') {
                    $pagination .= '<span class="px-2 py-1 text-gray-400">...</span>';
                } else {
                    $active = ($p == $page) ? 'bg-blue-500 text-white' : 'bg-gray-100 hover:bg-gray-200';
                    $pagination .= '<button data-page="' . $p . '" class="pagination-link px-3 py-1 rounded ' . $active . '">' . $p . '</button>';
                }
            }

            // Next Button (always shown, disabled if on last page)
            $pagination .= '<button data-page="' . min($totalPages, $page + 1) . '" class="pagination-link px-3 py-1 rounded ' .
                ($page == $totalPages ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-gray-100 hover:bg-gray-200') . '">&gt;</button>';

            $pagination .= '</div>';
        }


        return $this->response->setJSON([
            'table' => $rows,
            'pagination' => $pagination
        ]);
    }

    public function saveBank()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request.'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data = $this->request->getJSON(true);

        $validation = \Config\Services::validation();

        $validation->setRules([
            'account_name' => 'required|min_length[3]',
            'bank_name' => 'required|min_length[3]',
            'account_number' => 'required|numeric|min_length[6]',
            'ifsc_code' => 'required|regex_match[/^[A-Z]{4}0[A-Z0-9]{6}$/]',
            'branch_name' => 'permit_empty'
        ]);

        if (!$validation->run($data)) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $validation->getErrors()
            ]);
        }

        $db = \Config\Database::connect();
        $builder = $db->table('bank_details');

        $existing = $builder->where('user_id', $userId)->get()->getRow();

        $bankData = [
            'user_id' => $userId,
            'account_name' => $data['account_name'],
            'bank_name' => $data['bank_name'],
            'account_number' => $data['account_number'],
            'ifsc_code' => strtoupper($data['ifsc_code']),
            'branch_name' => $data['branch_name']
        ];

        if ($existing) {
            $builder->where('user_id', $userId)->update($bankData);
        } else {
            $builder->insert($bankData);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Bank details saved successfully.'
        ]);
    }

    public function getBankDetails()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type.'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session')); // your existing method

        $db = \Config\Database::connect();
        $builder = $db->table('bank_details');

        $bank = $builder->where('user_id', $userId)->get()->getRowArray();

        if ($bank) {
            // Mask account number except last 4 digits
            $maskedAccNo = 'XXXXXX' . substr($bank['account_number'], -4);

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'account_name' => $bank['account_name'],
                    'bank_name' => $bank['bank_name'],
                    'account_number' => $bank['account_number'],
                    'ifsc_code' => $bank['ifsc_code'],
                    'branch_name' => $bank['branch_name'] ?? '',
                ]
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No bank details found.'
            ]);
        }
    }

    public function requestWithdraw()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $data = $this->request->getJSON(true);
        $amount = (float) $data['amount'];

        if ($amount < 100) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Minimum withdrawal amount is ₹100.'
            ]);
        }

        // Get user details
        $user = $db->table('users')->where('id', $userId)->get()->getRowArray();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found.'
            ]);
        }

        if ($user['wallet_balance'] < $amount) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient wallet balance.'
            ]);
        }

        // Generate transaction ID
        $txnId = 'WD' . date('YmdHis') . $userId;

        // Start transaction
        $db->transStart();

        // Insert withdrawal record
        $db->table('withdrawals')->insert([
            'transaction_id' => $txnId,
            'user_id' => $userId,
            'amount' => $amount,
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Deduct from wallet
        $db->table('users')
            ->where('id', $userId)
            ->set('wallet_balance', 'wallet_balance - ' . $amount, false)
            ->update();

        $db->transComplete();

        if ($db->transStatus() === false) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to submit withdrawal request. Please try again.'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully.',
            'transaction_id' => $txnId
        ]);
    }

    public function getWalletStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $user = $db->table('users')->select('wallet_balance, refer_code')->where('id', $userId)->get()->getRowArray();

        $availableBalance = $user['wallet_balance'] ?? 0;

        $pending = $db->table('withdrawals')
            ->selectSum('amount')
            ->where('user_id', $userId)
            ->where('status', 'pending')
            ->get()->getRowArray();
        $pendingPayout = $pending['amount'] ?? 0;

        $total = $db->table('referrals')
            ->selectSum('reward_amount')
            ->where('referrer_id', $userId)
            ->get()->getRowArray();
        $totalEarned = $total['reward_amount'] ?? 0;

        return $this->response->setJSON([
            'available_balance' => number_format($availableBalance, 2),
            'pending_payout' => number_format($pendingPayout, 2),
            'total_earned' => number_format($totalEarned, 2),
        ]);
    }

    public function fetchPayouts()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Unauthorized']);
        }

        $page = (int) $this->request->getGet('page') ?? 1;
        $perPage = 5;
        $offset = ($page - 1) * $perPage;

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $builder = $db->table('withdrawals')->where('user_id', $userId);
        $total = $builder->countAllResults(false); // Don't reset query
        $results = $builder->orderBy('created_at', 'DESC')->limit($perPage, $offset)->get()->getResultArray();

        $payouts = [];
        foreach ($results as $row) {
            $payouts[] = [
                'date' => date('d M Y', strtotime($row['created_at'])),
                'amount' => '₹' . number_format($row['amount'], 2),
                'status' => ucfirst($row['status']),
                'txn_id' => $row['transaction_id']
            ];
        }

        $totalPages = ceil($total / $perPage);

        return $this->response->setJSON([
            'payouts' => $payouts,
            'current_page' => $page,
            'total_pages' => $totalPages
        ]);
    }


}