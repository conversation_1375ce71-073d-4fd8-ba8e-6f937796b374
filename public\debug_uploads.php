<?php
/**
 * Upload Debug Script
 * 
 * This script helps debug upload issues in the community panel
 * Access: http://localhost:8080/debug_uploads.php
 */

echo "<h2>Community Upload Debug Information</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>\n";

// Check if uploads directory exists and is accessible
$uploadDir = __DIR__ . '/uploads/community';
$webUploadDir = '/uploads/community';

echo "<h3>Directory Status</h3>\n";
echo "<ul>\n";

// Check main uploads directory
if (is_dir(__DIR__ . '/uploads')) {
    echo "<li class='success'>✓ Main uploads directory exists</li>\n";
} else {
    echo "<li class='error'>✗ Main uploads directory missing</li>\n";
}

// Check community subdirectory
if (is_dir($uploadDir)) {
    echo "<li class='success'>✓ Community uploads directory exists</li>\n";
} else {
    echo "<li class='error'>✗ Community uploads directory missing</li>\n";
    // Try to create it
    if (mkdir($uploadDir, 0755, true)) {
        echo "<li class='success'>✓ Created community uploads directory</li>\n";
    } else {
        echo "<li class='error'>✗ Failed to create community uploads directory</li>\n";
    }
}

// Check permissions
if (is_dir($uploadDir)) {
    if (is_writable($uploadDir)) {
        echo "<li class='success'>✓ Community uploads directory is writable</li>\n";
    } else {
        echo "<li class='error'>✗ Community uploads directory is not writable</li>\n";
    }
}

echo "</ul>\n";

// Test file creation
echo "<h3>File Creation Test</h3>\n";
$testFile = $uploadDir . '/test_' . time() . '.txt';
if (is_dir($uploadDir) && file_put_contents($testFile, 'test content')) {
    echo "<p class='success'>✓ Can create files in upload directory</p>\n";
    
    // Test web access
    $testWebUrl = 'http://' . $_SERVER['HTTP_HOST'] . $webUploadDir . '/' . basename($testFile);
    echo "<p>Testing web access: <a href='$testWebUrl' target='_blank'>$testWebUrl</a></p>\n";
    
    // Clean up
    unlink($testFile);
    echo "<p class='success'>✓ Test file cleaned up</p>\n";
} else {
    echo "<p class='error'>✗ Cannot create files in upload directory</p>\n";
}

// Check existing images
echo "<h3>Existing Images</h3>\n";
if (is_dir($uploadDir)) {
    $images = glob($uploadDir . '/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
    if (!empty($images)) {
        echo "<p>Found " . count($images) . " images:</p>\n";
        echo "<ul>\n";
        foreach (array_slice($images, 0, 5) as $image) { // Show first 5
            $filename = basename($image);
            $webUrl = 'http://' . $_SERVER['HTTP_HOST'] . $webUploadDir . '/' . $filename;
            $size = filesize($image);
            echo "<li>$filename (" . number_format($size) . " bytes) - <a href='$webUrl' target='_blank'>View</a></li>\n";
        }
        echo "</ul>\n";
        if (count($images) > 5) {
            echo "<p>... and " . (count($images) - 5) . " more images</p>\n";
        }
    } else {
        echo "<p>No images found in upload directory</p>\n";
    }
}

// Server information
echo "<h3>Server Information</h3>\n";
echo "<pre>\n";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "Script Directory: " . __DIR__ . "\n";
echo "Upload Directory: " . $uploadDir . "\n";
echo "Web Upload Path: " . $webUploadDir . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "</pre>\n";

// Check .htaccess
echo "<h3>.htaccess Status</h3>\n";
$htaccessFile = __DIR__ . '/uploads/.htaccess';
if (file_exists($htaccessFile)) {
    echo "<p class='success'>✓ .htaccess file exists in uploads directory</p>\n";
} else {
    echo "<p class='warning'>⚠ .htaccess file missing in uploads directory</p>\n";
}

// Test image access
echo "<h3>Image Access Test</h3>\n";
echo "<p>Testing if images can be accessed via web:</p>\n";

// Create a test image
$testImageContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$testImageFile = $uploadDir . '/test_image.png';

if (is_dir($uploadDir) && file_put_contents($testImageFile, $testImageContent)) {
    $testImageUrl = 'http://' . $_SERVER['HTTP_HOST'] . $webUploadDir . '/test_image.png';
    echo "<p>Test image created: <a href='$testImageUrl' target='_blank'>$testImageUrl</a></p>\n";
    echo "<img src='$testImageUrl' alt='Test Image' style='border:1px solid #ccc;'>\n";
    echo "<p><small>If you can see a tiny 1x1 pixel image above, web access is working.</small></p>\n";
    
    // Clean up after 5 seconds using JavaScript
    echo "<script>setTimeout(function(){ 
        fetch('$testImageUrl', {method: 'DELETE'}).catch(function(){}); 
    }, 5000);</script>\n";
} else {
    echo "<p class='error'>✗ Could not create test image</p>\n";
}

// Recommendations
echo "<h3>Recommendations</h3>\n";
echo "<ul>\n";
echo "<li>Ensure the uploads directory has proper permissions (755)</li>\n";
echo "<li>Check that your web server can serve files from the uploads directory</li>\n";
echo "<li>Verify that ad blockers or browser extensions aren't blocking requests</li>\n";
echo "<li>Check browser console for CORS or other JavaScript errors</li>\n";
echo "<li>Test with a different browser or incognito mode</li>\n";
echo "</ul>\n";

echo "<p><strong>Note:</strong> Delete this file after debugging for security.</p>\n";
?>
