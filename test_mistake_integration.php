<?php
/**
 * Test script to verify mistake analysis integration
 * Run this script to check if all components are working correctly
 */

// Include CodeIgniter bootstrap
require_once 'vendor/autoload.php';

// Database connection test
try {
    $pdo = new PDO('mysql:host=localhost;dbname=diary', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Check if mistakes table exists and has data
echo "\n=== Testing Mistakes Table ===\n";
try {
    $stmt = $pdo->query("DESCRIBE mistakes");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✅ Mistakes table exists with columns: " . implode(', ', $columns) . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM mistakes WHERE is_active = 1");
    $count = $stmt->fetchColumn();
    echo "✅ Found $count active mistakes in database\n";
    
    if ($count == 0) {
        echo "⚠️  No mistakes found. You may need to run the setup script.\n";
    }
} catch (PDOException $e) {
    echo "❌ Mistakes table test failed: " . $e->getMessage() . "\n";
}

// Test 2: Check if trade_mistakes table exists
echo "\n=== Testing Trade Mistakes Table ===\n";
try {
    $stmt = $pdo->query("DESCRIBE trade_mistakes");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✅ Trade_mistakes table exists with columns: " . implode(', ', $columns) . "\n";
} catch (PDOException $e) {
    echo "❌ Trade_mistakes table test failed: " . $e->getMessage() . "\n";
}

// Test 3: Check if trades table has mistake_id column
echo "\n=== Testing Trades Table ===\n";
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM trades LIKE 'mistake_id'");
    $result = $stmt->fetch();
    if ($result) {
        echo "✅ Trades table has mistake_id column\n";
    } else {
        echo "❌ Trades table missing mistake_id column\n";
    }
} catch (PDOException $e) {
    echo "❌ Trades table test failed: " . $e->getMessage() . "\n";
}

// Test 4: Check if files exist
echo "\n=== Testing File Structure ===\n";
$files = [
    'app/Controllers/Mistake.php' => 'Mistake Controller',
    'app/Models/MistakeModel.php' => 'Mistake Model',
    'app/Views/pages/mistakes.php' => 'Mistake View',
    'assets/scripts/mistakes.js' => 'Mistake JavaScript',
    'app/Config/Routes.php' => 'Routes Configuration'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description exists\n";
    } else {
        echo "❌ $description missing: $file\n";
    }
}

// Test 5: Check routes configuration
echo "\n=== Testing Routes ===\n";
$routesContent = file_get_contents('app/Config/Routes.php');
if (strpos($routesContent, "Mistake::index") !== false) {
    echo "✅ Mistake routes are configured\n";
} else {
    echo "❌ Mistake routes not found in configuration\n";
}

// Test 6: Check sidebar integration
echo "\n=== Testing Sidebar Integration ===\n";
$sidebarContent = file_get_contents('app/Views/includes/sidebar.php');
if (strpos($sidebarContent, "Mistakes") !== false) {
    echo "✅ Mistakes menu item added to sidebar\n";
} else {
    echo "❌ Mistakes menu item not found in sidebar\n";
}

// Test 7: Sample data check
echo "\n=== Testing Sample Data ===\n";
try {
    $stmt = $pdo->query("SELECT name, category, severity FROM mistakes WHERE is_active = 1 LIMIT 3");
    $mistakes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($mistakes) > 0) {
        echo "✅ Sample mistakes found:\n";
        foreach ($mistakes as $mistake) {
            echo "   - {$mistake['name']} ({$mistake['category']}, {$mistake['severity']})\n";
        }
    } else {
        echo "⚠️  No sample mistakes found\n";
    }
} catch (PDOException $e) {
    echo "❌ Sample data test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Integration Test Complete ===\n";
echo "If all tests pass, you can access the mistake analysis at: /Mistakes\n";
echo "Make sure to:\n";
echo "1. Run the mistake database setup script if no mistakes are found\n";
echo "2. Add some trades with mistakes to see data in the dashboard\n";
echo "3. Check browser console for any JavaScript errors\n";
?>
