<link rel="stylesheet" href="<?= base_url() ?>assets/css/pages/goals.css?v=<?= rand() ?>">

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>

        </div>
        <div class="flex space-x-3">
            <button id="addGoalBtn"
                class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-md text-sm font-medium hover:bg-primary-dark dark:hover:bg-primary-light transition-all shadow hover:shadow-lg">
                <i class="fas fa-plus mr-2"></i> Add New Goal
            </button>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-5 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-slate-400 text-sm">Active Goals</p>
                    <p class="text-2xl font-bold text-white mt-1" id="activeGoals">...</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                    <i class="fas fa-bullseye text-blue-400"></i>
                </div>
            </div>
            <div class="hidden mt-3 pt-3 border-t border-slate-800/50 flex items-center">
                <span class="text-green-400 text-sm flex items-center">
                    <i class="fas fa-arrow-up mr-1 text-xs"></i> <span class="text-green-400 text-sm"
                        id="newGoalsThisWeek">...</span>
                </span>
            </div>
        </div>

        <div class="glass-card p-5 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-slate-400 text-sm">Completion Rate</p>
                    <p class="text-2xl font-bold text-white mt-1" id="completionRate">...</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
            </div>
            <div class="hidden mt-3 pt-3 border-t border-slate-800/50 flex items-center">
                <span class="text-green-400 text-sm flex items-center">
                    <i class="fas fa-arrow-up mr-1 text-xs"></i> 12% from last month
                </span>
            </div>
        </div>

        <div class="glass-card p-5 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-slate-400 text-sm">Current Streak</p>
                    <p class="text-2xl font-bold text-white mt-1" id="currentStreak">...</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center pulse">
                    <i class="fas fa-fire text-orange-400"></i>
                </div>
            </div>
            <div class="hidden mt-3 pt-3 border-t border-slate-800/50 flex items-center">
                <span class="text-green-400 text-sm flex items-center">
                    <i class="fas fa-trophy mr-1 text-xs"></i><span class="text-green-400 text-sm"
                        id="toBestStreak">...</span>
                </span>
            </div>
        </div>

        <div class="glass-card p-5 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-slate-400 text-sm">Avg. Daily P&L</p>
                    <p class="text-2xl font-bold text-white mt-1" id="avgDailyPnl">...</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                    <i class="fas fa-rupee-sign text-purple-400"></i>
                </div>
            </div>
            <div class="hidden mt-3 pt-3 border-t border-slate-800/50 flex items-center">
                <span class="text-green-400 text-sm flex items-center">
                    <i class="fas fa-arrow-up mr-1 text-xs"></i> 8% from target
                </span>
            </div>
        </div>
    </div>

    <!-- Goal Type Tabs -->
    <div class="hidden mb-8 overflow-x-auto">
        <div class="flex space-x-1 relative pb-1">
            <div class="tab-indicator" style="width: 100px; left: 0;"></div>
            <button class="goal-tab active px-5 py-3 text-sm font-medium text-white relative">
                <i class="fas fa-bullse-eye mr-2"></i> All Goals
            </button>
            <button class="goal-tab px-5 py-3 text-sm font-medium text-slate-300 hover:text-white">
                <i class="fas fa-rupee-sign mr-2"></i> P&L Goals
            </button>
            <button class="goal-tab px-5 py-3 text-sm font-medium text-slate-300 hover:text-white">
                <i class="fas fa-shield-alt mr-2"></i> Risk Management
            </button>
            <button class="goal-tab px-5 py-3 text-sm font-medium text-slate-300 hover:text-white">
                <i class="fas fa-brain mr-2"></i> Behavior Goals
            </button>
            <button class="goal-tab px-5 py-3 text-sm font-medium text-slate-300 hover:text-white">
                <i class="fas fa-chess-knight mr-2"></i> Execution Goals
            </button>
            <button class="goal-tab px-5 py-3 text-sm font-medium text-slate-300 hover:text-white">
                <i class="fas fa-cog mr-2"></i> Custom Goals
            </button>
        </div>
    </div>

    <!-- Goals Grid - Modern Minimalist Cards -->
    <div id="goalsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <!-- Goal Card 1 - P&L Target -->
        <div
            class="flex flex-col justify-center items-center w-full py-16 text-center text-gray-500 dark:text-gray-400 col-span-full">
            <i class="fas fa-flag-checkered text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
            <p class="text-lg font-medium">No active goals yet</p>
            <p class="text-sm">Start by creating your first goal to track your performance</p>
        </div>
    </div>



    <!-- other cards -->
    <!-- Goal Card 2 - Risk Management -->
    <div class="hidden modern-card glass-card card-risk">
        <div class="card-content">
            <div class="card-header">
                <div>
                    <span class="type-badge">
                        <i class="fas fa-shield-alt mr-1 text-xs"></i> Risk
                    </span>
                    <h3 class="text-lg font-semibold text-white mt-2">Max Daily Risk: 2%</h3>
                </div>
                <span class="status-badge status-at-risk">
                    <i class="fas fa-exclamation-triangle mr-1"></i> At risk
                </span>
            </div>

            <div class="metric-value">1.8%</div>
            <p class="text-slate-400 text-sm">Today's risk level</p>

            <div class="mt-4">
                <div class="progress-info">
                    <span>0%</span>
                    <span>90% of limit</span>
                </div>
                <div class="thick-progress">
                    <div class="thick-progress-bar" style="width: 90%"></div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <p class="stat-label">Days Compliant</p>
                    <p class="stat-value">22/30</p>
                </div>
                <div class="stat-item">
                    <p class="stat-label">Current Streak</p>
                    <p class="stat-value">5 Days</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Goal Card 3 - Behavior Goal -->
    <div class="hidden modern-card glass-card card-behavior">
        <div class="card-content">
            <div class="card-header">
                <div>
                    <span class="type-badge">
                        <i class="fas fa-brain mr-1 text-xs"></i> Behavior
                    </span>
                    <h3 class="text-lg font-semibold text-white mt-2">Journal 100% Trades</h3>
                </div>
                <span class="status-badge status-on-track">
                    <i class="fas fa-check-circle mr-1"></i> On track
                </span>
            </div>

            <div class="metric-value">92%</div>
            <p class="text-slate-400 text-sm">Completion rate this month</p>

            <div class="mt-4">
                <div class="progress-info">
                    <span>0%</span>
                    <span>46/50 trades</span>
                </div>
                <div class="thick-progress">
                    <div class="thick-progress-bar" style="width: 92%"></div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <p class="stat-label">Current Streak</p>
                    <p class="stat-value">14 Days</p>
                </div>
                <div class="stat-item">
                    <p class="stat-label">Best Streak</p>
                    <p class="stat-value">27 Days</p>
                </div>
            </div>
        </div>
    </div>



    <!-- Performance Section -->
    <div class="hidden grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
        <!-- Streak Widget -->
        <div class="glass-card p-6 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-white">Current Streaks</h3>
                <span class="text-xs font-medium px-2 py-1 rounded-full bg-blue-500/10 text-blue-400">
                    <i class="fas fa-fire mr-1"></i> Active
                </span>
            </div>

            <div class="space-y-6">
                <div class="flex items-center">
                    <div class="relative w-16 h-16 mr-4">
                        <svg width="64" height="64" viewBox="0 0 64 64">
                            <circle cx="32" cy="32" r="30" fill="none" stroke="#1e293b" stroke-width="4" />
                            <circle cx="32" cy="32" r="30" fill="none" stroke="#3b82f6" stroke-width="4"
                                stroke-linecap="round" style="--dash-offset: 188;" class="streak-ring" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold">7</span>
                        </div>
                    </div>
                    <div>
                        <p class="text-white font-medium">7 days in a row</p>
                        <p class="text-slate-400 text-sm">Followed stop loss rule</p>
                        <div class="mt-2 flex items-center text-xs text-blue-400">
                            <i class="fas fa-calendar-alt mr-1"></i> Since Jun 12
                        </div>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="relative w-16 h-16 mr-4">
                        <svg width="64" , height="64" viewBox="0 0 64 64">
                            <circle cx="32" cy="32" r="30" fill="none" stroke="#1e293b" stroke-width="4" />
                            <circle cx="32" cy="32" r="30" fill="none" , stroke="#10b981" stroke-width="4"
                                stroke-linecap="round" style="--dash-offset: 157;" class="streak-ring" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold">5</span>
                        </div>
                    </div>
                    <div>
                        <p class="text-white font-medium">5 days in a row</p>
                        <p class="text-slate-400 text-sm">Pre-market routine</p>
                        <div class="mt-2 flex items-center text-xs text-green-400">
                            <i class="fas fa-calendar-alt mr-1"></i> Since Jun 14
                        </div>
                    </div>
                </div>

                <div class="pt-4 border-t border-slate-800/50">
                    <button class="w-full text-center text-blue-400 hover:text-blue-300 text-sm font-medium">
                        View all streaks <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Completion Rings -->
        <div class="glass-card p-6 rounded-xl border border-slate-800/50">
            <h3 class="text-lg font-semibold text-white mb-6">Completion Rings</h3>
            <div class="flex justify-around mb-6">
                <div class="text-center">
                    <div class="relative w-20 h-20 mx-auto mb-3">
                        <svg width="80" , height="80" viewBox="0 0 80 80">
                            <circle cx="40" , cy="40" r="36" fill="none" stroke="#1e293b" stroke-width="8" />
                            <circle cx="40" , cy="40" r="36" fill="none" stroke="#3b82f6" stroke-width="8"
                                stroke-linecap="round" style="--dash-offset: 226;" class="streak-ring" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold text-xl">72%</span>
                        </div>
                    </div>
                    <p class="text-slate-400 text-sm font-medium">Checklist</p>
                    <p class="text-xs text-slate-500 mt-1">+8% from last week</p>
                </div>
                <div class="text-center">
                    <div class="relative w-20 h-20 mx-auto mb-3">
                        <svg width="80" , height="80" viewBox="0 0 80 80">
                            <circle cx="40" , cy="40" r="36" fill="none" stroke="#1e293b" stroke-width="8" />
                            <circle cx="40" , cy="40" r="36" fill="none" , stroke="#10b981" stroke-width="8"
                                stroke-linecap="round" style="--dash-offset: 113;" class="streak-ring" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold text-xl">64%</span>
                        </div>
                    </div>
                    <p class="text-slate-400 text-sm font-medium">Journaled</p>
                    <p class="text-xs text-slate-500 mt-1">+12% from last week</p>
                </div>
                <div class="text-center">
                    <div class="relative w-20 h-20 mx-auto mb-3">
                        <svg width="80" , height="80" viewBox="0 0 80 80">
                            <circle cx="40" , cy="40" r="36" , fill="none" , stroke="#1e293b" , stroke-width="8" />
                            <circle cx="40" , cy="40" r="36" , fill="none" , stroke="#f59e0b" , stroke-width="8" ,
                                stroke-linecap="round" style="--dash-offset: 251;" class="streak-ring" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-white font-bold text-xl">80%</span>
                        </div>
                    </div>
                    <p class="text-slate-400 text-sm font-medium">Strategy</p>
                    <p class="text-xs text-slate-500 mt-1">+5% from last week</p>
                </div>
            </div>
            <div class="pt-4 border-t border-slate-800/50">
                <button class="w-full text-center text-blue-400 hover:text-blue-300 text-sm font-medium">
                    View detailed analysis <i class="fas fa-chevron-right ml-1 text-xs"></i>
                </button>
            </div>
        </div>

        <!-- Behavioral Radar Chart -->
        <div class="glass-card p-6 rounded-xl border border-slate-800/50">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-white">Behavioral Metrics</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-xs px-2 py-1 rounded-full bg-blue-500/10 text-blue-400 flex items-center">
                        <span class="w-2 h-2 rounded-full bg-blue-400 mr-1"></span> Current
                    </span>
                    <span class="text-xs px-2 py-1 rounded-full bg-green-500/10 text-green-400 flex items-center">
                        <span class="w-2 h-2 rounded-full bg-green-400 mr-1"></span> Last Month
                    </span>
                </div>
            </div>
            <div class="h-64">
                <canvas id="behaviorRadar"></canvas>
            </div>
        </div>
    </div>

    <!-- Motivational Quote -->
    <div id="quoteElement"
        class="glass-card p-8 rounded-xl border border-slate-800/50 text-center mb-8 fade-in relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-30"></div>
        <p id="quoteText" class="text-xl italic text-slate-300 mb-2 relative z-10">"The stock market is a device for
            transferring
            money from the impatient to the patient."</p>
        <p id="quoteAuthor" class="text-slate-500 relative z-10">— Warren Buffett</p>
    </div>

    <!-- Recent Activity -->
    <div class="glass-card p-6 rounded-xl border border-slate-800/50 mb-12">
        <h3 class="text-lg font-semibold text-white mb-6">Recent Activity</h3>
        <div class="space-y-4">
            <div class="flex items-start pb-4 border-b border-slate-800/50 last:border-0 last:pb-0">
                <div class="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-check text-green-400"></i>
                </div>
                <div>
                    <p class="text-white font-medium">Completed daily trading journal</p>
                    <p class="text-slate-400 text-sm">Today at 4:32 PM</p>
                </div>
            </div>

            <div class="flex items-start pb-4 border-b border-slate-800/50 last:border-0 last:pb-0">
                <div class="w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-bullseye text-blue-400"></i>
                </div>
                <div>
                    <p class="text-white font-medium">New goal created: "Reduce overtrading"</p>
                    <p class="text-slate-400 text-sm">Yesterday at 9:15 AM</p>
                </div>
            </div>

            <div class="flex items-start pb-4 border-b border-slate-800/50 last:border-0 last:pb-0">
                <div class="w-10 h-10 rounded-full bg-yellow-500/10 flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-exclamation text-yellow-400"></i>
                </div>
                <div>
                    <p class="text-white font-medium">Approached daily risk limit (1.8%)</p>
                    <p class="text-slate-400 text-sm">Jun 15 at 2:47 PM</p>
                </div>
            </div>

            <div class="flex items-start pb-4 border-b border-slate-800/50 last:border-0 last:pb-0">
                <div class="w-10 h-10 rounded-full bg-red-500/10 flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-times text-red-400"></i>
                </div>
                <div>
                    <p class="text-white font-medium">Missed pre-market routine</p>
                    <p class="text-slate-400 text-sm">Jun 14 at 9:05 AM</p>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- modal -->
<div id="addGoalModal"
    class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 hidden backdrop-blur-sm">
    <div
        class="glass-card p-8 rounded-xl w-full max-w-md border border-slate-700/50 modal-enter transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">Add New Goal</h3>
            <button id="closeModalBtn" class="text-slate-400 hover:text-white transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>


        <!-- Step 1: Goal Type Selection -->
        <div id="step1" class="step-content">
            <h4 class="text-lg font-medium text-white mb-4">Select Goal Type</h4>
            <div class="grid grid-cols-2 gap-3 mb-6">
                <button type="button"
                    class="goal-type-option glass-card p-4 rounded-lg border border-slate-700/50 text-center transition-all"
                    data-type="pnl">
                    <div class="w-10 h-10 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-rupee-sign text-purple-400"></i>
                    </div>
                    <p class="text-white font-medium">P&L Goal</p>
                    <p class="text-slate-400 text-xs mt-1">Profit & Loss targets</p>
                </button>
                <button type="button"
                    class="goal-type-option glass-card p-4 rounded-lg border border-slate-700/50 text-center transition-all"
                    data-type="risk">
                    <div class="w-10 h-10 rounded-full bg-orange-500/10 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-shield-alt text-orange-400"></i>
                    </div>
                    <p class="text-white font-medium">Risk Management</p>
                    <p class="text-slate-400 text-xs mt-1">Risk control goals</p>
                </button>
                <button type="button"
                    class="goal-type-option glass-card p-4 rounded-lg border border-slate-700/50 text-center transition-all coming-soon"
                    data-type="behavior">
                    <div class="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-brain text-teal-400"></i>
                    </div>
                    <p class="text-white font-medium">Behavior Goal</p>
                    <p class="text-slate-400 text-xs mt-1">Trading psychology</p>
                </button>
                <button type="button"
                    class="goal-type-option glass-card p-4 rounded-lg border border-slate-700/50 text-center transition-all coming-soon"
                    data-type="execution">
                    <div class="w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chess-knight text-blue-400"></i>
                    </div>
                    <p class="text-white font-medium">Execution Goal</p>
                    <p class="text-slate-400 text-xs mt-1">Trade execution</p>
                </button>
            </div>
            <div class="flex justify-end">
                <button type="button" id="nextStep1"
                    class="px-5 py-2.5 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 transition-all shadow-lg hover:shadow-xl">
                    Next <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </div>

        <!-- Step 2: P&L Goal Details (Hidden by default) -->
        <div id="step2-pnl" class="step-content hidden">
            <h4 class="text-lg font-medium text-white mb-4">P&L Goal Details</h4>

            <form action="#" method="POST" id="pnlGoalForm" enctype="multipart/form-data">
                <div class="mb-4">
                    <label class="block text-slate-400 text-sm mb-2 font-medium">Goal Name</label>
                    <input type="text" data-inputname="Goal Name"
                        class="gReq w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., Monthly P&L Target" name="goal_name" id="goal_name">
                </div>

                <div class="mb-4">
                    <label class="block text-slate-400 text-sm mb-2 font-medium">Timeframe</label>
                    <input type="hidden" class="gReq" name="timeframe" id="timeframe" value="monthly"
                        data-inputname="Timeframe">
                    <div class="grid grid-cols-3 gap-2">
                        <button type="button"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                            data-timeframe="weekly">
                            Weekly
                        </button>
                        <button type="button" id="monthlyBtn"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm timeframe-option selected"
                            data-timeframe="monthly">
                            Monthly
                        </button>
                        <button type="button"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                            data-timeframe="quarterly">
                            3 Months
                        </button>
                        <button type="button"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                            data-timeframe="halfyear">
                            6 Months
                        </button>
                        <button type="button"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                            data-timeframe="yearly">
                            Yearly
                        </button>
                        <button type="button"
                            class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                            data-timeframe="custom" id="customTimeframeBtn">
                            Custom
                        </button>
                    </div>
                    <div class="custom-timeframe-input mt-2" id="customTimeframeInput">
                        <div class="flex items-center space-x-2">
                            <input type="number" min="1" id="custom_number" name="custom_number"
                                data-inputname="Custom timeframe"
                                class="flex-1 bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Number">
                            <select id="custom_period" name="custom_period" data-inputname="Custom timeframe period"
                                class="flex-1 bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="days">Days</option>
                                <option value="weeks">Weeks</option>
                                <option value="months">Months</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-slate-400 text-sm mb-2 font-medium">P&L Goal Type</label>
                    <div class="space-y-2">
                        <label class="flex items-center space-x-3 py-2 px-3 rounded-lg border border-slate-700/50">
                            <input type="radio" name="goal_type" class="custom-radio" value="exact" checked>
                            <span class="text-white">Exact P&L Target</span>
                        </label>
                        <!-- <label class="flex items-center space-x-3 py-2 px-3 rounded-lg border border-slate-700/50">
                            <input type="radio" name="goal_type" class="custom-radio" value="highest">
                            <span class="text-white">Highest P&L Achieved</span>
                        </label> -->
                        <label class="flex items-center space-x-3 py-2 px-3 rounded-lg border border-slate-700/50">
                            <input type="radio" name="goal_type" class="custom-radio" value="average">
                            <span class="text-white">Average Daily P&L</span>
                        </label>
                        <label class="flex items-center space-x-3 py-2 px-3 rounded-lg border border-slate-700/50">
                            <input type="radio" name="goal_type" class="custom-radio" value="winrate">
                            <span class="text-white">Win Rate Percentage</span>
                        </label>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-slate-400 text-sm mb-2 font-medium">Target Value</label>
                    <div class="relative">
                        <input type="number" id="targetValueInput" name="target_value" data-inputname="Target value"
                            class="gReq w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., ₹50,000">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <span class="text-slate-500" id="targetValueSymbol">₹</span>
                        </div>
                    </div>
                </div>
            </form>

            <div class="flex justify-between">
                <button type="button"
                    class="prev-step px-5 py-2.5 rounded-lg border border-slate-700/50 text-slate-300 hover:bg-slate-800/50 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i> Back
                </button>
                <button type="button" onclick="submitPnlGoalForm('<?= base_url('submitPnlGoalForm') ?>')"
                    class="px-5 py-2.5 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 transition-all shadow-lg hover:shadow-xl">
                    Create Goal
                </button>
            </div>
        </div>

        <!-- Step 2: Risk Management Goal Details -->
        <div id="step2-risk" class="step-content hidden">
            <h4 class="text-lg font-medium text-white mb-4">Risk Management Goal</h4>

            <div class="mb-4">
                <label class="block text-slate-400 text-sm mb-2 font-medium">Risk Goal Type</label>
                <div class="grid grid-cols-2 gap-3">
                    <button type="button"
                        class="risk-type-option glass-card p-3 rounded-lg border border-slate-700/50 text-center transition-all"
                        data-risk-type="overtrading">
                        <div
                            class="w-8 h-8 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-exchange-alt text-purple-400"></i>
                        </div>
                        <p class="text-white text-sm font-medium">Overtrading Limit</p>
                    </button>
                    <button type="button"
                        class="risk-type-option glass-card p-3 rounded-lg border border-slate-700/50 text-center transition-all"
                        data-risk-type="daily-loss">
                        <div class="w-8 h-8 rounded-full bg-red-500/10 flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-arrow-down text-red-400"></i>
                        </div>
                        <p class="text-white text-sm font-medium">Daily Loss Limit</p>
                    </button>
                    <button type="button"
                        class="risk-type-option glass-card p-3 rounded-lg border border-slate-700/50 text-center transition-all"
                        data-risk-type="weekly-loss">
                        <div
                            class="w-8 h-8 rounded-full bg-orange-500/10 flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-calendar-week text-orange-400"></i>
                        </div>
                        <p class="text-white text-sm font-medium">Weekly Loss Limit</p>
                    </button>
                    <button type="button"
                        class="risk-type-option glass-card p-3 rounded-lg border border-slate-700/50 text-center transition-all"
                        data-risk-type="stop-loss">
                        <div class="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-hand-paper text-blue-400"></i>
                        </div>
                        <p class="text-white text-sm font-medium">Stop Loss Discipline</p>
                    </button>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-slate-400 text-sm mb-2 font-medium">Goal Name</label>
                <input type="text"
                    class="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Max 3 Trades Per Day">
            </div>

            <div class="mb-4">
                <label class="block text-slate-400 text-sm mb-2 font-medium">Measurement</label>
                <div class="flex items-center space-x-4">
                    <div class="flex-1">
                        <select
                            class="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>Fixed Amount</option>
                            <option>Percentage of Capital</option>
                            <option>Number of Trades</option>
                            <option>R Multiple</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <input type="text"
                            class="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Value">
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-slate-400 text-sm mb-2 font-medium">Timeframe</label>
                <div class="grid grid-cols-2 gap-2">
                    <button type="button"
                        class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm timeframe-option selected"
                        data-timeframe="daily">
                        Daily
                    </button>
                    <button type="button"
                        class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                        data-timeframe="weekly">
                        Weekly
                    </button>
                    <button type="button"
                        class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                        data-timeframe="monthly">
                        Monthly
                    </button>
                    <button type="button"
                        class="timeframe-option py-2 px-3 rounded-lg border border-slate-700/50 text-center text-sm"
                        data-timeframe="continuous">
                        Continuous
                    </button>
                </div>
            </div>

            <div class="mb-6">
                <label class="block text-slate-400 text-sm mb-2 font-medium">Alert Threshold</label>
                <div class="flex items-center">
                    <input type="range" min="0" max="100" value="80"
                        class="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer">
                    <span class="ml-3 text-white text-sm font-medium">80%</span>
                </div>
                <p class="text-slate-500 text-xs mt-1">Receive alerts when approaching your limit</p>
            </div>

            <div class="flex justify-between">
                <button type="button"
                    class="prev-step px-5 py-2.5 rounded-lg border border-slate-700/50 text-slate-300 hover:bg-slate-800/50 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i> Back
                </button>
                <button type="submit"
                    class="px-5 py-2.5 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 transition-all shadow-lg hover:shadow-xl">
                    Create Goal
                </button>
            </div>
        </div>

        <!-- Step 2: Behavior Goal (Coming Soon) -->
        <div id="step2-behavior" class="step-content hidden">
            <div class="text-center py-12">
                <div class="w-20 h-20 rounded-full bg-teal-500/10 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-brain text-teal-400 text-2xl"></i>
                </div>
                <h4 class="text-xl font-semibold text-white mb-2">Behavior Goals Coming Soon</h4>
                <p class="text-slate-400 mb-6 max-w-md mx-auto">We're working hard to bring you powerful tools
                    to track and improve your trading psychology and habits.</p>
                <button type="button"
                    class="prev-step px-5 py-2.5 rounded-lg border border-slate-700/50 text-slate-300 hover:bg-slate-800/50 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Goal Types
                </button>
            </div>
        </div>

        <!-- Step 2: Execution Goal (Coming Soon) -->
        <div id="step2-execution" class="step-content hidden">
            <div class="text-center py-12">
                <div class="w-20 h-20 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-chess-knight text-blue-400 text-2xl"></i>
                </div>
                <h4 class="text-xl font-semibold text-white mb-2">Execution Goals Coming Soon</h4>
                <p class="text-slate-400 mb-6 max-w-md mx-auto">Advanced trade execution tracking and analysis
                    tools will be available in our next update.</p>
                <button type="button"
                    class="prev-step px-5 py-2.5 rounded-lg border border-slate-700/50 text-slate-300 hover:bg-slate-800/50 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Goal Types
                </button>
            </div>
        </div>

    </div>
</div>