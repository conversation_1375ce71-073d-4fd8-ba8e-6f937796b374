// Mistake Analysis Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the mistake dashboard
    initializeMistakeDashboard();
});

function initializeMistakeDashboard() {
    loadMistakeMetrics();
    loadMistakeDistribution();
    loadRecentMistakes();
    loadMistakeHeatmap();
    initializeModal();
}

// Load mistake metrics (overview cards)
function loadMistakeMetrics() {
    fetch('/getMistakeMetrics')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading metrics:', data.error);
                return;
            }

            // Update total mistakes
            document.getElementById('totalMistakes').textContent = data.totalMistakes;
            
            // Update most common mistake
            document.getElementById('mostCommonName').textContent = data.mostCommon.name;
            document.getElementById('mostCommonCount').textContent = data.mostCommon.count;
            
            // Update improvement rate
            const improvementRate = data.improvementRate;
            document.getElementById('improvementRate').textContent = improvementRate + '%';
            
            // Update progress bars and colors
            updateProgressBar('weeklyProgress', Math.min(100, (data.thisWeekMistakes / 10) * 100));
            updateProgressBar('mostCommonProgress', Math.min(100, (data.mostCommon.count / 50) * 100));
            updateProgressBar('improvementProgress', Math.min(100, Math.abs(improvementRate)));
            
            // Update improvement color
            const improvementElement = document.getElementById('improvementChange');
            if (improvementRate > 0) {
                improvementElement.textContent = '+' + improvementRate + '%';
                improvementElement.className = 'font-medium text-green-500';
            } else if (improvementRate < 0) {
                improvementElement.textContent = improvementRate + '%';
                improvementElement.className = 'font-medium text-red-500';
            } else {
                improvementElement.textContent = '0%';
                improvementElement.className = 'font-medium text-gray-500';
            }
        })
        .catch(error => {
            console.error('Error loading mistake metrics:', error);
        });
}

// Load mistake distribution chart
function loadMistakeDistribution() {
    fetch('/getMistakeDistribution')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading distribution:', data.error);
                return;
            }

            const chartContainer = document.getElementById('mistakeChart');
            chartContainer.innerHTML = '';

            if (data.length === 0) {
                chartContainer.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">No mistake data available</div>';
                return;
            }

            const maxCount = Math.max(...data.map(item => item.count));
            const colors = ['red', 'yellow', 'blue', 'purple', 'green', 'indigo'];

            data.forEach((mistake, index) => {
                const height = (mistake.count / maxCount) * 100;
                const color = colors[index % colors.length];
                
                const barContainer = document.createElement('div');
                barContainer.className = 'bar-container';
                
                barContainer.innerHTML = `
                    <div class="bar bg-${color}-400 dark:bg-${color}-500" 
                         style="height: ${height}%;" 
                         title="${mistake.count} mistakes"
                         data-mistake-name="${mistake.name}">
                    </div>
                    <div class="bar-label">${mistake.name}</div>
                `;
                
                chartContainer.appendChild(barContainer);
            });
        })
        .catch(error => {
            console.error('Error loading mistake distribution:', error);
        });
}

// Load recent mistakes table
function loadRecentMistakes() {
    fetch('/getRecentMistakes')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading recent mistakes:', data.error);
                return;
            }

            const tableBody = document.getElementById('mistakeTableBody');
            tableBody.innerHTML = '';

            if (data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            No mistakes recorded yet
                        </td>
                    </tr>
                `;
                return;
            }

            data.forEach(mistake => {
                const severityColor = getSeverityColor(mistake.severity);
                const impactColor = getImpactColor(mistake.impact);
                
                const row = document.createElement('tr');
                row.className = 'mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer';
                row.setAttribute('data-mistake-id', mistake.name.toLowerCase());
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-${mistake.color_class}-100 dark:bg-${mistake.color_class}-900/50 flex items-center justify-center">
                                <i class="${mistake.icon} text-${mistake.color_class}-600 dark:text-${mistake.color_class}-300"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${mistake.name}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 dark:text-gray-100">${mistake.category}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${severityColor}">${mistake.severity}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${impactColor}">${mistake.impact}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${mistake.count}</div>
                    </td>
                `;
                
                // Add click event to show modal
                row.addEventListener('click', () => showMistakeModal(mistake));
                
                tableBody.appendChild(row);
            });

            // Update total count
            document.getElementById('totalMistakeCount').textContent = data.length;
        })
        .catch(error => {
            console.error('Error loading recent mistakes:', error);
        });
}

// Load mistake heatmap
function loadMistakeHeatmap() {
    fetch('/getMistakeHeatmap')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading heatmap:', data.error);
                return;
            }

            const heatmapGrid = document.getElementById('heatmapGrid');
            heatmapGrid.innerHTML = '';

            // Create 35 cells (5 weeks × 7 days)
            for (let i = 0; i < 35; i++) {
                const cell = document.createElement('div');
                cell.className = 'heatmap-day bg-gray-200 dark:bg-gray-700';
                cell.title = '0 mistakes';
                heatmapGrid.appendChild(cell);
            }

            // Populate with actual data
            data.forEach(item => {
                const dayIndex = calculateDayIndex(item.date, item.day_of_week);
                if (dayIndex >= 0 && dayIndex < 35) {
                    const cell = heatmapGrid.children[dayIndex];
                    const intensity = Math.min(4, item.mistake_count);
                    const colors = [
                        'bg-gray-200 dark:bg-gray-700',
                        'bg-blue-200 dark:bg-blue-900',
                        'bg-blue-400 dark:bg-blue-700',
                        'bg-blue-600 dark:bg-blue-500',
                        'bg-blue-800 dark:bg-blue-400'
                    ];
                    
                    cell.className = `heatmap-day ${colors[intensity]}`;
                    cell.title = `${item.mistake_count} mistakes on ${item.date}`;
                }
            });
        })
        .catch(error => {
            console.error('Error loading mistake heatmap:', error);
        });
}

// Helper functions
function updateProgressBar(elementId, percentage) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.width = percentage + '%';
    }
}

function getSeverityColor(severity) {
    switch (severity) {
        case 'High': return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
        case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
        case 'Low': return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        default: return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200';
    }
}

function getImpactColor(impact) {
    switch (impact) {
        case 'Critical': return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
        case 'Moderate': return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        case 'Minor': return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
        default: return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200';
    }
}

function calculateDayIndex(date, dayOfWeek) {
    // This is a simplified calculation - you might need to adjust based on your specific needs
    const today = new Date();
    const mistakeDate = new Date(date);
    const diffTime = today - mistakeDate;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 35) return -1; // Too old
    
    return 34 - diffDays; // Reverse order (most recent at end)
}

// Modal functionality
function initializeModal() {
    const modal = document.getElementById('mistakeModal');
    const closeButtons = document.querySelectorAll('.close-mistake-modal');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            modal.classList.add('hidden');
        });
    });
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });
}

function showMistakeModal(mistake) {
    const modal = document.getElementById('mistakeModal');
    
    // Update modal content
    document.getElementById('mistakeType').textContent = mistake.name;
    document.getElementById('mistakeCategory').textContent = mistake.category;
    document.getElementById('mistakeSeverity').textContent = mistake.severity;
    document.getElementById('mistakeFrequency').textContent = mistake.count + ' times';
    document.getElementById('mistakeImpact').textContent = mistake.impact;
    
    // Format last occurrence
    if (mistake.last_occurrence) {
        const date = new Date(mistake.last_occurrence);
        document.getElementById('mistakeTime').textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } else {
        document.getElementById('mistakeTime').textContent = 'Never';
    }
    
    // Update icon
    const iconElement = document.getElementById('mistakeIcon');
    iconElement.className = `flex-shrink-0 h-12 w-12 rounded-lg bg-${mistake.color_class}-100 dark:bg-${mistake.color_class}-900/50 flex items-center justify-center`;
    iconElement.innerHTML = `<i class="${mistake.icon} text-${mistake.color_class}-600 dark:text-${mistake.color_class}-300 text-xl"></i>`;
    
    // Show modal
    modal.classList.remove('hidden');
}
