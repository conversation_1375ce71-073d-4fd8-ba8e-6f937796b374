<?php

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

if (!function_exists('send_email')) {
    function send_email($to, $subject, $message, $from = '<EMAIL>', $fromName = 'Trade Diary')
    {
        // Load Composer's autoloader
        require APPPATH . '../vendor/autoload.php';

        // Create an instance of PHPMailer
        $mail = new PHPMailer(true);

        try {
            //Server settings
            $mail->isSMTP();                                            // Send using SMTP
            $mail->Host       = 'smtp-relay.brevo.com';                 // Set the SMTP server to send through
            $mail->SMTPAuth   = true;                                   // Enable SMTP authentication
            $mail->Username   = '<EMAIL>';             // SMTP username
            $mail->Password   = 'g2nNWbdOksRmZrLP';                     // SMTP password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;         // Enable TLS encryption; `PHPMailer::ENCRYPTION_SMTPS` encouraged
            $mail->Port       = 587;                                    // TCP port to connect to

            //Recipients
            $mail->setFrom($from, $fromName);
            $mail->addAddress($to);                                     // Add a recipient

            // Content
            $mail->isHTML(true);                                        // Set email format to HTML
            $mail->Subject = $subject;
            $mail->Body    = $message;

            $mail->send();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}