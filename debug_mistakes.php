<?php
/**
 * Debug script to check mistake data in database
 */

try {
    $pdo = new PDO('mysql:host=localhost;dbname=diary', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Debug: Mistake Database Analysis</h2>\n";
    
    // Check mistakes table
    echo "<h3>1. Mistakes Table Data:</h3>\n";
    $stmt = $pdo->query("SELECT id, name, category, severity, impact, icon, color_class, is_active FROM mistakes ORDER BY id");
    $mistakes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($mistakes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Severity</th><th>Impact</th><th>Icon</th><th>Color Class</th><th>Active</th></tr>\n";
        foreach ($mistakes as $mistake) {
            echo "<tr>";
            echo "<td>{$mistake['id']}</td>";
            echo "<td>{$mistake['name']}</td>";
            echo "<td>{$mistake['category']}</td>";
            echo "<td>{$mistake['severity']}</td>";
            echo "<td>{$mistake['impact']}</td>";
            echo "<td>{$mistake['icon']}</td>";
            echo "<td>{$mistake['color_class']}</td>";
            echo "<td>{$mistake['is_active']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>❌ No mistakes found in database!</p>\n";
        echo "<p>You need to run the setup script to insert default mistakes.</p>\n";
    }
    
    // Check trade_mistakes table
    echo "<h3>2. Trade Mistakes Relationships:</h3>\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM trade_mistakes");
    $count = $stmt->fetchColumn();
    echo "<p>Total trade-mistake relationships: <strong>$count</strong></p>\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("
            SELECT tm.id, tm.trade_id, tm.mistake_id, m.name as mistake_name, t.symbol, t.datetime 
            FROM trade_mistakes tm 
            JOIN mistakes m ON tm.mistake_id = m.id 
            JOIN trades t ON tm.trade_id = t.id 
            ORDER BY t.datetime DESC 
            LIMIT 10
        ");
        $tradeMistakes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Trade ID</th><th>Mistake ID</th><th>Mistake Name</th><th>Symbol</th><th>Date</th></tr>\n";
        foreach ($tradeMistakes as $tm) {
            echo "<tr>";
            echo "<td>{$tm['id']}</td>";
            echo "<td>{$tm['trade_id']}</td>";
            echo "<td>{$tm['mistake_id']}</td>";
            echo "<td>{$tm['mistake_name']}</td>";
            echo "<td>{$tm['symbol']}</td>";
            echo "<td>{$tm['datetime']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ No trade-mistake relationships found.</p>\n";
        echo "<p>This means no trades have been associated with mistakes yet.</p>\n";
    }
    
    // Check what the API would return
    echo "<h3>3. Simulated API Response (Recent Mistakes):</h3>\n";
    
    // Simulate a user ID (you can change this to test with a real user)
    $testUserId = 1;
    
    $stmt = $pdo->prepare("
        SELECT 
            m.name, 
            m.category, 
            m.severity, 
            m.impact, 
            m.icon, 
            m.color_class,
            COUNT(*) as count,
            MAX(t.datetime) as last_occurrence
        FROM trade_mistakes tm 
        JOIN trades t ON tm.trade_id = t.id 
        JOIN mistakes m ON tm.mistake_id = m.id 
        WHERE t.user_id = ? AND t.deleted_at IS NULL 
        GROUP BY m.id, m.name, m.category, m.severity, m.impact, m.icon, m.color_class
        ORDER BY last_occurrence DESC 
        LIMIT 10
    ");
    
    $stmt->execute([$testUserId]);
    $apiResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($apiResult) > 0) {
        echo "<pre>" . json_encode($apiResult, JSON_PRETTY_PRINT) . "</pre>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ No recent mistakes found for user ID $testUserId</p>\n";
        echo "<p>This could mean:</p>\n";
        echo "<ul>\n";
        echo "<li>User has no trades with mistakes</li>\n";
        echo "<li>User ID $testUserId doesn't exist</li>\n";
        echo "<li>All trades are soft-deleted (deleted_at is not NULL)</li>\n";
        echo "</ul>\n";
    }
    
    // Check users table
    echo "<h3>4. Users Check:</h3>\n";
    $stmt = $pdo->query("SELECT id, full_name, email FROM users ORDER BY id LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<p>Sample users:</p>\n";
        foreach ($users as $user) {
            echo "<p>ID: {$user['id']}, Name: {$user['full_name']}, Email: {$user['email']}</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ No users found!</p>\n";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>\n";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; }
</style>
