// Community page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Additional community-specific functionality can be added here
    console.log('Community page loaded');
});

// Additional helper functions for community page
function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full`;
    
    switch(type) {
        case 'success':
            toast.classList.add('bg-green-600');
            break;
        case 'error':
            toast.classList.add('bg-red-600');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-600');
            break;
        default:
            toast.classList.add('bg-blue-600');
    }
    
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Override the showSuccess and showError methods in CommunityFeed
if (typeof window !== 'undefined') {
    window.addEventListener('load', function() {
        if (window.communityFeed) {
            window.communityFeed.showSuccess = function(message) {
                showToast(message, 'success');
            };
            
            window.communityFeed.showError = function(message) {
                showToast(message, 'error');
            };
        }
    });
}
