"use client"

import { useState } from "react"
import { Header } from "@/components/header"
import { GenerateSummary } from "@/components/generate-summary"
import { LoadingSection } from "@/components/loading-section"
import { ResultsSection } from "@/components/results-section"

export default function TradeSummaryPage() {
  const [currentView, setCurrentView] = useState<"generate" | "loading" | "results">("generate")
  const [selectedTimeframe, setSelectedTimeframe] = useState<7 | 15 | 30>(7)

  const handleGenerateSummary = () => {
    setCurrentView("loading")

    // Simulate loading process
    setTimeout(() => {
      setCurrentView("results")
    }, 6000)
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="max-w-7xl mx-auto p-6">
        <Header />

        <div className="mt-8">
          {currentView === "generate" && (
            <GenerateSummary
              selectedTimeframe={selectedTimeframe}
              onTimeframeChange={setSelectedTimeframe}
              onGenerate={handleGenerateSummary}
            />
          )}

          {currentView === "loading" && <LoadingSection />}

          {currentView === "results" && (
            <ResultsSection timeframe={selectedTimeframe} onGenerateNew={() => setCurrentView("generate")} />
          )}
        </div>
      </div>
    </div>
  )
}
