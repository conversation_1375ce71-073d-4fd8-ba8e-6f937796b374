const calculateBtn = document.getElementById('calculateBtn');
const comparisonType = document.getElementById('comparisonType');
const fdRateContainer = document.getElementById('fdRateContainer');
const mfRateContainer = document.getElementById('mfRateContainer');
const goldRateContainer = document.getElementById('goldRateContainer');
const silverRateContainer = document.getElementById('silverRateContainer');
const realestateRateContainer = document.getElementById('realestateRateContainer');
const stocksRateContainer = document.getElementById('stocksRateContainer');
const historicalData = document.getElementById('historicalData');
const fdResult = document.getElementById('fdResult');
const mfResult = document.getElementById('mfResult');
const diffResult = document.getElementById('diffResult');
const betterOption = document.getElementById('betterOption');
const tooltip = document.getElementById('tooltip');

// Chart initialization
let chart;
const ctx = document.getElementById('returnsChart').getContext('2d');
initializeChart();

// Event Listeners
calculateBtn.addEventListener('click', calculateReturns);
comparisonType.addEventListener('change', updateInputFields);

document.addEventListener('DOMContentLoaded', function () {
    updateInputFields();
    calculateReturns();
});

// Function to update input fields based on comparison type
function updateInputFields() {
    const type = comparisonType.value;

    // Reset all containers
    fdRateContainer.classList.add('hidden');
    mfRateContainer.classList.add('hidden');
    goldRateContainer.classList.add('hidden');
    silverRateContainer.classList.add('hidden');
    realestateRateContainer.classList.add('hidden');
    stocksRateContainer.classList.add('hidden');

    // Show relevant containers based on comparison type
    if (type === 'fd_mf') {
        fdRateContainer.classList.remove('hidden');
        mfRateContainer.classList.remove('hidden');
    } else if (type === 'fd_gold') {
        fdRateContainer.classList.remove('hidden');
        goldRateContainer.classList.remove('hidden');
    } else if (type === 'gold_silver') {
        goldRateContainer.classList.remove('hidden');
        silverRateContainer.classList.remove('hidden');
    } else if (type === 'realestate_stocks') {
        realestateRateContainer.classList.remove('hidden');
        stocksRateContainer.classList.remove('hidden');
    }
}

// Function to calculate compound returns
function compoundReturns(principal, rate, years) {
    return principal * Math.pow(1 + (rate / 100), years);
}

// Function to calculate returns and update UI
function calculateReturns() {
    const principal = parseFloat(document.getElementById('principal').value) || 100000;
    const years = parseInt(document.getElementById('years').value) || 5;
    const type = comparisonType.value;

    let option1Name, option2Name;
    let option1Value = 0, option2Value = 0;
    let option1Rate = 0, option2Rate = 0;
    let historicalRates1 = [], historicalRates2 = [];

    if (type === 'fd_mf') {
        option1Name = "FD";
        option2Name = "Mutual Fund";
        option1Rate = parseFloat(document.getElementById('fdRate').value) || 6.5;
        option2Rate = parseFloat(document.getElementById('mfRate').value) || 12;

        // Generate sample historical rates for FD and MF
        for (let i = 0; i < 10; i++) {
            historicalRates1.push((5.5 + Math.random() * 2).toFixed(1));
            historicalRates2.push((10 + Math.random() * 6).toFixed(1));
        }
    } else if (type === 'fd_gold') {
        option1Name = "FD";
        option2Name = "Gold";
        option1Rate = parseFloat(document.getElementById('fdRate').value) || 6.5;
        option2Rate = parseFloat(document.getElementById('goldRate').value) || 8;

        // Generate sample historical rates for FD and Gold
        for (let i = 0; i < 10; i++) {
            historicalRates1.push((5.5 + Math.random() * 2).toFixed(1));
            historicalRates2.push((7 + Math.random() * 4).toFixed(1));
        }
    } else if (type === 'gold_silver') {
        option1Name = "Gold";
        option2Name = "Silver";
        option1Rate = parseFloat(document.getElementById('goldRate').value) || 8;
        option2Rate = parseFloat(document.getElementById('silverRate').value) || 10;

        // Generate sample historical rates for Gold and Silver
        for (let i = 0; i < 10; i++) {
            historicalRates1.push((7 + Math.random() * 4).toFixed(1));
            historicalRates2.push((8 + Math.random() * 6).toFixed(1));
        }
    } else if (type === 'realestate_stocks') {
        option1Name = "Real Estate";
        option2Name = "Stocks";
        option1Rate = parseFloat(document.getElementById('realestateRate').value) || 7.5;
        option2Rate = parseFloat(document.getElementById('stocksRate').value) || 15;

        // Generate sample historical rates for Gold and Silver
        for (let i = 0; i < 10; i++) {
            historicalRates1.push((6 + Math.random() * 3).toFixed(1));
            historicalRates2.push((12 + Math.random() * 8).toFixed(1));
        }
    }

    // Calculate final values
    option1Value = compoundReturns(principal, option1Rate, years);
    option2Value = compoundReturns(principal, option2Rate, years);

    const difference = Math.abs(option1Value - option2Value);
    const better = option1Value > option2Value ? option1Name : option2Name;

    // Format with commas and rupee symbol
    const formatNumber = num => '₹' + num.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    // Update UI with results
    fdResult.textContent = formatNumber(option1Value);
    mfResult.textContent = formatNumber(option2Value);
    diffResult.textContent = formatNumber(difference);
    betterOption.textContent = better;

    // Update result labels based on comparison type
    document.querySelector('#resultsContainer div:nth-child(1) h3').textContent = `${option1Name} Returns`;
    document.querySelector('#resultsContainer div:nth-child(2) h3').textContent = `${option2Name} Returns`;

    // Update historical data table
    updateHistoricalTable(historicalRates1, historicalRates2, type);

    // Update chart
    updateChart(principal, option1Rate, option2Rate, years, option1Name, option2Name);
}

// Function to update historical data table
function updateHistoricalTable(rates1, rates2, type) {
    historicalData.innerHTML = '';

    for (let i = 0; i < 10; i++) {
        const row = document.createElement('tr');
        const yearCell = document.createElement('td');
        const rate1Cell = document.createElement('td');
        const rate2Cell = document.createElement('td');
        const rate3Cell = document.createElement('td');
        const rate4Cell = document.createElement('td');

        yearCell.className = 'px-4 py-3 whitespace-nowrap text-sm dark:text-slate-300 text-slate-700';
        rate1Cell.className = 'px-4 py-3 whitespace-nowrap text-sm dark:text-slate-300 text-slate-700';
        rate2Cell.className = 'px-4 py-3 whitespace-nowrap text-sm dark:text-slate-300 text-slate-700';
        rate3Cell.className = 'px-4 py-3 whitespace-nowrap text-sm dark:text-slate-300 text-slate-700';
        rate4Cell.className = 'px-4 py-3 whitespace-nowrap text-sm dark:text-slate-300 text-slate-700';

        yearCell.textContent = (2024 - (9 - i));

        if (type === 'fd_mf') {
            rate1Cell.textContent = `${rates1[i]}%`;
            rate2Cell.textContent = `${rates2[i]}%`;
            rate3Cell.textContent = '-';
            rate4Cell.textContent = '-';
        } else if (type === 'fd_gold') {
            rate1Cell.textContent = `${rates1[i]}%`;
            rate2Cell.textContent = '-';
            rate3Cell.textContent = `${rates2[i]}%`;
            rate4Cell.textContent = '-';
        } else if (type === 'gold_silver') {
            rate1Cell.textContent = '-';
            rate2Cell.textContent = '-';
            rate3Cell.textContent = `${rates1[i]}%`;
            rate4Cell.textContent = `${rates2[i]}%`;
        } else if (type === 'realestate_stocks') {
            rate1Cell.textContent = `${rates1[i]}%`;
            rate2Cell.textContent = `${rates2[i]}%`;
            rate3Cell.textContent = '-';
            rate4Cell.textContent = '-';
        }

        row.appendChild(yearCell);
        row.appendChild(rate1Cell);
        row.appendChild(rate2Cell);
        row.appendChild(rate3Cell);
        row.appendChild(rate4Cell);

        historicalData.appendChild(row);
    }
}

// Initialize chart
function initializeChart() {
    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Option 1',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.15)',
                    tension: 0.3,
                    fill: true,
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: '#2563eb',
                    pointHoverBorderWidth: 2
                },
                {
                    label: 'Option 2',
                    data: [],
                    borderColor: '#1d4ed8',
                    backgroundColor: 'rgba(29, 78, 216, 0.15)',
                    tension: 0.3,
                    fill: true,
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: '#1d4ed8',
                    pointHoverBorderWidth: 2
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: '#e2e8f0',
                        font: {
                            family: "'Inter', sans-serif",
                            weight: '500'
                        },
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    enabled: false,
                    external: function (context) {
                        const tooltipEl = document.getElementById('tooltip');

                        // Hide if no tooltip
                        if (context.tooltip.opacity === 0) {
                            tooltipEl.style.opacity = '0';
                            return;
                        }

                        // Get the hovered element and dataset
                        const hoveredEl = context.chart.getElementsAtEventForMode(
                            context.tooltip,
                            'index',
                            { intersect: false },
                            false
                        )[0];

                        // Set Text
                        if (context.tooltip.body) {
                            const lines = context.tooltip.body.map(b => b.lines);
                            tooltipEl.innerHTML = lines.join('<br>');
                        }

                        // Set position
                        tooltipEl.style.opacity = '1';
                        tooltipEl.style.left = context.tooltip.caretX + 'px';
                        tooltipEl.style.top = (context.tooltip.caretY - tooltipEl.offsetHeight - 10) + 'px';

                        // Add a nice little arrow at the bottom
                        tooltipEl.innerHTML += '<div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-0 border-t-4 border-l-transparent border-r-transparent border-t-blue-500"></div>';
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false,
                        color: 'rgba(148, 163, 184, 0.1)'
                    },
                    ticks: {
                        color: '#94a3b8'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(148, 163, 184, 0.1)'
                    },
                    ticks: {
                        color: '#94a3b8',
                        callback: function (value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            }
        }
    });
}

// Update chart with new data
function updateChart(principal, rate1, rate2, years, label1, label2) {
    const labels = Array.from({ length: years + 1 }, (_, i) => i.toString());
    const data1 = labels.map(year => compoundReturns(principal, rate1, parseInt(year)));
    const data2 = labels.map(year => compoundReturns(principal, rate2, parseInt(year)));

    chart.data.labels = labels;
    chart.data.datasets[0].data = data1;
    chart.data.datasets[0].label = label1;
    chart.data.datasets[1].data = data2;
    chart.data.datasets[1].label = label2;

    // Update colors based on better performer
    if (data1[data1.length - 1] > data2[data2.length - 1]) {
        chart.data.datasets[0].borderColor = '#1d4ed8';
        chart.data.datasets[0].backgroundColor = 'rgba(29, 78, 216, 0.2)';
        chart.data.datasets[1].borderColor = '#3b82f6';
        chart.data.datasets[1].backgroundColor = 'rgba(59, 130, 246, 0.1)';
    } else {
        chart.data.datasets[0].borderColor = '#3b82f6';
        chart.data.datasets[0].backgroundColor = 'rgba(59, 130, 246, 0.1)';
        chart.data.datasets[1].borderColor = '#1d4ed8';
        chart.data.datasets[1].backgroundColor = 'rgba(29, 78, 216, 0.2)';
    }

    chart.update();
}

// Add subtle glow effect to inputs on focus
document.querySelectorAll('.input-field').forEach(input => {
    input.addEventListener('focus', function () {
        const glowEffect = document.getElementById('glowEffect');
        const rect = this.getBoundingClientRect();

        glowEffect.style.left = `${rect.left}px`;
        glowEffect.style.top = `${rect.top}px`;
        glowEffect.style.width = `${rect.width}px`;
        glowEffect.style.height = `${rect.height}px`;
        glowEffect.style.borderRadius = window.getComputedStyle(this).borderRadius;
        glowEffect.style.backgroundColor = 'rgba(37, 99, 235, 0.1)';
        glowEffect.style.opacity = '1';
        glowEffect.style.transition = 'opacity 0.3s';

        this.style.boxShadow = '0 0 0 2px rgba(37, 99, 235, 0.3)';
    });

    input.addEventListener('blur', function () {
        const glowEffect = document.getElementById('glowEffect');
        glowEffect.style.opacity = '0';
        this.style.boxShadow = 'none';
    });
});