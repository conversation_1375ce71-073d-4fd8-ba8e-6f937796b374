<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FixImageUrls extends Migration
{
    public function up()
    {
        // Convert full URLs to relative paths in existing posts
        $db = \Config\Database::connect();
        
        // Get all posts with image URLs
        $builder = $db->table('community_posts');
        $builder->select('id, image_url');
        $builder->where('image_url IS NOT NULL');
        $builder->where('image_url !=', '');
        
        $posts = $builder->get()->getResultArray();
        
        if (!empty($posts)) {
            log_message('info', 'Found ' . count($posts) . ' posts with image URLs to fix');
            
            foreach ($posts as $post) {
                $originalUrl = $post['image_url'];
                $newUrl = $this->convertToRelativePath($originalUrl);
                
                if ($newUrl !== $originalUrl) {
                    $builder = $db->table('community_posts');
                    $builder->where('id', $post['id']);
                    $builder->update(['image_url' => $newUrl]);
                    
                    log_message('info', "Updated post {$post['id']}: '$originalUrl' -> '$newUrl'");
                }
            }
            
            log_message('info', 'Successfully updated image URLs to relative paths');
        }
    }

    public function down()
    {
        // This migration is not reversible as we don't store the original base URLs
        log_message('info', 'FixImageUrls migration down() called - no action taken');
    }

    /**
     * Convert full URL to relative path
     */
    private function convertToRelativePath($url)
    {
        // If it's already a relative path, return as is
        if (!str_contains($url, 'http')) {
            return $url;
        }
        
        // Extract the path after the domain
        $patterns = [
            // Match: http://domain.com/uploads/community/filename.jpg -> uploads/community/filename.jpg
            '#^https?://[^/]+/(.+)$#',
            // Match: http://domain.com/path/uploads/community/filename.jpg -> uploads/community/filename.jpg
            '#^https?://[^/]+/.*(uploads/community/.+)$#',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                $relativePath = $matches[1];
                
                // Ensure it starts with uploads/community/
                if (str_contains($relativePath, 'uploads/community/')) {
                    // Extract just the uploads/community/filename part
                    if (preg_match('#(uploads/community/[^/]+)#', $relativePath, $pathMatches)) {
                        return $pathMatches[1];
                    }
                }
                
                return $relativePath;
            }
        }
        
        // If no pattern matches, try to extract filename and reconstruct path
        $filename = basename($url);
        if ($filename && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
            return 'uploads/community/' . $filename;
        }
        
        // Return original if we can't parse it
        return $url;
    }
}
