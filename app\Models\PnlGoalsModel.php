<?php

namespace App\Models;

use CodeIgniter\Model;

class PnlGoalsModel extends Model
{
    protected $table = 'pnl_goals';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $allowedFields = ['goal_name', 'user_id', 'timeframe', 'custom_number', 'custom_period', 'goal_type', 'target_value', 'status', 'completed_at'];

}