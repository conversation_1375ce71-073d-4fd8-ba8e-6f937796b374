<?php
// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

class ContactController {
    private $to_email = '<EMAIL>';
    private $from_email = '<EMAIL>';
    private $company_name = 'Trade Diary';
    
    public function handleContactForm() {
        try {
            // Get POST data with better error handling
            $input = $this->getInputData();
            
            if (!$input) {
                return $this->sendResponse(false, 'No data received');
            }
            
            // Validate required fields
            $validation = $this->validateInput($input);
            if (!$validation['valid']) {
                return $this->sendResponse(false, $validation['message']);
            }
            
            // Sanitize input
            $data = $this->sanitizeInput($input);
            
            // Send email
            $emailSent = $this->sendEmail($data);
            
            if ($emailSent) {
                return $this->sendResponse(true, 'Your message has been sent successfully!');
            } else {
                return $this->sendResponse(false, 'Failed to send message. Please try again.');
            }
            
        } catch (Exception $e) {
            error_log('Contact form error: ' . $e->getMessage());
            return $this->sendResponse(false, 'An error occurred: ' . $e->getMessage());
        }
    }
    
    private function getInputData() {
        // First try to get JSON input
        $json_input = file_get_contents('php://input');
        
        if ($json_input) {
            $decoded = json_decode($json_input, true);
            
            // Check for JSON decode errors
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('JSON decode error: ' . json_last_error_msg());
                // Fall back to POST data
                return $_POST;
            }
            
            return $decoded;
        }
        
        // Fallback to regular POST data
        return $_POST;
    }
    
    private function validateInput($input) {
        $required_fields = ['name', 'email', 'subject', 'message'];
        
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                return ['valid' => false, 'message' => ucfirst($field) . ' is required'];
            }
        }
        
        // Validate email
        if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'message' => 'Please enter a valid email address'];
        }
        
        // Validate subject - make it more flexible
        $valid_subjects = ['support', 'feedback', 'partnership', 'other'];
        if (!in_array(strtolower($input['subject']), $valid_subjects)) {
            // If subject is not in predefined list, treat as 'other'
            $input['subject'] = 'other';
        }
        
        // Check message length
        if (strlen($input['message']) < 10) {
            return ['valid' => false, 'message' => 'Message must be at least 10 characters long'];
        }
        
        if (strlen($input['message']) > 2000) {
            return ['valid' => false, 'message' => 'Message must be less than 2000 characters'];
        }
        
        return ['valid' => true];
    }
    
    private function sanitizeInput($input) {
        return [
            'name' => htmlspecialchars(trim($input['name']), ENT_QUOTES, 'UTF-8'),
            'email' => filter_var(trim($input['email']), FILTER_SANITIZE_EMAIL),
            'subject' => htmlspecialchars(trim($input['subject']), ENT_QUOTES, 'UTF-8'),
            'message' => htmlspecialchars(trim($input['message']), ENT_QUOTES, 'UTF-8')
        ];
    }
    
    private function sendEmail($data) {
        try {
            // Check if mail function is available
            if (!function_exists('mail')) {
                throw new Exception('Mail function is not available on this server');
            }
            
            // Email subject based on form subject
            $subject_map = [
                'support' => 'Support Request',
                'feedback' => 'Feedback',
                'partnership' => 'Partnership Inquiry',
                'other' => 'General Inquiry'
            ];
            
            $email_subject = '[' . $this->company_name . '] ' . 
                            ($subject_map[strtolower($data['subject'])] ?? 'General Inquiry') . 
                            ' from ' . $data['name'];
            
            // Email body
            $email_body = $this->generateEmailBody($data);
            
            // Email headers
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: ' . $this->from_email,
                'Reply-To: ' . $data['email'],
                'X-Mailer: PHP/' . phpversion()
            ];
            
            // Send email
            $result = mail($this->to_email, $email_subject, $email_body, implode("\r\n", $headers));
            
            if (!$result) {
                error_log('Mail function returned false');
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log('Email sending error: ' . $e->getMessage());
            return false;
        }
    }
    
    private function generateEmailBody($data) {
        $timestamp = date('Y-m-d H:i:s');
        
        // Escape data for HTML output
        $name = htmlspecialchars($data['name'], ENT_QUOTES, 'UTF-8');
        $email = htmlspecialchars($data['email'], ENT_QUOTES, 'UTF-8');
        $subject = htmlspecialchars(ucfirst($data['subject']), ENT_QUOTES, 'UTF-8');
        $message = nl2br(htmlspecialchars($data['message'], ENT_QUOTES, 'UTF-8'));
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Contact Form Submission</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
                .content { background: #f9f9f9; padding: 30px; }
                .field { margin-bottom: 20px; }
                .label { font-weight: bold; color: #4F46E5; }
                .value { margin-top: 5px; }
                .message-box { background: white; padding: 20px; border-left: 4px solid #4F46E5; }
                .footer { background: #f0f0f0; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>New Contact Form Submission</h2>
                </div>
                <div class='content'>
                    <div class='field'>
                        <div class='label'>Name:</div>
                        <div class='value'>{$name}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Email:</div>
                        <div class='value'>{$email}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Subject:</div>
                        <div class='value'>{$subject}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Message:</div>
                        <div class='message-box'>{$message}</div>
                    </div>
                </div>
                <div class='footer'>
                    <p>This message was sent from the {$this->company_name} contact form on {$timestamp}</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    private function sendResponse($success, $message) {
        $response = [
            'success' => $success,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response);
        exit;
    }
}

// Initialize and handle the request
try {
    $controller = new ContactController();
    $controller->handleContactForm();
} catch (Exception $e) {
    error_log('Controller initialization error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Server error occurred',
        'error' => $e->getMessage() // Remove this in production
    ]);
}
?>