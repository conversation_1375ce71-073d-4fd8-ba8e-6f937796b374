// Community JavaScript functionality
class CommunityManager {
    constructor() {
        this.currentPage = 1;
        this.isLoading = false;
        this.hasMore = true;
        this.isMyPostsMode = false;
        this.currentFilters = {
            post_type: 'all',
            asset_type: 'all',
            sort: 'latest'
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPosts();
        this.initSidebar();
    }

    bindEvents() {
        // Filter events
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[name="asset_type"]')) {
                this.currentFilters.asset_type = e.target.value;
                this.resetAndLoadPosts();
            }
            if (e.target.matches('select[name="sort"]')) {
                this.currentFilters.sort = e.target.value;
                this.resetAndLoadPosts();
            }
        });

        // Tab events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tab-button')) {
                this.handleTabClick(e.target);
            }
        });

        // Infinite scroll
        window.addEventListener('scroll', () => {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                this.loadMorePosts();
            }
        });

        // Sidebar toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('#sidebarToggle')) {
                this.toggleSidebar();
            }
            if (e.target.matches('#mobileSidebarToggle')) {
                this.toggleMobileSidebar();
            }
        });

        // Navigation events
        document.addEventListener('click', (e) => {
            if (e.target.closest('a[href="#"]')) {
                e.preventDefault();
                const navItem = e.target.closest('a');
                const text = navItem.querySelector('.nav-text')?.textContent;
                
                if (text === 'My Posts') {
                    this.loadMyPosts();
                } else if (text === 'Home Feed') {
                    this.loadHomeFeed();
                }
            }
        });
    }

    initSidebar() {
        // Initialize sidebar state
        const sidebar = document.querySelector('.sidebar');
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('sidebar-collapsed');
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.toggle('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('sidebar-collapsed'));
    }

    toggleMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.overlay');
        
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
    }

    handleTabClick(tabButton) {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-button').forEach(tab => {
            tab.classList.remove('tab-active');
        });

        // Add active class to clicked tab
        tabButton.classList.add('tab-active');

        // Update filter based on tab
        const tabText = tabButton.textContent.toLowerCase();
        switch(tabText) {
            case 'all':
                this.currentFilters.post_type = 'all';
                break;
            case 'setups':
                this.currentFilters.post_type = 'setup_alert';
                break;
            case 'p&l':
                this.currentFilters.post_type = 'pnl_share';
                break;
            case 'analysis':
                this.currentFilters.post_type = 'analysis';
                break;
            case 'journals':
                this.currentFilters.post_type = 'journal';
                break;
        }

        this.resetAndLoadPosts();
    }

    resetAndLoadPosts() {
        this.currentPage = 1;
        this.hasMore = true;
        document.getElementById('postsContainer').innerHTML = '';
        this.loadPosts();
    }

    async loadPosts() {
        if (this.isLoading || !this.hasMore) return;

        this.isLoading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                ...this.currentFilters
            });

            const response = await fetch(`/community/posts?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderPosts(data.posts);
                this.hasMore = data.has_more;
                this.currentPage++;
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            this.showError('Error loading posts: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    async loadMorePosts() {
        if (this.isLoading || !this.hasMore) return;

        if (this.isMyPostsMode) {
            await this.loadMyPostsData();
        } else {
            await this.loadPosts();
        }
    }

    async loadMyPosts() {
        this.isMyPostsMode = true;
        this.currentPage = 1;
        this.hasMore = true;
        document.getElementById('postsContainer').innerHTML = '';

        // Use the my-posts endpoint instead of regular posts
        await this.loadMyPostsData();

        // Update navigation state
        this.updateNavigation('My Posts');
    }

    async loadMyPostsData() {
        if (this.isLoading || !this.hasMore) return;

        this.isLoading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                post_type: this.currentFilters.post_type,
                asset_type: this.currentFilters.asset_type,
                sort: this.currentFilters.sort
            });

            const response = await fetch(`/community/my-posts?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderPosts(data.posts);
                this.hasMore = data.has_more;
                this.currentPage++;
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            this.showError('Error loading posts: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    async loadHomeFeed() {
        this.isMyPostsMode = false;
        // Reset to use regular posts endpoint
        this.currentFilters = {
            post_type: this.currentFilters.post_type,
            asset_type: this.currentFilters.asset_type,
            sort: this.currentFilters.sort
        };
        this.resetAndLoadPosts();

        // Update navigation state
        this.updateNavigation('Home Feed');
    }

    updateNavigation(activeItem) {
        document.querySelectorAll('.sidebar nav a').forEach(link => {
            link.classList.remove('bg-gray-800', 'text-white');
            link.classList.add('text-gray-300', 'hover:bg-gray-800', 'hover:text-white');
        });

        const activeLink = Array.from(document.querySelectorAll('.sidebar nav a')).find(link => {
            const text = link.querySelector('.nav-text')?.textContent;
            return text === activeItem;
        });

        if (activeLink) {
            activeLink.classList.add('bg-gray-800', 'text-white');
            activeLink.classList.remove('text-gray-300', 'hover:bg-gray-800', 'hover:text-white');
        }
    }

    renderPosts(posts) {
        const container = document.getElementById('postsContainer');
        
        posts.forEach(post => {
            const postElement = this.createPostElement(post);
            container.appendChild(postElement);
        });

        // Bind events for new posts
        this.bindPostEvents();
    }

    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg';
        postDiv.dataset.postId = post.id;

        // Create badges HTML
        const badgesHtml = post.badges.map(badge => 
            `<span class="badge-${badge.badge_type} text-xs text-white px-2 py-1 rounded-full bg-${badge.badge_color}-600">
                ${badge.badge_name}
            </span>`
        ).join('');

        // Create hashtags HTML
        const hashtagsHtml = post.hashtags_array.map(tag => 
            `<span class="hashtag">#${tag}</span>`
        ).join(' ');

        // Create mentions HTML
        const mentionsHtml = post.mentions_array.map(mention => 
            `<span class="mention">@${mention}</span>`
        ).join(' ');

        // Create stock tags HTML
        const stockTagsHtml = post.stock_tags_array.map(tag => 
            `<span class="stock-tag">$${tag}</span>`
        ).join(' ');

        postDiv.innerHTML = `
            <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                <div class="flex items-center gap-3">
                    <img src="${post.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User"
                        class="w-10 h-10 rounded-full border-2 border-purple-400">
                    <div>
                        <div class="font-bold text-white">${post.full_name}</div>
                        <div class="text-xs text-gray-400">
                            ${post.time_ago} · <span class="text-purple-400">${this.getPostTypeLabel(post.post_type)}</span>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-2 sm:justify-end">
                    <button class="follow-btn text-xs text-white px-3 py-1 rounded-full transition"
                            onclick="toggleFollow(${post.user_id}, this)"
                            data-following="${post.user_is_following}">
                        ${post.user_is_following ? 'Following' : 'Follow'}
                    </button>
                    ${badgesHtml}
                    <button class="text-gray-400 hover:text-white transition">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <h3 class="text-lg font-bold text-white mb-3">${post.title}</h3>
            <p class="text-gray-300 mb-4">${post.content}</p>

            ${post.image_path ? `
                <div class="mb-4 flex justify-start">
                    <img src="${base_url}writable/${post.image_path}" alt="Post Image" 
                         class="w-full max-w-xl rounded-lg object-cover">
                </div>
            ` : ''}

            <div class="flex flex-wrap gap-2 mb-4">
                ${hashtagsHtml} ${mentionsHtml} ${stockTagsHtml}
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-3 border-t border-gray-800 post-actions">
                <div class="flex flex-wrap gap-4">
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400"
                            onclick="toggleLike(${post.id}, this)">
                        <i class="${post.user_has_liked ? 'fas' : 'far'} fa-thumbs-up mr-1"></i>
                        <span class="like-count">${post.like_count}</span>
                    </button>
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400"
                            onclick="toggleComments(${post.id})">
                        <i class="far fa-comment mr-1"></i>
                        <span>${post.comment_count}</span>
                    </button>
                    <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                        <i class="far fa-share-square mr-1"></i>
                        <span>Share</span>
                    </button>
                </div>
            </div>

            <!-- Comment section -->
            <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="comments-${post.id}">
                <div class="comments-container"></div>
                <div class="flex items-start space-x-3 mt-4">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                        class="w-8 h-8 rounded-full">
                    <div class="flex-1 flex comment-input-container">
                        <input type="text" placeholder="Write a comment..."
                            class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none"
                            onkeypress="handleCommentKeypress(event, ${post.id})">
                        <button class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg"
                                onclick="addComment(${post.id})">Post</button>
                    </div>
                </div>
            </div>
        `;

        return postDiv;
    }

    getPostTypeLabel(postType) {
        const labels = {
            'setup_alert': 'Setup Alert',
            'pnl_share': 'P&L Share',
            'educational': 'Educational',
            'analysis': 'Analysis',
            'journal': 'Journal'
        };
        return labels[postType] || 'Post';
    }

    bindPostEvents() {
        // Events are bound via onclick attributes in the HTML
        // This method can be used for additional event binding if needed
    }

    showLoading() {
        // Add loading indicator if needed
    }

    hideLoading() {
        // Remove loading indicator if needed
    }

    showError(message) {
        console.error(message);
        // You can implement a toast notification here
    }
}

// Initialize community manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.communityManager = new CommunityManager();
});

// Global functions for onclick handlers
async function toggleLike(postId, button) {
    try {
        const response = await fetch('/community/toggle-like', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `post_id=${postId}`
        });

        const data = await response.json();
        
        if (data.success) {
            const icon = button.querySelector('i');
            const countSpan = button.querySelector('.like-count');
            
            if (data.is_liked) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                button.classList.add('text-teal-400');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                button.classList.remove('text-teal-400');
            }
            
            countSpan.textContent = data.like_count;
        }
    } catch (error) {
        console.error('Error toggling like:', error);
    }
}

async function toggleFollow(userId, button) {
    try {
        const response = await fetch('/community/toggle-follow', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `following_id=${userId}`
        });

        const data = await response.json();
        
        if (data.success) {
            if (data.is_following) {
                button.textContent = 'Following';
                button.classList.add('following');
            } else {
                button.textContent = 'Follow';
                button.classList.remove('following');
            }
            button.dataset.following = data.is_following;
        }
    } catch (error) {
        console.error('Error toggling follow:', error);
    }
}

async function toggleComments(postId) {
    const commentsSection = document.getElementById(`comments-${postId}`);
    
    if (commentsSection.classList.contains('active')) {
        commentsSection.classList.remove('active');
    } else {
        commentsSection.classList.add('active');
        await loadComments(postId);
    }
}

async function loadComments(postId) {
    try {
        const response = await fetch(`/community/comments?post_id=${postId}`);
        const data = await response.json();
        
        if (data.success) {
            const container = document.querySelector(`#comments-${postId} .comments-container`);
            container.innerHTML = '';
            
            data.comments.forEach(comment => {
                const commentElement = createCommentElement(comment);
                container.appendChild(commentElement);
            });
        }
    } catch (error) {
        console.error('Error loading comments:', error);
    }
}

function createCommentElement(comment) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'flex mb-4';
    
    commentDiv.innerHTML = `
        <img src="${comment.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User"
            class="w-8 h-8 rounded-full mr-3">
        <div class="flex-1">
            <div class="flex items-center">
                <div class="text-sm font-medium text-white">${comment.full_name}</div>
                <span class="text-xs text-gray-500 ml-2">${comment.time_ago}</span>
            </div>
            <p class="text-sm text-gray-300">${comment.content}</p>
            <div class="flex text-xs text-gray-500 mt-1">
                <button class="hover:text-teal-400 reply-btn" onclick="toggleReply(${comment.id})">Reply</button>
            </div>
            
            <!-- Reply section -->
            <div class="reply-section" id="reply-${comment.id}">
                <div class="flex items-start space-x-2 mt-2">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                        class="w-6 h-6 rounded-full">
                    <div class="flex-1 flex comment-input-container">
                        <input type="text" placeholder="Write your reply..."
                            class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm"
                            onkeypress="handleReplyKeypress(event, ${comment.post_id}, ${comment.id})">
                        <button class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm"
                                onclick="addReply(${comment.post_id}, ${comment.id})">Reply</button>
                    </div>
                </div>
            </div>
            
            ${comment.replies ? comment.replies.map(reply => createReplyElement(reply)).join('') : ''}
        </div>
    `;
    
    return commentDiv;
}

function createReplyElement(reply) {
    return `
        <div class="mt-3 pl-4 border-l-2 border-gray-700">
            <div class="flex">
                <img src="${reply.profile || 'https://randomuser.me/api/portraits/men/32.jpg'}" alt="User"
                    class="w-8 h-8 rounded-full mr-3">
                <div>
                    <div class="flex items-center">
                        <div class="text-sm font-medium text-white">${reply.full_name}</div>
                        <span class="text-xs text-gray-500 ml-2">${reply.time_ago}</span>
                    </div>
                    <p class="text-sm text-gray-300">${reply.content}</p>
                </div>
            </div>
        </div>
    `;
}

async function addComment(postId) {
    const input = document.querySelector(`#comments-${postId} .comment-input`);
    const content = input.value.trim();
    
    if (!content) return;
    
    try {
        const response = await fetch('/community/add-comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `post_id=${postId}&content=${encodeURIComponent(content)}`
        });

        const data = await response.json();
        
        if (data.success) {
            input.value = '';
            await loadComments(postId);
        }
    } catch (error) {
        console.error('Error adding comment:', error);
    }
}

async function addReply(postId, parentCommentId) {
    const input = document.querySelector(`#reply-${parentCommentId} .comment-input`);
    const content = input.value.trim();
    
    if (!content) return;
    
    try {
        const response = await fetch('/community/add-comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `post_id=${postId}&content=${encodeURIComponent(content)}&parent_comment_id=${parentCommentId}`
        });

        const data = await response.json();
        
        if (data.success) {
            input.value = '';
            toggleReply(parentCommentId);
            await loadComments(postId);
        }
    } catch (error) {
        console.error('Error adding reply:', error);
    }
}

function toggleReply(commentId) {
    const replySection = document.getElementById(`reply-${commentId}`);
    replySection.classList.toggle('active');
}

function handleCommentKeypress(event, postId) {
    if (event.key === 'Enter') {
        addComment(postId);
    }
}

function handleReplyKeypress(event, postId, parentCommentId) {
    if (event.key === 'Enter') {
        addReply(postId, parentCommentId);
    }
}
