<style>
    .metric-card {
        transition: all 0.3s ease;
    }
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .dark .metric-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }
    .mistake-item {
        transition: all 0.2s ease;
    }
    .mistake-item:hover {
        background-color: rgba(99, 102, 241, 0.1);
    }
    .dark .mistake-item:hover {
        background-color: rgba(99, 102, 241, 0.2);
    }
    .heatmap-day {
        width: 28px;
        height: 28px;
        border-radius: 5px;
        margin: 0;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
    }
    .heatmap-day:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }
    .heatmap-day:active {
        transform: scale(0.95);
    }
    .heatmap-day.clicked {
        animation: heatmapPulse 0.3s ease;
    }
    @keyframes heatmapPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.15); }
        100% { transform: scale(1); }
    }
    @media (min-width: 640px) {
        .heatmap-day {
            width: 34px;
            height: 34px;
            border-radius: 6px;
        }
    }

    /* Heatmap Tooltip */
    .heatmap-tooltip {
        position: absolute;
        background: #1f2937;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        pointer-events: none;
        transform: translateX(-50%);
    }
    .heatmap-tooltip.show {
        opacity: 1;
        visibility: visible;
    }
    .heatmap-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: #1f2937;
    }
    .dark .heatmap-tooltip {
        background: #374151;
        border: 1px solid #4b5563;
    }
    .dark .heatmap-tooltip::after {
        border-top-color: #374151;
    }
    .bar-chart {
        display: flex;
        align-items: flex-end;
        height: 200px;
        padding-top: 15px;
        overflow-x: auto;
        overflow-y: visible;
        padding-bottom: 10px;
    }
    @media (min-width: 640px) {
        .bar-chart {
            height: 250px;
            padding-top: 20px;
            overflow-x: visible;
        }
    }
    .bar-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        min-width: 45px;
        max-width: 80px;
        position: relative;
        height: 100%;
        justify-content: flex-end;
        margin: 0 1px;
    }
    @media (min-width: 640px) {
        .bar-container {
            min-width: 60px;
            max-width: none;
            margin: 0 2px;
        }
    }
    .bar {
        width: 85%;
        min-height: 6px;
        border-radius: 3px 3px 0 0;
        transition: all 0.3s ease;
        position: relative;
        cursor: pointer;
        display: block;
    }
    @media (min-width: 640px) {
        .bar {
            width: 70%;
            min-height: 8px;
            border-radius: 4px 4px 0 0;
        }
    }
    .bar:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .bar-label {
        margin-top: 6px;
        font-size: 0.75rem;
        text-align: center;
        color: #6b7280;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Hide bar labels on mobile completely */
    @media (max-width: 639px) {
        .bar-label {
            display: none !important;
        }
    }

    @media (min-width: 640px) {
        .bar-label {
            font-size: 0.75rem;
            margin-top: 8px;
            height: auto;
            display: flex !important;
        }
    }

    /* Mobile bar labels inside bars */
    .mobile-bar-label {
        display: block;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    @media (min-width: 640px) {
        .mobile-bar-label {
            display: none !important;
        }
    }

    /* Ensure bars have relative positioning for absolute labels */
    .bar {
        position: relative;
    }
    .dark .bar-label {
        color: #9ca3af;
    }
    .percentage-label {
        pointer-events: none;
        white-space: nowrap;
    }
    .modal {
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    .modal-content {
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    }
    .chart-container {
        height: 220px;
        width: 100%;
        overflow-x: auto;
        overflow-y: visible;
    }

    /* Mobile responsive improvements */
    @media (min-width: 640px) {
        .chart-container {
            height: 280px;
            overflow-x: visible;
        }
    }

    @media (max-width: 639px) {
        .mistake-item:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .dark .mistake-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        /* Improve chart readability on mobile */
        .bar-label {
            font-size: 0.625rem;
        }
    }
</style>

<!-- Mistake Analysis Dashboard -->
<div class="min-h-screen bg-gray-100 dark:bg-dark-900 transition-colors duration-200">
    <!-- Main Container -->
    <div class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Page Header -->
        <div class="mb-6 sm:mb-8">
            <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">Mistake Analysis</h1>
            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400">Track and analyze your trading mistakes to improve performance</p>
        </div>

        <!-- Overview Metrics -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <!-- Total Mistakes -->
            <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm font-medium">Total Mistakes</p>
                        <h3 id="totalMistakes" class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white mt-1 truncate">0</h3>
                    </div>
                    <div class="p-2 sm:p-3 rounded-full bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-300 flex-shrink-0">
                        <i class="fas fa-times-circle text-lg sm:text-xl"></i>
                    </div>
                </div>
                <div class="mt-3 sm:mt-4">
                    <div class="flex items-center justify-between text-xs sm:text-sm">
                        <span class="text-gray-500 dark:text-gray-400">This week</span>
                        <span id="weeklyChange" class="font-medium text-red-500">+0%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                        <div id="weeklyProgress" class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Most Common Mistake -->
            <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm font-medium">Most Common</p>
                        <h3 id="mostCommonName" class="text-lg sm:text-xl font-bold text-gray-800 dark:text-white mt-1 truncate">No mistakes</h3>
                    </div>
                    <div class="p-2 sm:p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-300 flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-lg sm:text-xl"></i>
                    </div>
                </div>
                <div class="mt-3 sm:mt-4">
                    <div class="flex items-center justify-between text-xs sm:text-sm">
                        <span class="text-gray-500 dark:text-gray-400">Occurrences</span>
                        <span id="mostCommonCount" class="font-medium text-gray-800 dark:text-gray-200">0</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                        <div id="mostCommonProgress" class="bg-yellow-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Improvement Rate -->
            <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm font-medium">Improvement Rate</p>
                        <h3 id="improvementRate" class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white mt-1 truncate">0%</h3>
                    </div>
                    <div class="p-2 sm:p-3 rounded-full bg-gray-100 dark:bg-gray-900/50 text-gray-600 dark:text-gray-300 flex-shrink-0">
                        <i class="fas fa-chart-line text-lg sm:text-xl"></i>
                    </div>
                </div>
                <div class="mt-3 sm:mt-4">
                    <div class="flex items-center justify-between text-xs sm:text-sm">
                        <span class="text-gray-500 dark:text-gray-400">Last week</span>
                        <span id="improvementChange" class="font-medium text-gray-500">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                        <div id="improvementProgress" class="bg-gray-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <!-- Mistake Distribution -->
            <div class="bg-white dark:bg-dark-800 rounded-lg shadow p-4 sm:p-6 lg:col-span-2">
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6">
                    <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-0">Mistake Distribution</h2>
                    <div class="flex space-x-2">
                        <button id="thisMonthBtn" class="px-2 sm:px-3 py-1 text-xs bg-primary-500 text-white rounded-full transition-colors duration-200">This Month</button>
                        <button id="allTimeBtn" class="px-2 sm:px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full transition-colors duration-200">All Time</button>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="bar-chart">
                        <div class="y-axis"></div>
                        <div id="mistakeChart" class="flex-1 flex items-end pl-2 sm:pl-8 h-full min-w-max">
                            <!-- Chart bars will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mistake Heatmap -->
            <div class="bg-white dark:bg-dark-800 rounded-lg shadow p-3 sm:p-4">
                <h2 class="text-base sm:text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4">Frequency Heatmap</h2>
                <div class="flex flex-col items-center space-y-3 sm:space-y-4">
                    <!-- Legend -->
                    <div class="flex justify-center items-center gap-2 sm:gap-3">
                        <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 font-medium">Less</span>
                        <div class="bg-gray-200 dark:bg-gray-700 w-4 h-4 sm:w-5 sm:h-5 rounded"></div>
                        <div class="bg-blue-200 dark:bg-blue-900 w-4 h-4 sm:w-5 sm:h-5 rounded"></div>
                        <div class="bg-blue-400 dark:bg-blue-700 w-4 h-4 sm:w-5 sm:h-5 rounded"></div>
                        <div class="bg-blue-600 dark:bg-blue-500 w-4 h-4 sm:w-5 sm:h-5 rounded"></div>
                        <div class="bg-blue-800 dark:bg-blue-400 w-4 h-4 sm:w-5 sm:h-5 rounded"></div>
                        <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 font-medium">More</span>
                    </div>

                    <!-- Heatmap Grid -->
                    <div class="w-full flex justify-center overflow-x-auto px-1">
                        <div class="grid grid-cols-7 gap-1.5 sm:gap-2 min-w-max">
                            <!-- Weekday labels -->
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">M</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">T</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">W</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">T</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">F</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">S</div>
                            <div class="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 h-5 sm:h-6 flex items-center justify-center font-semibold">S</div>

                            <!-- Heatmap cells will be populated by JavaScript -->
                            <div id="heatmapGrid" class="col-span-7 grid grid-cols-7 gap-1.5 sm:gap-2">
                                <!-- Heatmap data will be loaded here -->
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <!-- Custom Mistakes Management -->
        <div class="bg-white dark:bg-dark-800 rounded-lg shadow overflow-hidden mb-6 sm:mb-8">
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-0">My Custom Mistakes</h2>
                <button id="addCustomMistakeBtn" onclick="handleAddCustomMistake()"
                        class="px-3 sm:px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm sm:text-base">
                    <i class="fas fa-plus mr-1 sm:mr-2"></i>Add Custom Mistake
                </button>
            </div>
            <div class="p-4 sm:p-6">
                <div id="customMistakesContainer">
                    <div id="customMistakesLoading" class="text-center py-6 sm:py-8">
                        <i class="fas fa-spinner fa-spin text-xl sm:text-2xl text-gray-400 mb-2"></i>
                        <p class="text-sm sm:text-base text-gray-500 dark:text-gray-400">Loading custom mistakes...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Mistakes -->
        <div class="bg-white dark:bg-dark-800 rounded-lg shadow overflow-hidden mb-6 sm:mb-8">
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200">Recent Mistakes</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                <span class="hidden sm:inline">Mistake Name</span>
                                <span class="sm:hidden">Name</span>
                            </th>
                            <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                            <th scope="col" class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Severity</th>
                            <th scope="col" class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Impact</th>
                            <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Count</th>
                        </tr>
                    </thead>
                    <tbody id="mistakeTableBody" class="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        Previous
                    </button>
                    <button class="ml-3 relative inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        Next
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span id="totalMistakeCount" class="font-medium">0</span> mistakes
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mistake Analysis Modal -->
<div id="mistakeModal" class="modal fixed z-10 inset-0 overflow-y-auto hidden">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-bottom bg-white dark:bg-dark-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-dark-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div id="mistakeIcon" class="flex-shrink-0 h-12 w-12 rounded-lg bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
                        <i class="fas fa-brain text-red-600 dark:text-red-300 text-xl"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="mistakeType">Mistake Details</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400" id="mistakeCategory">Category</p>
                            <div class="mt-4">
                                <p class="text-sm text-gray-700 dark:text-gray-300" id="mistakeDescription">
                                    Mistake description will appear here.
                                </p>
                            </div>
                            <div class="mt-4 grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Severity</p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeSeverity">Medium</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Frequency</p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeFrequency">0 times</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Impact</p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeImpact">Moderate</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Last Occurrence</p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeTime">Never</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/30 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="close-mistake-modal w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-500 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Analyze Pattern
                </button>
                <button type="button" class="close-mistake-modal mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-dark-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Mistake Modal -->
<div id="customMistakeModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 9999; align-items: center; justify-content: center;">
    <div class="bg-white dark:bg-dark-800 rounded-lg shadow-xl" style="max-width: 400px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <form id="customMistakeForm">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100" id="customMistakeModalTitle">Add Custom Mistake</h3>
            </div>

            <!-- Form Content -->
            <div class="px-6 py-6">
                <div class="space-y-4">
                    <!-- Mistake Name -->
                    <div>
                        <label for="mistakeName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mistake Name *</label>
                        <input type="text" id="mistakeName" name="name" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm
                                      focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                                      bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                      placeholder-gray-500 dark:placeholder-gray-400"
                               placeholder="Enter mistake name">
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="mistakeCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="mistakeCategory" name="category" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm
                                       focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                                       bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="" class="text-gray-500 dark:text-gray-400">Select Category</option>
                            <option value="Cognitive" class="text-gray-900 dark:text-white">Cognitive</option>
                            <option value="Psychological" class="text-gray-900 dark:text-white">Psychological</option>
                            <option value="Behavioral" class="text-gray-900 dark:text-white">Behavioral</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                <button type="button" class="close-custom-mistake-modal px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300
                                           bg-white dark:bg-dark-700 border border-gray-300 dark:border-gray-600 rounded-md
                                           hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-500 hover:bg-primary-600
                                          rounded-md transition-colors">
                    <span id="submitButtonText">Create Mistake</span>
                </button>
            </div>
        </form>
    </div>
</div>
