<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Moderator Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        ::-webkit-scrollbar-thumb {
            background: #64748b;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Animation for buttons */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .pulse-animation {
            animation: pulse 1.5s infinite;
        }
        
        /* Custom glow effect */
        .glow-effect {
            box-shadow: 0 0 15px rgba(99, 102, 241, 0.3);
        }
        
        /* Modal transition */
        .modal {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .modal-enter {
            opacity: 0;
            transform: scale(0.9);
        }
        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
        }
        .modal-exit {
            opacity: 1;
            transform: scale(1);
        }
        .modal-exit-active {
            opacity: 0;
            transform: scale(0.9);
        }
        
        /* Responsive adjustments */
        @media (max-width: 640px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }
            .profile-stats {
                justify-content: center;
            }
            .profile-actions {
                justify-content: center;
                margin-top: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- Profile Header -->
        <div class="bg-gray-800 rounded-2xl shadow-xl p-6 mb-8 relative glow-effect">
            <div class="flex flex-wrap profile-header">
                <!-- Profile Picture -->
                <div class="relative group mr-6 mb-4 sm:mb-0">
                    <div class="relative">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Profile"
                              class="w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-indigo-600 object-cover shadow-lg">
                        <div class="absolute -bottom-2 -right-2 bg-indigo-600 rounded-full p-1">
                            <i class="fas fa-shield-alt text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-indigo-600 bg-opacity-70 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer">
                        <i class="fas fa-camera text-2xl"></i>
                    </div>
                </div>
                                
                <!-- Profile Info -->
                <div class="flex-1 min-w-0">
                    <div class="flex flex-col h-full">
                        <div class="flex-1">
                            <h1 id="profileName" class="text-2xl sm:text-3xl font-bold truncate">Super Moderator</h1>
                            <p class="text-indigo-400 mb-2">@super_moderator</p>
                            <p id="profileBio" class="text-gray-300 mb-4 max-w-2xl">
                                Senior Community Moderator | 5+ years experience | Ensuring safe and engaging discussions
                            </p>
                            <div id="profileTradingStyle" class="bg-gray-700 text-indigo-300 px-3 py-1 rounded-full text-sm inline-block mb-4">
                                <i class="fas fa-user-shield mr-1"></i> Super Moderator
                            </div>
                        </div>
                                                
                        <!-- Stats -->
                        <div class="flex flex-wrap profile-stats gap-4 sm:gap-6 mt-4 pt-4 border-t border-gray-700">
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold">1.2K</div>
                                <div class="text-gray-400 text-xs sm:text-sm">Posts</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold">5.2K</div>
                                <div class="text-gray-400 text-xs sm:text-sm">Followers</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold">342</div>
                                <div class="text-gray-400 text-xs sm:text-sm">Following</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold">3</div>
                                <div class="text-gray-400 text-xs sm:text-sm">Years</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold">12.8K</div>
                                <div class="text-gray-400 text-xs sm:text-sm">Actions</div>
                            </div>
                        </div>
                    </div>
                </div>
                                
                <!-- Profile Actions -->
                <div class="flex profile-actions space-x-2 mt-4 sm:mt-0">
                    <button id="editBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1.5 rounded-lg transition-colors flex items-center text-sm">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </button>
                </div>
            </div>
        </div>
                
        <!-- Content Tabs -->
        <div class="flex overflow-x-auto border-b border-gray-700 mb-6 scrollbar-hide">
            <button id="postsTab" class="px-4 py-2 font-medium text-indigo-400 border-b-2 border-indigo-400 whitespace-nowrap">
                <i class="fas fa-newspaper mr-2"></i>Your Posts
            </button>
            <button id="followersTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-users mr-2"></i>Followers (5,231)
            </button>
            <button id="followingTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-user-friends mr-2"></i>Following (342)
            </button>
            <button id="modActionsTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-gavel mr-2"></i>Mod Actions
            </button>
            <button id="analyticsTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-chart-line mr-2"></i>Analytics
            </button>
        </div>
                
        <!-- Posts Content -->
        <div id="postsContent" class="space-y-6">
            <!-- Post 1 -->
            <div class="bg-gray-800 rounded-xl p-6 shadow-lg hover:glow-effect transition-all">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-3">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-10 h-10 rounded-full">
                        <div>
                            <div class="font-bold">Super Moderator</div>
                            <div class="text-gray-400 text-sm">2 hours ago</div>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-gray-400 hover:text-indigo-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-4">
                    <p class="mb-3">Just implemented new community guidelines to improve discussion quality. Please review them in the pinned post. Let me know if you have any questions!</p>
                    <div class="bg-gray-700 p-3 rounded-lg border-l-4 border-indigo-500">
                        <h4 class="font-bold text-indigo-300 mb-2"><i class="fas fa-bullhorn mr-2"></i>Important Update</h4>
                        <p class="text-sm">New rule: All trading discussions must include proper risk disclosure. Posts without this will be removed after one warning.</p>
                    </div>
                </div>
                <div class="flex justify-between items-center text-gray-400 text-sm">
                    <div class="flex space-x-4">
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-thumbs-up"></i>
                            <span>124</span>
                        </button>
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-comment"></i>
                            <span>28</span>
                        </button>
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                    </div>
                    <div>
                        <span class="bg-indigo-900 text-indigo-300 px-2 py-1 rounded text-xs">
                            <i class="fas fa-shield-alt mr-1"></i> Announcement
                        </span>
                    </div>
                </div>
            </div>
                    
            <!-- Post 2 -->
            <div class="bg-gray-800 rounded-xl p-6 shadow-lg hover:glow-effect transition-all">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-3">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-10 h-10 rounded-full">
                        <div>
                            <div class="font-bold">Super Moderator</div>
                            <div class="text-gray-400 text-sm">1 day ago</div>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-gray-400 hover:text-indigo-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-4">
                    <p class="mb-3">Weekly moderation report: We've removed 23 spam posts, warned 5 users for inappropriate content, and banned 1 repeat offender. Community quality is improving!</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-3">
                        <div class="bg-gray-700 p-2 rounded text-center">
                            <div class="text-red-400 font-bold">23</div>
                            <div class="text-xs">Spam Removed</div>
                        </div>
                        <div class="bg-gray-700 p-2 rounded text-center">
                            <div class="text-yellow-400 font-bold">5</div>
                            <div class="text-xs">Warnings</div>
                        </div>
                        <div class="bg-gray-700 p-2 rounded text-center">
                            <div class="text-green-400 font-bold">1</div>
                            <div class="text-xs">Bans</div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center text-gray-400 text-sm">
                    <div class="flex space-x-4">
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-thumbs-up"></i>
                            <span>89</span>
                        </button>
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="far fa-comment"></i>
                            <span>15</span>
                        </button>
                        <button class="flex items-center space-x-1 hover:text-indigo-400">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                    </div>
                    <div>
                        <span class="bg-purple-900 text-purple-300 px-2 py-1 rounded text-xs">
                            <i class="fas fa-chart-pie mr-1"></i> Report
                        </span>
                    </div>
                </div>
            </div>
        </div>
                
        <!-- Followers Content (Hidden by default) -->
        <div id="followersContent" class="hidden">
            <!-- Follower Filters -->
            <div class="bg-gray-800 rounded-xl p-4 mb-6 flex flex-wrap gap-3">
                <div class="relative flex-1 min-w-[200px]">
                    <input type="text" placeholder="Search followers..." class="bg-gray-700 text-white w-full px-4 py-2 rounded-lg pl-10">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <select class="bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <option>All Followers</option>
                    <option>Recently Active</option>
                    <option>Most Engaged</option>
                    <option>New Followers</option>
                </select>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-filter mr-2"></i>Filter
                </button>
            </div>
                    
            <!-- Follower List -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- Follower 1 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Follower" class="w-12 h-12 rounded-full">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate">Sarah Trader</div>
                            <div class="text-gray-400 text-sm truncate">@sarah_trades</div>
                        </div>
                        <div class="bg-green-500 rounded-full p-1" title="Online now">
                            <div class="w-2 h-2"></div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Member since:</div>
                        <div>Jan 2023</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Last active:</div>
                        <div>2 hours ago</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center group-hover:bg-indigo-600 group-hover:text-white">
                            Message
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-slash text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- Follower 2 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Follower" class="w-12 h-12 rounded-full">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate">Mike Investor</div>
                            <div class="text-gray-400 text-sm truncate">@mike_invests</div>
                        </div>
                        <div class="bg-gray-500 rounded-full p-1" title="Offline">
                            <div class="w-2 h-2"></div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Member since:</div>
                        <div>Mar 2022</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Last active:</div>
                        <div>1 day ago</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center group-hover:bg-indigo-600 group-hover:text-white">
                            Message
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-slash text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- Follower 3 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Follower" class="w-12 h-12 rounded-full border-2 border-indigo-500">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate">Lisa Hodler</div>
                            <div class="text-gray-400 text-sm truncate">@lisa_holds</div>
                        </div>
                        <div class="bg-green-500 rounded-full p-1" title="Online now">
                            <div class="w-2 h-2"></div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Member since:</div>
                        <div>Nov 2021</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Last active:</div>
                        <div>30 mins ago</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center">
                            Following
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-slash text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- Follower 4 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/men/41.jpg" alt="Follower" class="w-12 h-12 rounded-full">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate">Crypto Expert</div>
                            <div class="text-gray-400 text-sm truncate">@crypto_guru</div>
                        </div>
                        <div class="bg-gray-500 rounded-full p-1" title="Offline">
                            <div class="w-2 h-2"></div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Member since:</div>
                        <div>Feb 2023</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Last active:</div>
                        <div>3 days ago</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center group-hover:bg-indigo-600 group-hover:text-white">
                            Message
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-slash text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- View More -->
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                    <div class="text-center">
                        <div class="text-indigo-400 mb-2">
                            <i class="fas fa-chevron-down text-2xl"></i>
                        </div>
                        <div class="font-medium">View all 5,231 followers</div>
                        <div class="text-gray-400 text-sm mt-1">Scroll for more</div>
                    </div>
                </div>
            </div>
        </div>
                
        <!-- Following Content (Hidden by default) -->
        <div id="followingContent" class="hidden">
            <!-- Following Filters -->
            <div class="bg-gray-800 rounded-xl p-4 mb-6 flex flex-wrap gap-3">
                <div class="relative flex-1 min-w-[200px]">
                    <input type="text" placeholder="Search following..." class="bg-gray-700 text-white w-full px-4 py-2 rounded-lg pl-10">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <select class="bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <option>All Following</option>
                    <option>Most Active</option>
                    <option>Recently Followed</option>
                    <option>Verified Only</option>
                </select>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-filter mr-2"></i>Filter
                </button>
            </div>
                    
            <!-- Following List -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- Following 1 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Following" class="w-12 h-12 rounded-full border-2 border-blue-400">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate flex items-center">
                                Crypto News
                                <i class="fas fa-check-circle text-blue-400 ml-1 text-sm"></i>
                            </div>
                            <div class="text-gray-400 text-sm truncate">@crypto_updates</div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Followers:</div>
                        <div>124.5K</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Posts:</div>
                        <div>3.2K</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center">
                            Following
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-minus text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- Following 2 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Following" class="w-12 h-12 rounded-full">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate">Trading Signals</div>
                            <div class="text-gray-400 text-sm truncate">@trade_alerts</div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Followers:</div>
                        <div>87.3K</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Posts:</div>
                        <div>1.8K</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center">
                            Following
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-minus text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- Following 3 -->
                <div class="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 transition-colors cursor-pointer group">
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="https://randomuser.me/api/portraits/men/12.jpg" alt="Following" class="w-12 h-12 rounded-full border-2 border-blue-400">
                        <div class="flex-1 min-w-0">
                            <div class="font-bold truncate flex items-center">
                                Market Insights
                                <i class="fas fa-check-circle text-blue-400 ml-1 text-sm"></i>
                            </div>
                            <div class="text-gray-400 text-sm truncate">@market_watch</div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm mb-2">
                        <div class="text-gray-400">Followers:</div>
                        <div>256.7K</div>
                    </div>
                    <div class="flex justify-between text-sm mb-3">
                        <div class="text-gray-400">Posts:</div>
                        <div>5.6K</div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-full text-sm transition-colors flex-1 text-center">
                            Following
                        </button>
                        <button class="bg-gray-700 hover:bg-gray-600 w-8 h-8 rounded-full flex items-center justify-center transition-colors group-hover:bg-red-500">
                            <i class="fas fa-user-minus text-xs"></i>
                        </button>
                    </div>
                </div>
                                
                <!-- View More -->
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                    <div class="text-center">
                        <div class="text-indigo-400 mb-2">
                            <i class="fas fa-chevron-down text-2xl"></i>
                        </div>
                        <div class="font-medium">View all 342 following</div>
                        <div class="text-gray-400 text-sm mt-1">Scroll for more</div>
                    </div>
                </div>
            </div>
        </div>
                
        <!-- Mod Actions Content (Hidden by default) -->
        <div id="modActionsContent" class="hidden">
            <div class="bg-gray-800 rounded-xl p-6 mb-6">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-gavel mr-2 text-indigo-400"></i> Recent Moderation Actions
                </h3>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700 text-gray-300">
                            <tr>
                                <th class="px-4 py-2 text-left">Action</th>
                                <th class="px-4 py-2 text-left">User</th>
                                <th class="px-4 py-2 text-left">Content</th>
                                <th class="px-4 py-2 text-left">Date</th>
                                <th class="px-4 py-2 text-left">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            <tr class="hover:bg-gray-700">
                                <td class="px-4 py-3">
                                    <span class="bg-red-900 text-red-300 px-2 py-1 rounded text-xs">Post Removed</span>
                                </td>
                                <td class="px-4 py-3">@spammer123</td>
                                <td class="px-4 py-3 truncate max-w-xs">Promoting scam project...</td>
                                <td class="px-4 py-3">10 min ago</td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Completed</span>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-700">
                                <td class="px-4 py-3">
                                    <span class="bg-yellow-900 text-yellow-300 px-2 py-1 rounded text-xs">Warning</span>
                                </td>
                                <td class="px-4 py-3">@new_trader</td>
                                <td class="px-4 py-3 truncate max-w-xs">Inappropriate language...</td>
                                <td class="px-4 py-3">1 hour ago</td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Completed</span>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-700">
                                <td class="px-4 py-3">
                                    <span class="bg-red-900 text-red-300 px-2 py-1 rounded text-xs">User Banned</span>
                                </td>
                                <td class="px-4 py-3">@scam_artist</td>
                                <td class="px-4 py-3 truncate max-w-xs">Multiple violations...</td>
                                <td class="px-4 py-3">3 hours ago</td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Completed</span>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-700">
                                <td class="px-4 py-3">
                                    <span class="bg-blue-900 text-blue-300 px-2 py-1 rounded text-xs">Post Approved</span>
                                </td>
                                <td class="px-4 py-3">@legit_user</td>
                                <td class="px-4 py-3 truncate max-w-xs">Market analysis...</td>
                                <td class="px-4 py-3">5 hours ago</td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Completed</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-center">
                    <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                        View All Actions
                    </button>
                </div>
            </div>
        </div>
                
        <!-- Analytics Content (Hidden by default) -->
        <div id="analyticsContent" class="hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-800 rounded-xl p-6">
                    <h3 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-indigo-400"></i> Community Growth
                    </h3>
                    <div class="h-64 bg-gray-700 rounded-lg p-4">
                        <!-- Chart placeholder -->
                        <div class="flex items-center justify-center h-full text-gray-500">
                            <i class="fas fa-chart-line text-4xl"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-800 rounded-xl p-6">
                    <h3 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-users mr-2 text-indigo-400"></i> User Engagement
                    </h3>
                    <div class="h-64 bg-gray-700 rounded-lg p-4">
                        <!-- Chart placeholder -->
                        <div class="flex items-center justify-center h-full text-gray-500">
                            <i class="fas fa-chart-pie text-4xl"></i>
                        </div>
                    </div>
                </div>
            </div>
                    
            <div class="bg-gray-800 rounded-xl p-6">
                <h3 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fas fa-tasks mr-2 text-indigo-400"></i> Moderation Metrics
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                    <div class="bg-gray-700 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-indigo-400">23</div>
                        <div class="text-gray-400">Today's Actions</div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-green-400">128</div>
                        <div class="text-gray-400">Resolved Issues</div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-yellow-400">5</div>
                        <div class="text-gray-400">Pending Reviews</div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold text-red-400">2</div>
                        <div class="text-gray-400">Bans Issued</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden modal">
        <div class="bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold">
                    <i class="fas fa-user-edit mr-2 text-indigo-400"></i> Edit Profile
                </h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-gray-400 mb-1">Name</label>
                    <input type="text" id="editName" value="Super Moderator" 
                           class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-gray-400 mb-1">Username</label>
                    <input type="text" id="editUsername" value="@super_moderator" 
                           class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-gray-400 mb-1">Bio</label>
                    <textarea id="editBio" class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg h-24 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">Senior Community Moderator | 5+ years experience | Ensuring safe and engaging discussions</textarea>
                </div>
                
                <div>
                    <label class="block text-gray-400 mb-1">Role</label>
                    <select id="editRole" class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option selected>Super Moderator</option>
                        <option>Lead Moderator</option>
                        <option>Senior Moderator</option>
                        <option>Junior Moderator</option>
                    </select>
                </div>
                
                <div class="pt-2">
                    <button id="saveProfile" class="bg-indigo-600 hover:bg-indigo-700 text-white w-full py-2 rounded-lg transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        const tabs = {
            postsTab: document.getElementById('postsTab'),
            followersTab: document.getElementById('followersTab'),
            followingTab: document.getElementById('followingTab'),
            modActionsTab: document.getElementById('modActionsTab'),
            analyticsTab: document.getElementById('analyticsTab')
        };
                
        const contents = {
            postsContent: document.getElementById('postsContent'),
            followersContent: document.getElementById('followersContent'),
            followingContent: document.getElementById('followingContent'),
            modActionsContent: document.getElementById('modActionsContent'),
            analyticsContent: document.getElementById('analyticsContent')
        };
                
        function switchTab(activeTab) {
            // Reset all tabs
            Object.values(tabs).forEach(tab => {
                tab.classList.remove('text-indigo-400', 'border-b-2', 'border-indigo-400');
                tab.classList.add('text-gray-400');
            });
                    
            // Hide all contents
            Object.values(contents).forEach(content => {
                if (content) content.classList.add('hidden');
            });
                    
            // Activate selected tab
            tabs[activeTab].classList.add('text-indigo-400', 'border-b-2', 'border-indigo-400');
            tabs[activeTab].classList.remove('text-gray-400');
                    
            // Show corresponding content
            const contentId = activeTab.replace('Tab', 'Content');
            if (contents[contentId]) {
                contents[contentId].classList.remove('hidden');
            }
        }
                
        // Add event listeners
        Object.keys(tabs).forEach(tabId => {
            if (tabs[tabId]) {
                tabs[tabId].addEventListener('click', () => switchTab(tabId));
            }
        });
                
        // Modal functionality
        const editBtn = document.getElementById('editBtn');
        const editModal = document.getElementById('editModal');
        const closeModal = document.getElementById('closeModal');
        const saveProfile = document.getElementById('saveProfile');
        
        // Profile elements
        const profileName = document.getElementById('profileName');
        const profileUsername = document.querySelector('.text-indigo-400.mb-2');
        const profileBio = document.getElementById('profileBio');
        const profileTradingStyle = document.getElementById('profileTradingStyle');
        
        // Edit form elements
        const editName = document.getElementById('editName');
        const editUsername = document.getElementById('editUsername');
        const editBio = document.getElementById('editBio');
        const editRole = document.getElementById('editRole');
        
        // Open modal
        editBtn.addEventListener('click', () => {
            editModal.classList.remove('hidden');
            setTimeout(() => {
                editModal.classList.add('modal-enter-active');
            }, 10);
        });
        
        // Close modal
        closeModal.addEventListener('click', () => {
            editModal.classList.remove('modal-enter-active');
            setTimeout(() => {
                editModal.classList.add('hidden');
            }, 300);
        });
        
        // Save profile changes
        saveProfile.addEventListener('click', () => {
            // Update profile with new values
            profileName.textContent = editName.value;
            profileUsername.textContent = editUsername.value;
            profileBio.textContent = editBio.value;
            profileTradingStyle.innerHTML = `
                <i class="fas fa-user-shield mr-1"></i> ${editRole.value}
            `;
            
            // Close modal
            editModal.classList.remove('modal-enter-active');
            setTimeout(() => {
                editModal.classList.add('hidden');
            }, 300);
            
            // Show success message
            const successMsg = document.createElement('div');
            successMsg.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2';
            successMsg.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>Profile updated successfully!</span>
            `;
            document.body.appendChild(successMsg);
                    
            setTimeout(() => {
                successMsg.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                setTimeout(() => successMsg.remove(), 500);
            }, 3000);
        });
                
        // Initialize with posts tab active
        switchTab('postsTab');
    </script>
</body>
</html>