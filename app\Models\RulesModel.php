<?php

namespace App\Models;

use CodeIgniter\Model;

class RulesModel extends Model
{
    protected $table = 'rules';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $useSoftDeletes = true;
    protected $useTimestamps = true;

    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'user_id',
        'name',
        'description',
        'category',
        'is_active'
    ];

    protected $validationRules = [
        'name' => 'required|max_length[255]',
        'description' => 'permit_empty|max_length[1000]',
        'category' => 'permit_empty|max_length[100]',
        'user_id' => 'permit_empty|integer',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    /**
     * Get rules for a specific user with statistics
     */
    public function getRulesForUser($userId)
    {
        try {
            // First, check if the new structure exists
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');

            if (in_array('name', $fields) && in_array('description', $fields) && in_array('category', $fields)) {
                // New structure exists
                $builder = $this->db->table('rules r');
                $builder->select('
                    r.id,
                    r.name,
                    r.description,
                    r.category,
                    r.user_id,
                    r.is_active,
                    r.created_at,
                    r.updated_at,
                    COALESCE(COUNT(tr.id), 0) as usage_count,
                    COALESCE(SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END), 0) as followed_count,
                    COALESCE(SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END), 0) as violated_count,
                    CASE
                        WHEN COUNT(tr.id) > 0 THEN
                            ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(tr.id)), 2)
                        ELSE 0
                    END as adherence_rate
                ');
                $builder->leftJoin('trade_rules tr', 'r.id = tr.rule_id');
                $builder->where('(r.user_id IS NULL OR r.user_id = ' . intval($userId) . ')');
                $builder->where('r.is_active', 1);
                $builder->groupBy('r.id, r.name, r.description, r.category, r.user_id, r.is_active, r.created_at, r.updated_at');
                $builder->orderBy('r.created_at', 'DESC');

                return $builder->get()->getResultArray();
            } else {
                // Old structure - transform to new format
                $rulesQuery = $db->query("
                    SELECT r.id, r.rule as name, r.created_at, r.updated_at,
                           COUNT(tr.id) as usage_count,
                           SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                           SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                           CASE
                               WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                               ELSE 0
                           END as adherence_rate
                    FROM rules r
                    LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                    WHERE r.deleted_at IS NULL
                    GROUP BY r.id, r.rule, r.created_at, r.updated_at
                    ORDER BY r.rule ASC
                ");

                $transformedRules = [];
                foreach ($rulesQuery->getResultArray() as $rule) {
                    // Try to detect category from rule name/text
                    $category = $this->detectCategoryFromRuleName($rule['name']);

                    $transformedRules[] = [
                        'id' => $rule['id'],
                        'name' => $rule['name'],
                        'description' => '', // No description in old structure
                        'category' => $category,
                        'user_id' => null, // All rules are global in old structure
                        'is_active' => 1,
                        'created_at' => $rule['created_at'],
                        'updated_at' => $rule['updated_at'],
                        'usage_count' => (int)$rule['usage_count'],
                        'followed_count' => (int)$rule['followed_count'],
                        'violated_count' => (int)$rule['violated_count'],
                        'adherence_rate' => (float)$rule['adherence_rate']
                    ];
                }

                return $transformedRules;
            }
        } catch (\Exception $e) {
            log_message('error', 'getRulesForUser error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a single rule by ID
     */
    public function getRuleById($id, $userId = null)
    {
        try {
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');

            if (in_array('name', $fields) && in_array('description', $fields) && in_array('category', $fields)) {
                // New structure
                $query = $db->query("SELECT * FROM rules WHERE id = ? AND deleted_at IS NULL", [$id]);
                return $query->getRowArray();
            } else {
                // Old structure - transform to new format
                $query = $db->query("SELECT * FROM rules WHERE id = ? AND deleted_at IS NULL", [$id]);
                $rule = $query->getRowArray();

                if ($rule) {
                    $category = $this->detectCategoryFromRuleName($rule['rule']);

                    return [
                        'id' => $rule['id'],
                        'name' => $rule['rule'],
                        'description' => '',
                        'category' => $category,
                        'user_id' => null,
                        'is_active' => 1,
                        'created_at' => $rule['created_at'],
                        'updated_at' => $rule['updated_at']
                    ];
                }
                return null;
            }
        } catch (\Exception $e) {
            log_message('error', 'getRuleById error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update a rule
     */
    public function updateRule($id, $data, $userId = null)
    {
        $builder = $this->where('id', $id);
        if ($userId) {
            $builder->where('(user_id IS NULL OR user_id = ' . intval($userId) . ')');
        }
        return $builder->set($data)->update();
    }

    /**
     * Detect category from rule name using keywords
     */
    private function detectCategoryFromRuleName($ruleName)
    {
        $ruleName = strtolower($ruleName);

        // Entry-related keywords
        $entryKeywords = ['entry', 'enter', 'buy', 'long', 'short', 'breakout', 'confirmation', 'signal', 'setup', 'pattern', 'wait for', 'candle close'];

        // Exit-related keywords
        $exitKeywords = ['exit', 'sell', 'close', 'profit', 'target', 'take profit', 'book profit', 'square off'];

        // Risk management keywords
        $riskKeywords = ['risk', 'stop', 'loss', 'stoploss', 'stop loss', 'capital', 'size', 'position', 'money', 'management', 'respect'];

        // Psychology keywords
        $psychologyKeywords = ['revenge', 'emotion', 'fear', 'greed', 'discipline', 'patience', 'calm', 'psychology', 'mental', 'feeling', 'avoid'];

        // Analysis keywords
        $analysisKeywords = ['analysis', 'analyze', 'timeframe', 'chart', 'technical', 'fundamental', 'research', 'study', 'check', 'higher'];

        // Check each category
        foreach ($entryKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'entry';
            }
        }

        foreach ($exitKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'exit';
            }
        }

        foreach ($riskKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'risk_management';
            }
        }

        foreach ($psychologyKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'psychology';
            }
        }

        foreach ($analysisKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'analysis';
            }
        }

        // Default to general if no keywords match
        return 'general';
    }

}
