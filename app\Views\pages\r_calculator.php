<link rel="stylesheet" href="<?= base_url() ?>assets/css/pages/r_calculator.css?v=<?= rand() ?>">

<div class="container mx-auto px-4 md:px-6 py-8 max-w-7xl">
    <!-- Header -->
    <header class="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
        <div>
            <h1 class="text-4xl font-bold dark:text-white text-gray-900 mb-2">Rupee <span
                    class="text-blue-500">Returns</span> Analyzer</h1>
            <p class="dark:text-slate-400 text-gray-600">Compare investment returns in Indian rupees</p>
        </div>
    </header>

    <!-- Main Calculator -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
        <!-- Input Form -->
        <div class="glass-effect bg-white dark:bg-gray-800 bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold dark:text-white text-gray-900">Calculator</h2>
                <div class="flex space-x-2">
                    <i class="fas fa-sliders-h text-blue-500"></i>
                </div>
            </div>

            <div class="space-y-5">
                <div>
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Investment
                        Amount (₹)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-rupee-sign"></i></span>
                        <input id="principal" type="number" value="100000"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Time Period
                        (Years)</label>
                    <input id="years" type="number" value="5" min="1" max="30"
                        class="input-field bg-gray-100 dark:bg-gray-700 w-full px-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                </div>

                <div>
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Comparison
                        Type</label>
                    <select id="comparisonType" class="input-field bg-gray-100 dark:bg-gray-700 w-full px-3 py-3 rounded-lg focus:outline-none">
                        <option value="fd_mf">FD vs Mutual Fund</option>
                        <option value="fd_gold">FD vs Gold</option>
                        <option value="gold_silver">Gold vs Silver</option>
                        <option value="realestate_stocks">Real Estate vs Stocks</option>
                    </select>
                </div>

                <div id="fdRateContainer">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">FD Interest
                        Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="fdRate" type="number" value="6.5" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div id="mfRateContainer">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Mutual Fund
                        Growth Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="mfRate" type="number" value="12" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div id="goldRateContainer" class="hidden">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Gold Growth
                        Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="goldRate" type="number" value="8" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div id="silverRateContainer" class="hidden">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Silver Growth
                        Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="silverRate" type="number" value="10" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div id="realestateRateContainer" class="hidden">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Real Estate
                        Growth Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="realestateRate" type="number" value="7.5" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <div id="stocksRateContainer" class="hidden">
                    <label class="block text-sm font-medium dark:text-slate-300 text-gray-700 mb-2">Stocks Growth
                        Rate (%)</label>
                    <div class="relative">
                        <span
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 dark:text-slate-400 text-gray-500"><i
                                class="fas fa-percent"></i></span>
                        <input id="stocksRate" type="number" value="15" step="0.1"
                            class="input-field bg-gray-100 dark:bg-gray-700 w-full pl-8 pr-3 py-3 rounded-lg placeholder-slate-500 focus:outline-none">
                    </div>
                </div>

                <button id="calculateBtn" style="color:white"
                    class="btn-primary w-full font-medium py-3 px-4 rounded-lg flex items-center justify-center mt-6">
                    <i class="fas fa-calculator mr-2"></i> Calculate Returns
                </button>
            </div>
        </div>

        <!-- Results -->
        <div class="glass-effect bg-white dark:bg-gray-800 p-6 rounded-xl col-span-1 lg:col-span-2">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold dark:text-white text-gray-900">Projected Results</h2>
                <div class="flex items-center text-blue-500 text-sm">
                    <i class="fas fa-info-circle mr-1"></i>
                    <span>Projection estimates only</span>
                </div>
            </div>

            <div id="resultsContainer" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div class="glass-card p-5 rounded-lg relative overflow-hidden pulse-animation">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-blue-800/10"></div>
                    <div class="flex justify-between items-start relative z-10">
                        <div>
                            <h3 class="font-medium dark:text-slate-300 text-gray-700">FD Returns</h3>
                            <p class="text-3xl font-bold text-blue-500 mt-3"><span id="fdResult">-</span></p>
                        </div>
                        <div class="bg-blue-600/20 p-2 rounded-lg">
                            <i class="fas fa-landmark text-blue-500"></i>
                        </div>
                    </div>
                </div>

                <div class="glass-card p-5 rounded-lg relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-teal-600/10 to-emerald-600/10"></div>
                    <div class="flex justify-between items-start relative z-10">
                        <div>
                            <h3 class="font-medium dark:text-slate-300 text-gray-700">Mutual Fund Returns</h3>
                            <p class="text-3xl font-bold text-teal-500 mt-3"><span id="mfResult">-</span></p>
                        </div>
                        <div class="bg-teal-600/20 p-2 rounded-lg">
                            <i class="fas fa-chart-line text-teal-500"></i>
                        </div>
                    </div>
                </div>

                <div class="glass-card p-5 rounded-lg relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-blue-800/10"></div>
                    <div class="flex justify-between items-start relative z-10">
                        <div>
                            <h3 class="font-medium dark:text-slate-300 text-gray-700">Difference</h3>
                            <p class="text-3xl font-bold text-blue-500 mt-3"><span id="diffResult">-</span></p>
                        </div>
                        <div class="bg-blue-600/20 p-2 rounded-lg">
                            <i class="fas fa-scale-balanced text-blue-500"></i>
                        </div>
                    </div>
                </div>

                <div class="glass-card p-5 rounded-lg relative overflow-hidden glow">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-blue-800/10 animate-pulse"></div>
                    <div class="flex justify-between items-start relative z-10">
                        <div>
                            <h3 class="font-medium dark:text-slate-300 text-gray-700">Better Option</h3>
                            <p class="text-3xl font-bold text-blue-500 mt-3"><span id="betterOption">-</span></p>
                        </div>
                        <div class="bg-blue-600/20 p-2 rounded-lg">
                            <i class="fas fa-trophy text-blue-500"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="font-semibold dark:text-white text-gray-900">Growth Comparison</h3>
                    <div class="flex items-center text-sm text-blue-500">
                        <i class="fas fa-expand mr-1 cursor-pointer hover:text-blue-700"></i>
                    </div>
                </div>
                <div class="chart-container glass-card rounded-xl overflow-hidden">
                    <canvas id="returnsChart"></canvas>
                    <div id="tooltip" class="tooltip"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Features -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
        <!-- Historical Performance -->
        <div class="glass-effect bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold dark:text-white text-gray-900">Historical Performance</h2>
                <div class="flex items-center text-sm text-blue-500">
                    <i class="fas fa-clock mr-1"></i>
                    <span>Last 10 years</span>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-slate-300 dark:divide-slate-700">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                                Year</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                                FD</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                                Mutual Fund</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                                Gold</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                                Silver</th>
                        </tr>
                    </thead>
                    <tbody id="historicalData" class="divide-y divide-slate-200 dark:divide-slate-800">
                        <!-- Data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="glass-effect bg-white dark:bg-gray-800 p-6 rounded-xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold dark:text-white text-gray-900">Risk Analysis</h2>
                <div class="flex items-center text-sm text-blue-500">
                    <i class="fas fa-shield-alt mr-1"></i>
                    <span>Assessment</span>
                </div>
            </div>
            <div id="riskAnalysis" class="space-y-6">
                <div>
                    <h3 class="font-medium dark:text-white text-gray-900 mb-3">Volatility Comparison</h3>
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-sm dark:text-slate-400 text-gray-600 mb-2">
                                <span class="flex items-center"><span
                                        class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span> Fixed Deposit</span>
                                <span>3.6% volatility</span>
                            </div>
                            <div class="w-full bg-slate-200 dark:bg-slate-800 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 15%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm dark:text-slate-400 text-gray-600 mb-2">
                                <span class="flex items-center"><span
                                        class="w-2 h-2 rounded-full bg-teal-500 mr-2"></span> Mutual Fund</span>
                                <span>15.2% volatility</span>
                            </div>
                            <div class="w-full bg-slate-200 dark:bg-slate-800 rounded-full h-2">
                                <div class="bg-teal-500 h-2 rounded-full" style="width: 60%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm dark:text-slate-400 text-gray-600 mb-2">
                                <span class="flex items-center"><span
                                        class="w-2 h-2 rounded-full bg-yellow-500 mr-2"></span> Gold</span>
                                <span>10.8% volatility</span>
                            </div>
                            <div class="w-full bg-slate-200 dark:bg-slate-800 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 40%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white text-gray-900 mb-3">Liquidity</h3>
                    <div class="space-y-2">
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Fixed Deposit</span>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-orange-500 mr-2"></span>
                                <span class="text-sm text-orange-500">Low Liquidity</span>
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Mutual Funds</span>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                <span class="text-sm text-green-500">High Liquidity</span>
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Commodities</span>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                                <span class="text-sm text-blue-500">Medium Liquidity</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="font-medium dark:text-white text-gray-900 mb-3">Tax Implications</h3>
                    <div class="space-y-2">
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Fixed Deposit</span>
                            <span class="text-sm text-red-500">Taxable Interest</span>
                        </div>
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Mutual Funds</span>
                            <span class="text-sm text-green-500">LTCG Benefits</span>
                        </div>
                        <div
                            class="flex justify-between items-center py-2 px-3 rounded-lg bg-slate-100 dark:bg-slate-800/50">
                            <span class="dark:text-slate-300 text-gray-700 font-medium">Commodities</span>
                            <span class="text-sm text-yellow-500">Varies by region</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Takeaways -->
    <div class="glass-effect bg-white dark:bg-gray-800 p-6 rounded-xl mb-8">
        <h2
            class="text-xl font-semibold mb-6 dark:text-white text-gray-900 border-b border-slate-300 dark:border-slate-700 pb-3">
            Investment Insights</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="glass-card p-5 rounded-lg flex flex-col">
                <div class="flex items-center">
                    <div class="bg-blue-100 dark:bg-blue-600/20 p-3 rounded-lg mr-4">
                        <i class="fas fa-chart-line text-blue-500 text-lg"></i>
                    </div>
                    <h3 class="font-medium dark:text-white text-gray-900">Compounding</h3>
                </div>
                <p class="text-sm dark:text-slate-400 text-gray-600 mt-4 leading-relaxed">Small annual return
                    differences create major final amount variations through compounding over time.</p>
            </div>
            <div class="glass-card p-5 rounded-lg flex flex-col">
                <div class="flex items-center">
                    <div class="bg-teal-100 dark:bg-teal-600/20 p-3 rounded-lg mr-4">
                        <i class="fas fa-balance-scale text-teal-500 text-lg"></i>
                    </div>
                    <h3 class="font-medium dark:text-white text-gray-900">Diversification</h3>
                </div>
                <p class="text-sm dark:text-slate-400 text-gray-600 mt-4 leading-relaxed">Balanced portfolios
                    across asset classes optimize risk-adjusted returns better than single investments.</p>
            </div>
            <div class="glass-card p-5 rounded-lg flex flex-col">
                <div class="flex items-center">
                    <div class="bg-yellow-100 dark:bg-yellow-600/20 p-3 rounded-lg mr-4">
                        <i class="fas fa-shield-alt text-yellow-500 text-lg"></i>
                    </div>
                    <h3 class="font-medium dark:text-white text-gray-900">Inflation Hedge</h3>
                </div>
                <p class="text-sm dark:text-slate-400 text-gray-600 mt-4 leading-relaxed">Equities and commodities
                    historically outpace inflation more effectively than fixed income investments.</p>
            </div>
            <div class="glass-card p-5 rounded-lg flex flex-col">
                <div class="flex items-center">
                    <div class="bg-rose-100 dark:bg-rose-600/20 p-3 rounded-lg mr-4">
                        <i class="fas fa-hand-holding-usd text-rose-500 text-lg"></i>
                    </div>
                    <h3 class="font-medium dark:text-white text-gray-900">Risk Management</h3>
                </div>
                <p class="text-sm dark:text-slate-400 text-gray-600 mt-4 leading-relaxed">Higher potential returns
                    typically come with increased volatility. Optimal allocation balances risk tolerance and time
                    horizon.</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center dark:text-slate-400 text-gray-600 text-sm pb-8">
        <p><i class="fas fa-info-circle mr-1 text-blue-500"></i> This tool provides projections only, not financial
            advice. Past performance doesn't guarantee future returns.</p>
    </footer>
</div>


<div id="glowEffect" class="fixed inset-0 pointer-events-none opacity-0"></div>