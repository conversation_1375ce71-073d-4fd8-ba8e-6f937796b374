document.addEventListener('DOMContentLoaded', function () {
    // Accordion functionality
    const accordionHeaders = document.querySelectorAll('.accordion-header');

    accordionHeaders.forEach(header => {
        header.addEventListener('click', function () {
            const accordionItem = this.parentElement;
            const accordionContent = this.nextElementSibling;
            const accordionIcon = this.querySelector('svg');

            // Toggle active class on content
            accordionContent.classList.toggle('active');

            // Rotate icon
            accordionIcon.classList.toggle('rotate-180');

            // Close other open accordions
            accordionHeaders.forEach(otherHeader => {
                if (otherHeader !== header) {
                    const otherContent = otherHeader.nextElementSibling;
                    const otherIcon = otherHeader.querySelector('svg');
                    otherContent.classList.remove('active');
                    otherIcon.classList.remove('rotate-180');
                }
            });
        });
    });
});