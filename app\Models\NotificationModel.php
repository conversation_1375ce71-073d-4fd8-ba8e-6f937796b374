<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationModel extends Model
{
    protected $table = 'notifications';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'user_id',
        'actor_id',
        'type',
        'target_type',
        'target_id',
        'message',
        'is_read'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = false;

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'actor_id' => 'required|integer',
        'type' => 'required|in_list[like_post,like_comment,comment_post,reply_comment,follow_user]',
        'target_type' => 'required|in_list[post,comment,user]',
        'target_id' => 'required|integer',
        'message' => 'required|max_length[500]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'actor_id' => [
            'required' => 'Actor ID is required',
            'integer' => 'Actor ID must be an integer'
        ],
        'type' => [
            'required' => 'Notification type is required',
            'in_list' => 'Invalid notification type'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Create a notification
     */
    public function createNotification($userId, $actorId, $type, $targetType, $targetId, $message)
    {
        // Don't create notification if user is notifying themselves
        if ($userId == $actorId) {
            return false;
        }

        // Check if similar notification already exists (to avoid spam)
        $existing = $this->where([
            'user_id' => $userId,
            'actor_id' => $actorId,
            'type' => $type,
            'target_type' => $targetType,
            'target_id' => $targetId
        ])->where('created_at >', date('Y-m-d H:i:s', strtotime('-1 hour')))->first();

        if ($existing) {
            // Update existing notification instead of creating new one
            return $this->update($existing['id'], [
                'message' => $message,
                'is_read' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        return $this->insert([
            'user_id' => $userId,
            'actor_id' => $actorId,
            'type' => $type,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'message' => $message,
            'is_read' => 0
        ]);
    }

    /**
     * Get user notifications with actor info
     */
    public function getUserNotifications($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' n');
        $builder->select('n.*, u.full_name as actor_name, u.profile as actor_profile, u.badge as actor_badge');
        $builder->join('users u', 'u.id = n.actor_id');
        $builder->where('n.user_id', $userId);
        $builder->orderBy('n.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        $notifications = $builder->get()->getResultArray();
        
        // Add time ago and format
        foreach ($notifications as &$notification) {
            $notification['time_ago'] = $this->timeAgo($notification['created_at']);
        }
        
        return $notifications;
    }

    /**
     * Get unread notification count
     */
    public function getUnreadCount($userId)
    {
        return $this->where('user_id', $userId)
                   ->where('is_read', 0)
                   ->countAllResults();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId)
    {
        return $this->where('id', $notificationId)
                   ->where('user_id', $userId)
                   ->update(['is_read' => 1]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead($userId)
    {
        return $this->where('user_id', $userId)
                   ->update(['is_read' => 1]);
    }

    /**
     * Delete old notifications (cleanup)
     */
    public function deleteOldNotifications($days = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Time ago helper
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . 'm ago';
        if ($time < 86400) return floor($time/3600) . 'h ago';
        if ($time < 2592000) return floor($time/86400) . 'd ago';
        if ($time < 31536000) return floor($time/2592000) . 'mo ago';
        return floor($time/31536000) . 'y ago';
    }
}
