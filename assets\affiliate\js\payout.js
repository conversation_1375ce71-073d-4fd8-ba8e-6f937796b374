$(document).ready(function () {
    $('#openWithdrawModal').on('click', function () {
        $('#withdrawModal').removeClass('hidden');
    });
    $('#closeWithdrawModal').on('click', function () {
        $('#withdrawModal').addClass('hidden');
    });

    fetchBankDetails();
    getWalletStats()
    loadPayouts();

    $('#withdrawForm').on('submit', function (e) {
        e.preventDefault();

        const amount = $('#withdrawAmount').val();

        if (!amount || amount < 100) {
            showToast('Minimum withdrawal amount is ₹100.', 'red');
            return;
        }

        $.ajax({
            url: base_url + 'requestWithdraw',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ amount }),
            success: function (res) {
                if (res.success) {
                    showToast('Withdrawal request submitted!', 'green');
                    $('#withdrawModal').addClass('hidden');
                    $('#withdrawForm')[0].reset();
                    getWalletStats()
                    loadPayouts();
                } else {
                    showToast(res.message || 'Something went wrong.', 'red');
                }
            }
        });
    });
});

function getWalletStats() {
    $.ajax({
        url: base_url + "getWalletStats",
        method: "GET",
        dataType: "json",
        success: function (response) {
            $('#availableBalance').text('₹' + response.available_balance);
            $('#pendingPayout').text('₹' + response.pending_payout);
            $('#totalEarned').text('₹' + response.total_earned);
        },
        error: function () {
            console.error('Failed to load wallet stats.');
        }
    });
}

function fetchBankDetails() {
    $.ajax({
        url: base_url + 'getBankDetails',
        method: 'GET',
        success: function (res) {
            if (res.success && res.data) {
                $('.displayAccountName').text(res.data.account_name);
                $('.displayBankName').text(res.data.bank_name);
                $('.displayAccountNumber').text(res.data.account_number);
                $('.displayIfscCode').text(res.data.ifsc_code);
                $('.displayBranchName').text(res.data.branch_name || '-');

                $('#accountName').val(res.data.account_name);
                $('#bankName').val(res.data.bank_name);
                $('#accountNumber').val(res.data.account_number);
                $('#ifscCode').val(res.data.ifsc_code);
                $('#branchName').val(res.data.branch_name);

                $('#noBankAccount').addClass('hidden');
                $('#bankAccountInfo').removeClass('hidden');
            } else {
                $('#accountName').val('');
                $('#bankName').val('');
                $('#accountNumber').val('');
                $('#ifscCode').val('');
                $('#branchName').val('');

                $('#noBankAccount').removeClass('hidden');
                $('#bankAccountInfo').addClass('hidden');
            }
        }
    });
}

// Elements
const bankPopup = document.getElementById('bankPopup');
const addBankBtn = document.getElementById('addBankBtn');
const addBankBtn2 = document.getElementById('addBankBtn2');
const editBankBtn = document.getElementById('editBankBtn');
const closePopupBtn = document.getElementById('closePopupBtn');
const cancelBtn = document.getElementById('cancelBtn');
const bankForm = document.getElementById('bankForm');
const popupTitle = document.getElementById('popupTitle');

const noBankAccount = document.getElementById('noBankAccount');
const bankAccountInfo = document.getElementById('bankAccountInfo');

const displayAccountName = document.getElementById('displayAccountName');
const displayBankName = document.getElementById('displayBankName');
const displayAccountNumber = document.getElementById('displayAccountNumber');
const displayIfscCode = document.getElementById('displayIfscCode');

// Open popup for adding bank account
addBankBtn.addEventListener('click', openBankPopup);
addBankBtn2.addEventListener('click', openBankPopup);

// Open popup for editing bank account
editBankBtn?.addEventListener('click', function () {
    popupTitle.textContent = 'Edit Bank Account';
    openBankPopup();
});

function openBankPopup() {
    bankPopup.style.display = 'flex';
}

// Close popup
function closeBankPopup() {
    bankPopup.style.display = 'none';
}

closePopupBtn.addEventListener('click', closeBankPopup);
cancelBtn.addEventListener('click', closeBankPopup);

// Handle form submission
$('#bankForm').on('submit', function (e) {
    e.preventDefault();

    // Clear previous errors
    $('.input-error').text('');

    const accountName = $('#accountName').val().trim();
    const bankName = $('#bankName').val().trim();
    const accountNumber = $('#accountNumber').val().trim();
    const ifscCode = $('#ifscCode').val().trim().toUpperCase();
    const branchName = $('#branchName').val().trim();

    let hasError = false;

    if (!accountName) {
        $('#accountNameError').text('Account holder name is required.');
        hasError = true;
    }

    if (!bankName) {
        $('#bankNameError').text('Bank name is required.');
        hasError = true;
    }

    if (!accountNumber || !/^\d{6,}$/.test(accountNumber)) {
        $('#accountNumberError').text('Enter a valid account number (minimum 6 digits).');
        hasError = true;
    }

    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode)) {
        $('#ifscCodeError').text('Enter a valid IFSC code (e.g., SBIN0001234).');
        hasError = true;
    }

    if (hasError) return;

    $.ajax({
        url: base_url + 'savePayoutDetails', // Change to your actual endpoint
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            account_name: accountName,
            bank_name: bankName,
            account_number: accountNumber,
            ifsc_code: ifscCode,
            branch_name: branchName
        }),
        success: function (res) {
            if (res.success) {
                closeBankPopup(); // Hide popup if needed
                showToast('Bank details saved successfully!', 'green');
                fetchBankDetails()
                
            } else {
                if (res.errors) {
                    $.each(res.errors, function (field, msg) {
                        $(`#${field}Error`).text(msg);
                    });
                } else {
                    showToast(res.message || 'Something went wrong.', 'red');
                }
            }
        },
        error: function () {
            showToast('Failed to save bank details.', 'red');
        }
    });
});

function loadPayouts(page = 1) {
    $.ajax({
        url: `${base_url}fetchPayouts?page=${page}`,
        method: "GET",
        dataType: "json",
        success: function (res) {
            const tbody = $('#payoutHistoryBody');
            const pagination = $('#pagination');
            tbody.empty();
            pagination.empty();

            if (res.payouts.length === 0) {
                tbody.append(`<tr><td colspan="5" class="text-center text-gray-500 py-4">No payout records found.</td></tr>`);
                return;
            }

            res.payouts.forEach(row => {
                tbody.append(`
                    <tr>
                        <td class="px-6 py-4 text-sm text-gray-500">${row.date}</td>
                        <td class="px-6 py-4 text-sm font-medium text-gray-900">${row.amount}</td>
                        <td class="px-6 py-4">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                ${row.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                ${row.status}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">${row.txn_id}</td>
                    </tr>
                `);
            });

            // Pagination
            const current = res.current_page;
            const total = res.total_pages;

            let paginationHTML = '';

            paginationHTML += `<button class="px-3 py-1 border rounded ${current === 1 ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-200'}" ${current === 1 ? 'disabled' : ''} onclick="loadPayouts(${current - 1})">&lt;</button>`;

            let start = Math.max(1, current - 2);
            let end = Math.min(total, current + 2);

            if (start > 1) {
                paginationHTML += `<button class="px-3 py-1 border rounded hover:bg-gray-200" onclick="loadPayouts(1)">1</button>`;
                if (start > 2) paginationHTML += `<span class="px-2">...</span>`;
            }

            for (let i = start; i <= end; i++) {
                paginationHTML += `<button class="px-3 py-1 border rounded ${i === current ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}" onclick="loadPayouts(${i})">${i}</button>`;
            }

            if (end < total) {
                if (end < total - 1) paginationHTML += `<span class="px-2">...</span>`;
                paginationHTML += `<button class="px-3 py-1 border rounded hover:bg-gray-200" onclick="loadPayouts(${total})">${total}</button>`;
            }

            paginationHTML += `<button class="px-3 py-1 border rounded ${current === total ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-200'}" ${current === total ? 'disabled' : ''} onclick="loadPayouts(${current + 1})">&gt;</button>`;

            pagination.html(paginationHTML);
        },
        error: function () {
            $('#payoutHistoryBody').html(`<tr><td colspan="5" class="text-center text-red-500 py-4">Failed to fetch data.</td></tr>`);
        }
    });
}

function showToast(message, color = 'green') {
    const toast = $(`
        <div class="fixed top-4 right-4 bg-${color}-600 text-white px-4 py-2 rounded shadow-lg z-50">
            ${message}
        </div>
    `);
    $('body').append(toast);
    setTimeout(() => toast.fadeOut(300, () => toast.remove()), 3000);
}

// Close popup when clicking outside
bankPopup.addEventListener('click', function (e) {
    if (e.target === bankPopup) {
        closeBankPopup();
    }
});