// User Management JavaScript

$(document).ready(function() {
    let currentPage = 1;
    let currentFilters = {};

    // Initialize
    loadUsersData();
    initializeEventHandlers();

    function initializeEventHandlers() {
        // Filter handlers
        $('#searchFilter').on('input', debounce(applyFilters, 500));
        $('#statusFilter, #planFilter').on('change', applyFilters);
        $('#dateFromFilter').on('change', applyFilters);
        
        // Button handlers
        $('#refreshData').on('click', function() {
            currentPage = 1;
            loadUsersData();
        });
        
        $('#exportCSV').on('click', exportToCSV);
        $('#clearFilters').on('click', clearFilters);
        
        // Modal handlers
        $('#closeModal').on('click', closeUserModal);
        $('#userModal').on('click', function(e) {
            if (e.target === this) closeUserModal();
        });
        
        // Close modal on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                closeUserModal();
            }
        });
    }

    function loadUsersData() {
        showLoading();
        
        const params = new URLSearchParams({
            page: currentPage,
            per_page: 10,
            ...currentFilters
        });

        $.ajax({
            url: `${ADMIN_BASE_URL}/get-users-data?${params}`,
            method: 'GET',
            success: function(response) {
                hideLoading();
                if (response.success) {
                    renderUsersTable(response.data);
                    renderPagination(response.pagination);
                    updateRecordCount(response.pagination.total_records);
                    updateStatsCards(response.stats);
                } else {
                    showToast('Failed to load users data', 'error');
                }
            },
            error: function(xhr) {
                hideLoading();
                if (xhr.status === 401) {
                    window.location.href = '/admin/login';
                } else {
                    showToast('Error loading data. Please try again.', 'error');
                }
            }
        });
    }

    // Helper function to detect dark mode
    function isDarkMode() {
        return document.documentElement.classList.contains('dark');
    }

    // Helper function to get theme-appropriate colors
    function getThemeColors() {
        const darkMode = isDarkMode();
        return {
            primaryText: darkMode ? '#f9fafb' : '#111827',
            secondaryText: darkMode ? '#d1d5db' : '#6b7280',
            accentColor: darkMode ? '#818cf8' : '#4f46e5',
            accentHover: darkMode ? '#6366f1' : '#3730a3'
        };
    }

    function renderUsersTable(data) {
        const tbody = $('#usersTableBody');
        tbody.empty();

        // Store current data for theme change refreshes
        window.currentUsersData = data;

        if (data.length === 0) {
            tbody.html(`
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                        <i class="fas fa-users text-4xl mb-4 text-gray-300 dark:text-gray-600"></i>
                        <p class="text-lg font-medium">No users found</p>
                        <p class="text-sm">Try adjusting your search criteria</p>
                    </td>
                </tr>
            `);
            return;
        }

        const colors = getThemeColors();

        data.forEach(user => {
            const statusBadge = getSubscriptionStatusBadge(user.sub_start, user.sub_end);
            const planBadge = getPlanBadge(user.sub_start, user.sub_end);
            const lastActivity = user.updated_at ? formatDate(user.updated_at) : 'Never';
            const registrationDate = formatDate(user.created_at);
            const emailVerified = user.is_verified ?
                '<i class="fas fa-check-circle text-green-500 ml-1" title="Email Verified"></i>' :
                '<i class="fas fa-exclamation-circle text-yellow-500 ml-1" title="Email Not Verified"></i>';

            const row = `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                                <span style="color: ${colors.accentColor}; font-weight: 500; font-size: 14px;">
                                    ${user.full_name ? user.full_name.charAt(0).toUpperCase() : 'U'}
                                </span>
                            </div>
                            <div class="ml-4">
                                <div style="font-size: 14px; font-weight: 500; color: ${colors.primaryText};">
                                    ${escapeHtml(user.full_name || 'N/A')}
                                </div>
                                <div style="font-size: 14px; color: ${colors.secondaryText};">
                                    ID: ${user.id}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div style="font-size: 14px; color: ${colors.primaryText};">
                            ${escapeHtml(user.email)}${emailVerified}
                        </div>
                        <div style="font-size: 14px; color: ${colors.secondaryText};">
                            ${user.google_id ? 'Google Account' : 'Regular Account'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${planBadge}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div style="font-size: 14px; color: ${colors.primaryText};">
                            ${registrationDate}
                        </div>
                        <div style="font-size: 14px; color: ${colors.secondaryText};">
                            ${user.total_referrals || 0} referrals
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${statusBadge}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div style="font-size: 14px; color: ${colors.primaryText};">
                            Last: ${lastActivity}
                        </div>
                        <div style="font-size: 14px; color: ${colors.secondaryText};">
                            ₹${formatAmount(user.total_withdrawals)} withdrawn
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <button onclick="viewUserDetails(${user.id})"
                                    style="color: ${colors.accentColor}; transition: color 0.2s;"
                                    onmouseover="this.style.color='${colors.accentHover}'"
                                    onmouseout="this.style.color='${colors.accentColor}'"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getSubscriptionStatusBadge(subStart, subEnd) {
        const now = new Date();
        const darkMode = isDarkMode();

        if (!subStart && !subEnd) {
            // No subscription data - Free user
            const bgColor = darkMode ? '#374151' : '#f3f4f6';
            const textColor = darkMode ? '#d1d5db' : '#111827';
            return `<span style="display: inline-flex !important; align-items: center !important; padding: 4px 8px !important; border-radius: 9999px !important; font-size: 12px !important; font-weight: 500 !important; background-color: ${bgColor} !important; color: ${textColor} !important;">Free</span>`;
        }

        if (subEnd) {
            const endDate = new Date(subEnd);
            const startDate = subStart ? new Date(subStart) : null;

            if (endDate > now) {
                // Active subscription
                if (startDate && startDate > new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000))) {
                    // Started within last 7 days - consider as renewed (Blue)
                    const bgColor = darkMode ? '#1e3a8a' : '#dbeafe';
                    const textColor = darkMode ? '#bfdbfe' : '#1e3a8a';
                    return `<span style="display: inline-flex !important; align-items: center !important; padding: 4px 8px !important; border-radius: 9999px !important; font-size: 12px !important; font-weight: 500 !important; background-color: ${bgColor} !important; color: ${textColor} !important;">Renewed</span>`;
                } else {
                    // Regular active subscription (Green)
                    const bgColor = darkMode ? '#14532d' : '#dcfce7';
                    const textColor = darkMode ? '#bbf7d0' : '#14532d';
                    return `<span style="display: inline-flex !important; align-items: center !important; padding: 4px 8px !important; border-radius: 9999px !important; font-size: 12px !important; font-weight: 500 !important; background-color: ${bgColor} !important; color: ${textColor} !important;">Active</span>`;
                }
            } else {
                // Expired subscription (Red)
                const bgColor = darkMode ? '#7f1d1d' : '#fee2e2';
                const textColor = darkMode ? '#fecaca' : '#7f1d1d';
                return `<span style="display: inline-flex !important; align-items: center !important; padding: 4px 8px !important; border-radius: 9999px !important; font-size: 12px !important; font-weight: 500 !important; background-color: ${bgColor} !important; color: ${textColor} !important;">Expired</span>`;
            }
        }

        // Has start date but no end date (shouldn't happen normally) (Yellow)
        const bgColor = darkMode ? '#78350f' : '#fef3c7';
        const textColor = darkMode ? '#fde68a' : '#78350f';
        return `<span style="display: inline-flex !important; align-items: center !important; padding: 4px 8px !important; border-radius: 9999px !important; font-size: 12px !important; font-weight: 500 !important; background-color: ${bgColor} !important; color: ${textColor} !important;">Pending</span>`;
    }

    function getPlanBadge(subStart, subEnd) {
        if (!subStart || !subEnd) {
            return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">Free</span>';
        }

        const startDate = new Date(subStart);
        const endDate = new Date(subEnd);
        const now = new Date();

        // Check if subscription is still active
        if (endDate <= now) {
            return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">Expired</span>';
        }

        // Calculate duration in days
        const durationInDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

        // Determine plan type based on duration
        if (durationInDays >= 300 && durationInDays <= 400) {
            // Yearly plan (around 365 days, with some tolerance)
            return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">Yearly</span>';
        } else if (durationInDays >= 25 && durationInDays <= 35) {
            // Monthly plan (around 30 days, with some tolerance)
            return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">Monthly</span>';
        } else {
            // Custom duration or unknown
            return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">Custom</span>';
        }
    }

    function updateStatsCards(stats) {
        // Update total users
        $('#totalUsers').html(stats.total_users.toLocaleString());
        $('#totalUsersChange').html(`${stats.monthly_growth >= 0 ? '+' : ''}${stats.monthly_growth}% from last month`);
        $('#totalUsersChange').removeClass('text-red-600 text-green-600').addClass(stats.monthly_growth >= 0 ? 'text-green-600' : 'text-red-600');

        // Update active subscriptions
        $('#activeUsers').html(stats.active_users.toLocaleString());
        const activePercentage = stats.total_users > 0 ? ((stats.active_users / stats.total_users) * 100).toFixed(1) : 0;
        $('#activeUsersChange').html(`${activePercentage}% of total users`);

        // Update new users this month
        $('#newUsers').html(stats.new_users_this_month.toLocaleString());
        $('#newUsersChange').html('This month');

        // Update paid users
        $('#premiumUsers').html(stats.premium_users.toLocaleString());
        const paidPercentage = stats.total_users > 0 ? ((stats.premium_users / stats.total_users) * 100).toFixed(1) : 0;
        const monthlyYearlyBreakdown = `${stats.monthly_users || 0}M / ${stats.yearly_users || 0}Y`;
        $('#premiumUsersChange').html(`${monthlyYearlyBreakdown} (${paidPercentage}%)`);
    }

    function renderPagination(pagination) {
        const container = $('#paginationContainer');
        
        if (pagination.total_pages <= 1) {
            container.html('');
            return;
        }

        let paginationHtml = `
            <div class="flex items-center justify-between">
                <div class="flex items-center text-sm text-gray-700 dark:text-gray-300">
                    Showing ${((pagination.current_page - 1) * pagination.per_page) + 1} to 
                    ${Math.min(pagination.current_page * pagination.per_page, pagination.total_records)} of 
                    ${pagination.total_records} results
                </div>
                <div class="flex items-center space-x-2">
        `;

        // Previous button
        if (pagination.current_page > 1) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page - 1})" 
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600">
                    Previous
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.current_page;
            paginationHtml += `
                <button onclick="changePage(${i})" 
                        class="px-3 py-2 text-sm font-medium ${isActive ? 
                            'text-white bg-indigo-600 border-indigo-600' : 
                            'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                        } border rounded-md">
                    ${i}
                </button>
            `;
        }

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page + 1})" 
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600">
                    Next
                </button>
            `;
        }

        paginationHtml += '</div></div>';
        container.html(paginationHtml);
    }

    function applyFilters() {
        currentFilters = {
            search: $('#searchFilter').val(),
            status: $('#statusFilter').val(),
            plan: $('#planFilter').val(),
            date_from: $('#dateFromFilter').val()
        };
        
        // Remove empty filters
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });
        
        currentPage = 1;
        loadUsersData();
    }

    function clearFilters() {
        $('#searchFilter').val('');
        $('#statusFilter').val('');
        $('#planFilter').val('');
        $('#dateFromFilter').val('');
        currentFilters = {};
        currentPage = 1;
        loadUsersData();
    }

    function updateRecordCount(count) {
        $('#recordCount').text(count.toLocaleString());
    }

    // Global functions
    window.changePage = function(page) {
        currentPage = page;
        loadUsersData();
    };

    window.viewUserDetails = function(userId) {
        showLoading();
        
        $.ajax({
            url: `${ADMIN_BASE_URL}/user-details/${userId}`,
            method: 'GET',
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showUserDetailsModal(response);
                } else {
                    showToast('Failed to load user details', 'error');
                }
            },
            error: function() {
                hideLoading();
                showToast('Error loading user details', 'error');
            }
        });
    };

    function showUserDetailsModal(data) {
        const user = data.user;
        const bankDetails = data.bank_details;
        const recentReferrals = data.recent_referrals;
        const recentWithdrawals = data.recent_withdrawals;

        const modalContent = `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- User Information -->
                <div class="space-y-6">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">User Information</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Full Name:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(user.full_name || 'N/A')}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Email:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(user.email)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Referral Code:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(user.refer_code || 'N/A')}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Account Type:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${user.google_id ? 'Google Account' : 'Regular Account'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Email Verified:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${user.is_verified ? 'Yes' : 'No'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Subscription Status:</span>
                                ${getSubscriptionStatusBadge(user.sub_start, user.sub_end)}
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Plan Type:</span>
                                ${getPlanBadge(user.sub_start, user.sub_end)}
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Registered:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${formatDate(user.created_at)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Last Activity:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${user.updated_at ? formatDate(user.updated_at) : 'Never'}</span>
                            </div>
                            ${user.sub_start ? `
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Subscription Start:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${formatDate(user.sub_start)}</span>
                            </div>
                            ` : ''}
                            ${user.sub_end ? `
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Subscription End:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${formatDate(user.sub_end)}</span>
                            </div>
                            ` : ''}
                            ${user.broker ? `
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Broker Connected:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(user.broker)}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Statistics</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">${user.total_referrals || 0}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Referrals</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">₹${formatAmount(user.total_withdrawals)}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Withdrawn</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">${user.total_trades || 0}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Trades</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-yellow-600">₹${formatAmount(user.pending_withdrawals)}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Pending</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Details & Recent Activity -->
                <div class="space-y-6">
                    ${bankDetails ? `
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Bank Details</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Account Holder:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(bankDetails.account_holder_name)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Account Number:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(bankDetails.account_number)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">IFSC Code:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(bankDetails.ifsc_code)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Bank Name:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(bankDetails.bank_name)}</span>
                            </div>
                        </div>
                    </div>
                    ` : `
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Bank Details</h4>
                        <p class="text-gray-600 dark:text-gray-400 text-center py-4">No bank details provided</p>
                    </div>
                    `}

                    <!-- Recent Referrals -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent Referrals</h4>
                        ${recentReferrals.length > 0 ? `
                            <div class="space-y-2">
                                ${recentReferrals.map(referral => `
                                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                                        <div>
                                            <div class="font-medium text-gray-900 dark:text-gray-100">${escapeHtml(referral.referred_name || 'N/A')}</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">${escapeHtml(referral.referred_email)}</div>
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">${formatDate(referral.created_at)}</div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <p class="text-gray-600 dark:text-gray-400 text-center py-4">No referrals yet</p>
                        `}
                    </div>

                    <!-- Recent Withdrawals -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent Withdrawals</h4>
                        ${recentWithdrawals.length > 0 ? `
                            <div class="space-y-2">
                                ${recentWithdrawals.map(withdrawal => `
                                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                                        <div>
                                            <div class="font-medium text-gray-900 dark:text-gray-100">₹${formatAmount(withdrawal.amount)}</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">${formatDate(withdrawal.created_at)}</div>
                                        </div>
                                        <div>${getStatusBadge(withdrawal.status)}</div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <p class="text-gray-600 dark:text-gray-400 text-center py-4">No withdrawals yet</p>
                        `}
                    </div>
                </div>
            </div>
        `;

        $('#modalContent').html(modalContent);
        $('#userModal').removeClass('hidden').addClass('flex');
    }

    function closeUserModal() {
        $('#userModal').addClass('hidden').removeClass('flex');
    }

    function exportToCSV() {
        showLoading();
        
        const params = new URLSearchParams({
            export: 'csv',
            ...currentFilters
        });

        window.location.href = `${ADMIN_BASE_URL}/export-users?${params}`;
        
        setTimeout(() => {
            hideLoading();
            showToast('Export started. Download will begin shortly.', 'success');
        }, 1000);
    }

    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function formatAmount(amount) {
        if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
            return '0.00';
        }
        const numericAmount = parseFloat(amount);
        if (isNaN(numericAmount)) {
            return '0.00';
        }
        return numericAmount.toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // Store current data for theme change refreshes
    window.currentUsersData = [];

    // Listen for theme changes and refresh table
    function observeThemeChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    // Theme changed, refresh the table to update colors
                    if (window.currentUsersData && window.currentUsersData.length > 0) {
                        renderUsersTable(window.currentUsersData);
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // Initialize theme observer
    observeThemeChanges();

    // Add User Form Handler
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        submitAddUserForm();
    });

    // Real-time validation
    $('#fullName').on('blur', function() {
        const value = $(this).val().trim();
        if (value && value.length < 2) {
            showFieldError('fullName', 'Full name must be at least 2 characters long');
        } else {
            $('#fullNameError').addClass('hidden');
        }
    });

    $('#email').on('blur', function() {
        const value = $(this).val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
            showFieldError('email', 'Please enter a valid email address');
        } else {
            $('#emailError').addClass('hidden');
        }
    });

    $('#password').on('blur', function() {
        const value = $(this).val();
        if (value && value.length < 6) {
            showFieldError('password', 'Password must be at least 6 characters long');
        } else {
            $('#passwordError').addClass('hidden');
        }
    });
});

// Add User Modal Functions
function openAddUserModal() {
    $('#addUserModal').removeClass('hidden');
    resetAddUserForm();
}

function closeAddUserModal() {
    $('#addUserModal').addClass('hidden');
    resetAddUserForm();
}

function resetAddUserForm() {
    $('#addUserForm')[0].reset();
    $('#paymentFields').addClass('hidden');
    $('#dateFields').addClass('hidden');
    $('.error-message').addClass('hidden');
    $('#addUserSpinner').addClass('hidden');
    $('#addUserSubmit span').text('Add User');
    // Clear payment options
    $('#paymentId').html('<option value="">Select a payment ID</option>');
}

function toggleDateFields() {
    const plan = $('#subscriptionPlan').val();
    const paymentFields = $('#paymentFields');
    const dateFields = $('#dateFields');

    if (plan === 'free') {
        paymentFields.addClass('hidden');
        dateFields.addClass('hidden');
        $('#paymentId, #startDate, #endDate').removeAttr('required');
    } else {
        paymentFields.removeClass('hidden');
        dateFields.removeClass('hidden');
        $('#paymentId, #startDate, #endDate').attr('required', 'required');
        loadAvailablePayments(); // Load payment options when showing paid plans
    }
}

function loadAvailablePayments() {
    const paymentSelect = $('#paymentId');

    // Show loading state
    paymentSelect.html('<option value="">Loading payments...</option>');

    $.ajax({
        url: ADMIN_BASE_URL + '/get-available-payments',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Clear existing options
                paymentSelect.html('<option value="">Select a payment ID</option>');

                // Add payment options
                if (response.data && response.data.length > 0) {
                    response.data.forEach(function(payment) {
                        paymentSelect.append(
                            `<option value="${payment.payment_id}" data-amount="${payment.amount}" data-period="${payment.period}">
                                ${payment.display_text}
                            </option>`
                        );
                    });
                } else {
                    paymentSelect.html('<option value="">No available payments found</option>');
                }
            } else {
                paymentSelect.html('<option value="">Error loading payments</option>');
                showNotification('error', 'Failed to load available payments');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading payments:', error);
            paymentSelect.html('<option value="">Error loading payments</option>');
            showNotification('error', 'Error loading available payments');
        }
    });
}



function togglePassword() {
    const passwordField = $('#password');
    const toggleIcon = $('#passwordToggleIcon');

    if (passwordField.attr('type') === 'password') {
        passwordField.attr('type', 'text');
        toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
    } else {
        passwordField.attr('type', 'password');
        toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
    }
}

function validateAddUserForm() {
    let isValid = true;

    // Validate full name
    const fullName = $('#fullName').val().trim();
    if (!fullName || fullName.length < 2) {
        showFieldError('fullName', 'Full name must be at least 2 characters long');
        isValid = false;
    }

    // Validate email
    const email = $('#email').val().trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
        showFieldError('email', 'Email is required');
        isValid = false;
    } else if (!emailRegex.test(email)) {
        showFieldError('email', 'Please enter a valid email address');
        isValid = false;
    }

    // Validate password
    const password = $('#password').val();
    if (!password) {
        showFieldError('password', 'Password is required');
        isValid = false;
    } else if (password.length < 6) {
        showFieldError('password', 'Password must be at least 6 characters long');
        isValid = false;
    }

    // Validate subscription plan specific fields
    const plan = $('#subscriptionPlan').val();
    if (plan !== 'free') {
        // Validate payment ID
        const paymentId = $('#paymentId').val();
        if (!paymentId) {
            showFieldError('paymentId', 'Payment ID is required for paid plans');
            isValid = false;
        }

        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();

        if (!startDate) {
            showFieldError('startDate', 'Start date is required for paid plans');
            isValid = false;
        }

        if (!endDate) {
            showFieldError('endDate', 'End date is required for paid plans');
            isValid = false;
        }

        if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
            showFieldError('endDate', 'End date must be after start date');
            isValid = false;
        }
    }

    return isValid;
}

function showFieldError(fieldName, message) {
    const errorElement = $(`#${fieldName}Error`);
    if (errorElement.length) {
        errorElement.text(message).removeClass('hidden');
    }
}

function submitAddUserForm() {
    // Clear previous errors
    $('.error-message').addClass('hidden');

    // Validate form client-side
    if (!validateAddUserForm()) {
        return;
    }

    // Show loading state
    $('#addUserSpinner').removeClass('hidden');
    $('#addUserSubmit span').text('Adding...');
    $('#addUserSubmit').prop('disabled', true);

    // Get form data
    const formData = {
        fullName: $('#fullName').val().trim(),
        email: $('#email').val().trim(),
        password: $('#password').val(),
        subscriptionPlan: $('#subscriptionPlan').val(),
        paymentId: $('#paymentId').val(),
        startDate: $('#startDate').val(),
        endDate: $('#endDate').val()
    };

    // Submit form
    $.ajax({
        url: ADMIN_BASE_URL + '/add-user',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', response.message);
                closeAddUserModal();
                loadUsersData(); // Refresh the users table
                loadUserStats(); // Refresh statistics
            } else {
                if (response.errors) {
                    // Show field-specific errors
                    Object.keys(response.errors).forEach(field => {
                        const errorElement = $(`#${field}Error`);
                        if (errorElement.length) {
                            errorElement.text(response.errors[field]).removeClass('hidden');
                        }
                    });
                } else {
                    showNotification('error', response.message || 'Failed to add user');
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Add user error:', error);
            showNotification('error', 'An error occurred while adding the user');
        },
        complete: function() {
            // Reset loading state
            $('#addUserSpinner').addClass('hidden');
            $('#addUserSubmit span').text('Add User');
            $('#addUserSubmit').prop('disabled', false);
        }
    });
}

function showNotification(type, message) {
    // Create notification element
    const notification = $(`
        <div class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas ${type === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400'} text-xl"></i>
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="$(this).closest('.fixed').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `);

    // Add to page
    $('body').append(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 5000);
}
