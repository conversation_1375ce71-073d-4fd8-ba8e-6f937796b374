<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rules Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }
        
        .dark body {
            background-color: #0f172a;
            color: #e2e8f0;
        }
        
        .rule-card {
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .rule-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .gradient-border {
            position: relative;
            border-radius: 1rem;
        }
        
        .gradient-border::before {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 1rem;
            background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .gradient-border:hover::before {
            opacity: 1;
        }
        
        .glow-effect {
            box-shadow: 0 0 15px rgba(99, 102, 241, 0.3);
        }
        
        .glow-effect:hover {
            box-shadow: 0 0 25px rgba(99, 102, 241, 0.5);
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring__circle {
            transition: stroke-dashoffset 0.5s ease;
            transform-origin: 50% 50%;
        }
        
        .modal-enter {
            opacity: 0;
            transform: scale(0.95);
        }
        
        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: all 0.2s ease-out;
        }
        
        .modal-exit {
            opacity: 1;
            transform: scale(1);
        }
        
        .modal-exit-active {
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.2s ease-in;
        }

        /* Theme toggle styles */
        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .theme-toggle:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .dark .theme-toggle:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .theme-toggle i {
            transition: all 0.3s ease;
        }

        .theme-toggle .sun {
            display: none;
        }

        .theme-toggle .moon {
            display: block;
        }

        .dark .theme-toggle .sun {
            display: block;
        }

        .dark .theme-toggle .moon {
            display: none;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <div class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6">
        <!-- Theme Toggle Button -->
        <div class="flex justify-end mb-4">
            <button id="themeToggle" class="theme-toggle">
                <i class="fas fa-sun sun text-yellow-500"></i>
                <i class="fas fa-moon moon text-indigo-400"></i>
            </button>
        </div>

        <!-- Page Header with Glass Morphism Effect -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Trading Rules
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2 text-sm sm:text-base max-w-lg">
                    Define and track your trading rules to improve consistency and performance. 
                    <span class="text-blue-500 dark:text-blue-400 font-medium">Your discipline creates profits.</span>
                </p>
            </div>
            <button id="createRuleBtn" class="group relative w-full sm:w-auto flex items-center justify-center px-5 py-3 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 text-sm sm:text-base shadow-lg hover:shadow-xl overflow-hidden">
                <span class="relative z-10 flex items-center">
                    <i class="fas fa-plus w-4 h-4 sm:w-5 sm:h-5 mr-2"></i>
                    Create New Rule
                </span>
                <span class="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            </button>
        </div>

        <!-- Rule Selector with Glass Card -->
        <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg border border-gray-200/50 dark:border-gray-700/50 transition-colors duration-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
                <h2 class="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-white mb-3 sm:mb-0 flex items-center">
                    <span class="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-lg mr-3">
                        <i class="fas fa-list-check w-5 h-5"></i>
                    </span>
                    Your Rule Collection
                </h2>
                <div class="relative w-full sm:w-72">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchRules" placeholder="Search rules..." class="w-full bg-gray-50/70 dark:bg-gray-700/70 border border-gray-300/50 dark:border-gray-600/50 rounded-xl py-2.5 pl-10 pr-4 text-sm sm:text-base text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all duration-300 backdrop-blur-sm">
                </div>
            </div>

            <!-- Filter Chips with Hover Effects -->
            <div class="flex flex-wrap gap-3 mb-6">
                <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-gray-100 dark:bg-gray-700 text-sm text-gray-800 dark:text-white border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 cursor-pointer shadow-lg shadow-gray-500/10 active overflow-hidden" data-filter="all">
                    <span class="relative z-10 flex items-center">
                        All Rules <span id="totalRulesCount" class="ml-2 text-gray-500 dark:text-gray-300">(0)</span>
                    </span>
                    <span class="absolute inset-0 bg-gray-200 dark:bg-gray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                </button>
                <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-blue-50 dark:bg-blue-900/20 text-sm text-blue-600 dark:text-blue-400 border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="most-used">
                    <span class="relative z-10 flex items-center">
                        Most Used <i class="fas fa-trending-up w-4 h-4 ml-2"></i>
                    </span>
                    <span class="absolute inset-0 bg-blue-100 dark:bg-blue-900/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                </button>
                <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-purple-50 dark:bg-purple-900/20 text-sm text-purple-600 dark:text-purple-400 border border-purple-100 dark:border-purple-800 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="high-impact">
                    <span class="relative z-10 flex items-center">
                        High Impact <i class="fas fa-bolt w-4 h-4 ml-2"></i>
                    </span>
                    <span class="absolute inset-0 bg-purple-100 dark:bg-purple-900/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                </button>
                <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-green-50 dark:bg-green-900/20 text-sm text-green-600 dark:text-green-400 border border-green-100 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="new">
                    <span class="relative z-10 flex items-center">
                        New <i class="fas fa-sparkles w-4 h-4 ml-2"></i>
                    </span>
                    <span class="absolute inset-0 bg-green-100 dark:bg-green-900/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                </button>
            </div>

            <!-- Rules Grid with Animated Cards -->
            <div id="rulesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                <!-- Sample Rule Card 1 -->
                <div class="rule-card gradient-border bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex justify-between items-start mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-md mr-3">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Wait for Confirmation</h3>
                        </div>
                        <span class="text-xs px-2.5 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">Entry</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">Always wait for candle close confirmation before entering a trade to avoid false breakouts.</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-green-500 dark:text-green-400 text-xs"></i>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">87% adherence</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-edit w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-chart-pie w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sample Rule Card 2 -->
                <div class="rule-card gradient-border bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex justify-between items-start mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center shadow-md mr-3">
                                <i class="fas fa-hand-holding-usd text-white text-lg"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">1% Risk Rule</h3>
                        </div>
                        <span class="text-xs px-2.5 py-1 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">Risk</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">Never risk more than 1% of your account on a single trade to preserve capital.</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-2">
                                <i class="fas fa-check text-green-500 dark:text-green-400 text-xs"></i>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">92% adherence</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-edit w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-chart-pie w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sample Rule Card 3 -->
                <div class="rule-card gradient-border bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex justify-between items-start mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center shadow-md mr-3">
                                <i class="fas fa-brain text-white text-lg"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">No Revenge Trading</h3>
                        </div>
                        <span class="text-xs px-2.5 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">Psychology</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">After a loss, take a break instead of immediately entering another trade.</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center mr-2">
                                <i class="fas fa-times text-red-500 dark:text-red-400 text-xs"></i>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">65% adherence</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-edit w-4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-chart-pie w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add New Rule Card with Animation -->
            <div class="flex items-center justify-center bg-gray-50/70 dark:bg-gray-700/70 rounded-xl p-6 border-2 border-dashed border-gray-300/50 dark:border-gray-600/50 hover:border-green-500/50 dark:hover:border-green-500/50 transition-all duration-300 cursor-pointer mt-6 glow-effect" id="addRuleCard">
                <div class="text-center group">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-plus text-white text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors duration-300">Add new trading rule</p>
                </div>
            </div>
        </div>

        <!-- Rules Analysis Section with Glass Morphism -->
        <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg border border-gray-200/50 dark:border-gray-700/50 transition-colors duration-200">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
                <div class="flex items-center space-x-3 mb-4 sm:mb-0">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                        <i class="fas fa-chart-line text-white text-lg sm:text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white">Rules Performance Analysis</h2>
                        <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">Track your trading discipline and rule adherence</p>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                    <select id="analyticsTimeframe" class="bg-gray-50/70 dark:bg-gray-700/70 border border-gray-300/50 dark:border-gray-600/50 rounded-lg px-3 py-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-colors backdrop-blur-sm">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                    <button id="refreshAnalytics" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg overflow-hidden">
                        <span class="relative z-10 flex items-center">
                            <i class="fas fa-sync-alt w-3 h-3 sm:w-4 sm:h-4 mr-2"></i>
                            Refresh
                        </span>
                        <span class="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-800 opacity-0 hover:opacity-100 transition-opacity duration-300"></span>
                    </button>
                </div>
            </div>

            <!-- Top Rules Chart with Progress Rings -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                <div class="bg-gradient-to-br from-slate-50/80 to-slate-100/80 dark:from-slate-800/60 dark:to-slate-700/60 rounded-xl p-5 sm:p-6 lg:p-7 border border-slate-200/50 dark:border-slate-600/30 shadow-md hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                    <div class="flex items-center space-x-3 mb-5 sm:mb-6 lg:mb-7">
                        <div class="w-8 h-8 sm:w-9 sm:h-9 rounded-lg bg-gradient-to-br from-indigo-500/80 to-violet-600/80 flex items-center justify-center shadow-sm">
                            <i class="fas fa-trophy text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg sm:text-xl font-bold text-slate-800 dark:text-slate-100">Top 5 Most Followed Rules</h3>
                    </div>
                    <div id="topRulesList" class="space-y-5">
                        <!-- Top Rule 1 -->
                        <div class="flex items-center">
                            <div class="relative w-12 h-12 mr-4">
                                <svg class="progress-ring w-full h-full" viewBox="0 0 36 36">
                                    <circle class="progress-ring__circle" stroke="#e2e8f0" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                                    <circle class="progress-ring__circle" stroke="#4f46e5" stroke-width="3" stroke-linecap="round" fill="transparent" r="16" cx="18" cy="18" stroke-dasharray="100 100" stroke-dashoffset="15"></circle>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-indigo-600 dark:text-indigo-400">85%</div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">1% Risk Rule</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Risk Management</p>
                            </div>
                            <div class="text-green-500 text-xs font-medium bg-green-100 dark:bg-green-900/50 px-2 py-1 rounded-full">+12%</div>
                        </div>

                        <!-- Top Rule 2 -->
                        <div class="flex items-center">
                            <div class="relative w-12 h-12 mr-4">
                                <svg class="progress-ring w-full h-full" viewBox="0 0 36 36">
                                    <circle class="progress-ring__circle" stroke="#e2e8f0" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                                    <circle class="progress-ring__circle" stroke="#8b5cf6" stroke-width="3" stroke-linecap="round" fill="transparent" r="16" cx="18" cy="18" stroke-dasharray="100 100" stroke-dashoffset="25"></circle>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-purple-600 dark:text-purple-400">75%</div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">Wait for Confirmation</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Entry Strategy</p>
                            </div>
                            <div class="text-green-500 text-xs font-medium bg-green-100 dark:bg-green-900/50 px-2 py-1 rounded-full">+8%</div>
                        </div>

                        <!-- Top Rule 3 -->
                        <div class="flex items-center">
                            <div class="relative w-12 h-12 mr-4">
                                <svg class="progress-ring w-full h-full" viewBox="0 0 36 36">
                                    <circle class="progress-ring__circle" stroke="#e2e8f0" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                                    <circle class="progress-ring__circle" stroke="#ec4899", stroke-width="3", stroke-linecap="round", fill="transparent", r="16", cx="18", cy="18", stroke-dasharray="100 100", stroke-dashoffset="40"></circle>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-pink-600 dark:text-pink-400">60%</div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">Set Stop Loss</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Risk Management</p>
                            </div>
                            <div class="text-yellow-500 text-xs font-medium bg-yellow-100 dark:bg-yellow-900/50 px-2 py-1 rounded-full">+5%</div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-slate-50/80 to-slate-100/80 dark:from-slate-800/60 dark:to-slate-700/60 rounded-xl p-5 sm:p-6 lg:p-7 border border-slate-200/50 dark:border-slate-600/30 shadow-md hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                    <div class="flex items-center space-x-3 mb-5 sm:mb-6 lg:mb-7">
                        <div class="w-8 h-8 sm:w-9 sm:h-9 rounded-lg bg-gradient-to-br from-orange-500/80 to-red-600/80 flex items-center justify-center shadow-sm">
                            <i class="fas fa-exclamation-circle text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg sm:text-xl font-bold text-slate-800 dark:text-slate-100">Least Used Rules</h3>
                    </div>
                    <div class="mt-6" id="leastUsedRulesContainer">
                        <!-- Least Used Rule 1 -->
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-red-500/20 to-orange-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-ban text-red-500 text-lg"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">No Revenge Trading</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Psychology</p>
                            </div>
                            <div class="text-red-500 text-xs font-medium bg-red-100 dark:bg-red-900/50 px-2 py-1 rounded-full">35%</div>
                        </div>

                        <!-- Least Used Rule 2 -->
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500/20 to-amber-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-yellow-500 text-lg"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">Wait for London Open</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Timing</p>
                            </div>
                            <div class="text-red-500 text-xs font-medium bg-red-100 dark:bg-red-900/50 px-2 py-1 rounded-full">42%</div>
                        </div>

                        <!-- Least Used Rule 3 -->
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-indigo-500/20 flex items-center justify-center mr-4">
                                <i class="fas fa-book text-blue-500 text-lg"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">Journal Every Trade</h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Habit</p>
                            </div>
                            <div class="text-red-500 text-xs font-medium bg-red-100 dark:bg-red-900/50 px-2 py-1 rounded-full">48%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Rule Modal (hidden by default) -->
        <div id="createRuleModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl w-full max-w-md mx-4 sm:mx-6 lg:mx-0 shadow-xl transform transition-all duration-300 modal-enter">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white">Create New Trading Rule</h3>
                        <button id="closeModal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                                    
                    <form id="ruleForm" class="space-y-4">
                        <div>
                            <label for="ruleName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rule Name</label>
                            <input type="text" id="ruleName" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" placeholder="e.g. Wait for Confirmation">
                        </div>
                                                
                        <div>
                            <label for="ruleCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                            <select id="ruleCategory" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                                <option value="entry">Entry Strategy</option>
                                <option value="exit">Exit Strategy</option>
                                <option value="risk">Risk Management</option>
                                <option value="psychology">Psychology</option>
                                <option value="habit">Habit</option>
                            </select>
                        </div>
                                                
                        <div>
                            <label for="ruleDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                            <textarea id="ruleDescription" rows="3" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" placeholder="Describe your rule in detail..."></textarea>
                        </div>
                                                
                        <div>
                            <label for="rulePriority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                            <div class="flex items-center space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="priority" value="low" class="form-radio h-4 w-4 text-blue-600 dark:text-blue-500 transition-colors">
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">Low</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="priority" value="medium" checked class="form-radio h-4 w-4 text-blue-600 dark:text-blue-500 transition-colors">
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">Medium</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="priority" value="high" class="form-radio h-4 w-4 text-blue-600 dark:text-blue-500 transition-colors">
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">High</span>
                                </label>
                            </div>
                        </div>
                                                
                        <div class="flex justify-end space-x-3 pt-4">
                            <button type="button" id="cancelRule" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-colors shadow-md">
                                Create Rule
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const createRuleBtn = document.getElementById('createRuleBtn');
        const addRuleCard = document.getElementById('addRuleCard');
        const createRuleModal = document.getElementById('createRuleModal');
        const closeModal = document.getElementById('closeModal');
        const cancelRule = document.getElementById('cancelRule');
        const ruleForm = document.getElementById('ruleForm');
        const searchRules = document.getElementById('searchRules');
        const filterBtns = document.querySelectorAll('.filter-btn');
        const refreshAnalytics = document.getElementById('refreshAnalytics');
        const analyticsTimeframe = document.getElementById('analyticsTimeframe');
        const rulesGrid = document.getElementById('rulesGrid');
        const totalRulesCount = document.getElementById('totalRulesCount');
        const themeToggle = document.getElementById('themeToggle');

        // Sample data
        let rules = [
            {
                id: 1,
                name: "Wait for Confirmation",
                category: "entry",
                description: "Always wait for candle close confirmation before entering a trade to avoid false breakouts.",
                priority: "high",
                adherence: 87,
                trend: "+8%",
                icon: "chart-line",
                color: "from-blue-500 to-indigo-600"
            },
            {
                id: 2,
                name: "1% Risk Rule",
                category: "risk",
                description: "Never risk more than 1% of your account on a single trade to preserve capital.",
                priority: "high",
                adherence极客时间: 92,
                trend: "+12%",
                icon: "hand-holding-usd",
                color: "from-red-500 to-pink-600"
            },
            {
                id: 3,
                name: "No Revenge Trading",
                category: "psychology",
                description: "After a loss, take a break instead of immediately entering another trade.",
                priority: "medium",
                adherence: 65,
                trend: "-5%",
                icon: "brain",
                color: "from-green-500 to-teal-600"
            }
        ];

        // Theme management
        function setTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            }
        }

        function toggleTheme() {
            if (document.documentElement.classList.contains('dark')) {
                setTheme('light');
            } else {
                setTheme('dark');
            }
        }

        // Check for saved theme preference
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            setTheme('dark');
        } else {
            setTheme('light');
        }

        // Update rules count
        function updateRulesCount() {
            totalRulesCount.textContent = `(${rules.length})`;
        }

        // Initialize the app
        function init() {
            updateRulesCount();
            
            // Add event listeners
            createRuleBtn.addEventListener('click', showCreateRuleModal);
            addRuleCard.addEventListener('click', showCreateRuleModal);
            closeModal.addEventListener('click', hideCreateRuleModal);
            cancelRule.addEventListener('click', hideCreateRuleModal);
            
            ruleForm.addEventListener('submit', function(e) {
                e.preventDefault();
                createNewRule();
            });
            
            searchRules.addEventListener('input', filterRules);
            
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterRulesByCategory(this.dataset.filter);
                });
            });
            
            refreshAnalytics.addEventListener('click', refreshAnalyticsData);
            
            // Theme toggle
            themeToggle.addEventListener('click', toggleTheme);
        }

        // Show create rule modal
        function showCreateRuleModal() {
            createRuleModal.classList.remove('hidden');
            setTimeout(() => {
                createRuleModal.querySelector('div').classList.add('modal-enter-active');
            }, 10);
        }

        // Hide create rule modal
        function hideCreateRuleModal() {
            createRuleModal.querySelector('div').classList.remove('modal-enter-active');
            setTimeout(() => {
                createRuleModal.classList.add('hidden');
                ruleForm.reset();
            }, 200);
        }

        // Create new rule
        function createNewRule() {
            const name = document.getElementById('ruleName').value;
            const category = document.getElementById('ruleCategory').value;
            const description = document.getElementById('ruleDescription').value;
            const priority = document.querySelector('input[name="priority"]:checked').value;
            
            if (!name || !description) {
                alert('Please fill in all required fields');
                return;
            }
            
            const newRule = {
                id: rules.length + 1,
                name,
                category,
                description,
                priority,
                adherence: Math.floor(Math.random() * 30) + 50, // Random adherence between 50-80%
                trend: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 10) + 1}%`,
                icon: getRandomIcon(category),
                color: getRandomColor(category)
            };
            
            rules.unshift(newRule);
            renderRules();
            updateRulesCount();
            hideCreateRuleModal();
        }

        // Get random icon based on category
        function getRandomIcon(category) {
            const icons = {
                entry: ['chart-line', 'chart-bar', 'chart-area', 'chart-pie'],
                exit: ['sign-out-alt', 'exchange-alt', 'redo', 'undo'],
                risk: ['shield-alt', 'exclamation-triangle', 'hand-holding-usd', 'money-bill-wave'],
                psychology: ['brain', 'meditation', 'balance-scale', 'user-astronaut'],
                habit: ['calendar-check', 'tasks', 'clipboard-list', 'book']
            };
            
            const categoryIcons = icons[category] || icons['entry'];
            return categoryIcons[Math.floor(Math.random() * categoryIcons.length)];
        }

        // Get random gradient color based on category
        function getRandomColor(category) {
            const colors = {
                entry: ['from-blue-500 to-indigo-600', 'from-cyan-500 to-blue-600', 'from-sky-500 to-blue-600'],
                exit: ['from-purple-500 to-pink-600', 'from-fuchsia-500 to-purple-600', 'from-violet-500 to-purple-600'],
                risk: ['from-red-500 to-pink-600', 'from-rose-500 to-pink-600', 'from-orange-500 to-red-600'],
                psychology: ['from-green-500 to-teal-600', 'from-emerald-500 to-teal-600', 'from-lime-500 to-green-600'],
                habit: ['from-yellow-500 to-amber-600', 'from-amber-500 to-orange-600', 'from-orange-500 to-amber-600']
            };
            
            const categoryColors = colors[category] || colors['entry'];
            return categoryColors[Math.floor(Math.random() * categoryColors.length)];
        }

        // Filter rules by search term
        function filterRules() {
            const searchTerm = searchRules.value.toLowerCase();
            const filteredRules = rules.filter(rule => 
                rule.name.toLowerCase().includes(searchTerm) ||
                rule.description.toLowerCase().includes(searchTerm)
            );
            renderRules(filteredRules);
        }

        // Filter rules by category
        function filterRulesByCategory(filter) {
            let filteredRules = [...rules];
            
            if (filter === 'most-used') {
                filteredRules.sort((a, b) => b.adherence - a.adherence);
            } else if (filter === 'high-impact') {
                filteredRules = filteredRules.filter(rule => rule.priority === 'high');
            } else if (filter === 'new') {
                filteredRules = filteredRules.slice(0, 3); // Show most recent
            }
            
            renderRules(filteredRules);
        }

        // Refresh analytics data
        function refreshAnalyticsData() {
            // Simulate loading
            refreshAnalytics.innerHTML = '<i class="fas fa-spinner animate-spin mr-2"></i> Refreshing...';
            
            setTimeout(() => {
                // In a real app, you would fetch new data here
                refreshAnalytics.innerHTML = '<i class="fas fa-check mr-2"></i> Refreshed';
                
                setTimeout(() => {
                    refreshAnalytics.innerHTML = '<i class="fas fa-sync-alt mr-2"></i> Refresh';
                }, 2000);
            }, 1500);
        }

        // Render rules to the grid
        function renderRules(rulesToRender = rules) {
            rulesGrid.innerHTML = '';
            
            if (rulesToRender.length === 0) {
                rulesGrid.innerHTML = `
                    <div class="col-span-full text-center py-10">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <i class="fas fa-search text-gray-400 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300">No rules found</h3>
                        <p class="text-gray-500 dark:text-gray-400 mt-1">Try adjusting your search or create a new rule</p>
                    </div>
                `;
                return;
            }
            
            rulesToRender.forEach(rule => {
                const adherenceColor = rule.adherence >= 80 ? 'bg-green-100 dark:bg-green-900' :
                                      rule.adherence >= 60 ? 'bg-yellow-100 dark:bg-yellow-900' :
                                      'bg-red-100 dark:bg-red-900';
                
                const adherenceIcon = rule.adherence >= 80 ? 'fa-check' :
                                     rule.adherence >= 60 ? 'fa-minus' :
                                     'fa-times';
                
                const adherenceIconColor = rule.adherence >= 80 ? 'text-green-500 dark:text-green-400' :
                                          rule.adherence >= 60 ? 'text-yellow-500 dark:text-yellow-400' :
                                          'text-red-500 dark:text-red-400';
                
                const categoryLabel = rule.category === 'entry' ? 'Entry' :
                                     rule.category === 'exit' ? 'Exit' :
                                     rule.category === 'risk' ? 'Risk' :
                                     rule.category === 'psychology' ? 'Psychology' : 'Habit';
                
                const categoryLabelColor = rule.category === 'entry' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                                          rule.category === 'exit' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :
                                          rule.category === 'risk' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
                                          rule.category === 'psychology' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                                          'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
                
                const ruleCard = document.createElement('div');
                ruleCard.className = 'rule-card gradient-border bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300';
                ruleCard.innerHTML = `
                    <div class="flex justify-between items-start mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br ${rule.color} flex items-center justify-center shadow-md mr-3">
                                <i class="fas fa-${rule.icon} text-white text-lg"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">${rule.name}</h3>
                        </div>
                        <span class="text-xs px-2.5 py-1 rounded-full ${categoryLabelColor}">${categoryLabel}</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">${rule.description}</p>
                    <div class="flex justify-between items-center">
                        <div极客时间 class="flex items-center">
                            <div class="w-6 h-6 rounded-full ${adherenceColor} flex items-center justify-center mr-2">
                                <i class="fas ${adherenceIcon} ${adherenceIconColor} text-xs"></i>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">${rule.adherence}% adherence</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-edit w-极客时间4 h-4"></i>
                            </button>
                            <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                <i class="fas fa-chart-pie w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                rulesGrid.appendChild(ruleCard);
            });
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>