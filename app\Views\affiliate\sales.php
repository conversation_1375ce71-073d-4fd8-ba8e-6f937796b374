<!-- Main Content -->
<div class="flex-1 overflow-auto">
    <!-- Mobile Menu Button -->
    <div class="md:hidden p-4">
        <button id="mobileMenuButton" class="mobile-menu-button text-gray-700">
            <i class="fas fa-bars text-xl"></i>
        </button>
    </div>

    <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Sales Dashboard</h2>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Total Sales Card -->
            <!-- Total Sales Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-700">Total Sales</h3>
                    <div class="p-2 rounded-full bg-indigo-100 text-indigo-600">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-800 mb-2"><?= $totalSales ?></p>
                <div class="text-sm text-gray-500">
                    <p>Last 7 Days: <?= $last7Sales ?></p>
                    <p>Last 30 Days: <?= $last30Sales ?></p>
                </div>
            </div>

            <!-- Total Commission Earned Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-700">Total Commission Earned</h3>
                    <div class="p-2 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-800 mb-2">₹<?= number_format($lifetimeCommission, 2) ?></p>
                <div class="text-sm text-gray-500">
                    <p>Last 7 Days: ₹<?= number_format($last7Commission, 2) ?></p>
                    <p>Last 30 Days: ₹<?= number_format($last30Commission, 2) ?></p>
                </div>
            </div>

            <!-- Tier Level Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-700">Tier Level</h3>
                    <div class="p-2 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-trophy"></i>
                    </div>
                </div>
                <div class="mb-2">
                    <span class="inline-block w-3 h-3 rounded-full bg-yellow-400 mr-2"></span>
                    <span class="font-medium">Current: <?= $tier ?></span>
                </div>
                <?php if ($nextTier != 'N/A'): ?>
                    <div class="mb-2">
                        <span class="inline-block w-3 h-3 rounded-full bg-gray-400 mr-2"></span>
                        <span class="font-medium">Next Tier: <?= $nextTier ?></span>
                    </div>
                    <p class="text-sm text-gray-500">Sales Needed: <?= $salesNeeded ?> more</p>
                <?php else: ?>
                    <p class="text-sm text-green-600 font-medium">You’re in the highest tier!</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tier Details Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Commission Tier</h2>

            <!-- Progress Bar Section -->
            <div class="mb-6">
                <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium">Bronze</span>
                    <span class="text-sm font-medium">Silver</span>
                    <span class="text-sm font-medium">Gold</span>
                </div>
                <div class="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-yellow-400 to-yellow-600"
                        style="width: <?= min(100, ($totalSales / 100) * 100) ?>%;"></div>
                </div>
                <div class="flex justify-between mt-1">
                    <span class="text-xs text-gray-500">0</span>
                    <span class="text-xs text-gray-500">50</span>
                    <span class="text-xs text-gray-500">100</span>
                </div>
            </div>

            <!-- Tier Cards -->
            <div class="grid grid-cols-1 md:grid-cols-<?= $tier == 'Influencer' ? '4' : '3' ?> gap-4">

                <?php if ($tier == 'Influencer'): ?>
                    <div class="border border-purple-300 rounded-lg p-4 bg-purple-50">
                        <div class="flex items-center mb-2">
                            <span class="inline-block w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            <h3 class="font-medium text-purple-800">Influencer</h3>
                        </div>
                        <p class="text-gray-700">20% commission</p>
                    </div>
                <?php endif; ?>

                <div
                    class="border border-gray-200 rounded-lg p-4 <?= $tier == 'Bronze' ? 'bg-yellow-50 border-yellow-400' : '' ?>">
                    <div class="flex items-center mb-2">
                        <span class="inline-block w-3 h-3 rounded-full bg-yellow-400 mr-2"></span>
                        <h3 class="font-medium">Bronze</h3>
                    </div>
                    <p class="text-gray-700">10% commission</p>
                </div>

                <div
                    class="border border-gray-200 rounded-lg p-4 <?= $tier == 'Silver' ? 'bg-gray-100 border-gray-400' : '' ?>">
                    <div class="flex items-center mb-2">
                        <span class="inline-block w-3 h-3 rounded-full bg-gray-400 mr-2"></span>
                        <h3 class="font-medium">Silver</h3>
                    </div>
                    <p class="text-gray-700">15% commission after 50 referrals</p>
                </div>

                <div
                    class="border border-gray-200 rounded-lg p-4 <?= $tier == 'Gold' ? 'bg-yellow-100 border-yellow-600' : '' ?>">
                    <div class="flex items-center mb-2">
                        <span class="inline-block w-3 h-3 rounded-full bg-yellow-600 mr-2"></span>
                        <h3 class="font-medium">Gold</h3>
                    </div>
                    <p class="text-gray-700">20% commission after 100 referrals</p>
                </div>
            </div>

        </div>


        <!-- Sales History Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">Sales History</h2>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sale Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Commission
                                Earned</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                        </tr>
                    </thead>
                    <tbody id="referralTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="4" class="text-center py-6 text-gray-400">Loading...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="paginationLinks" class="flex justify-start items-center gap-2 mt-4 text-sm text-gray-700 p-3">
            </div>

            <div class="p-4 border-t border-gray-200 bg-gray-50">
                <p class="text-sm text-gray-500">Commission is recorded only after a successful purchase and refund
                    window.</p>
            </div>
        </div>
    </div>
</div>