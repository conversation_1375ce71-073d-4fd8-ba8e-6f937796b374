/* Modern Card Styles */
.modern-card {
    position: relative;
    /* background: var(--card-bg); */
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    overflow: hidden;
    z-index: 1;
    height: 100%;
}

.modern-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(79, 70, 229, 0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
    border-color: rgba(79, 70, 229, 0.3);
}

.modern-card:hover::before {
    opacity: 1;
}

.modern-card .card-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(79, 70, 229, 0) 100%);
    color: theme('colors.primary');
    font-size: 1.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.1);
}

.modern-card:hover .card-icon {
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.2) 0%, rgba(79, 70, 229, 0) 100%);
    box-shadow: 0 0 10px rgba(79, 70, 229, 0.2);
    animation: glow-pulse 2s ease-in-out infinite;
}

/* Enhanced Card Badges */
.card-badge {
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.35rem 0.8rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(79, 70, 229, 0.1);
    color: theme('colors.primary');
    box-shadow: inset 0 0 0 1px rgba(79, 70, 229, 0.2);
}

.badge-primary {
    background: rgba(79, 70, 229, 0.15);
    color: theme('colors.primary');
    box-shadow: inset 0 0 0 1px rgba(79, 70, 229, 0.3);
}

.badge-secondary {
    background: rgba(236, 72, 153, 0.15);
    color: theme('colors.secondary');
    box-shadow: inset 0 0 0 1px rgba(236, 72, 153, 0.3);
}

.badge-success {
    background: rgba(16, 185, 129, 0.15);
    color: theme('colors.success');
    box-shadow: inset 0 0 0 1px rgba(16, 185, 129, 0.3);
}

.badge-accent {
    background: rgba(245, 158, 11, 0.15);
    color: theme('colors.accent');
    box-shadow: inset 0 0 0 1px rgba(245, 158, 11, 0.3);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.15);
    color: theme('colors.danger');
    box-shadow: inset 0 0 0 1px rgba(239, 68, 68, 0.3);
}

.modern-card:hover .badge-primary {
    background: rgba(79, 70, 229, 0.2);
}

.modern-card:hover .badge-secondary {
    background: rgba(236, 72, 153, 0.2);
}

.modern-card:hover .badge-success {
    background: rgba(16, 185, 129, 0.2);
}

.modern-card:hover .badge-accent {
    background: rgba(245, 158, 11, 0.2);
}

.modern-card:hover .badge-danger {
    background: rgba(239, 68, 68, 0.2);
}

/* Enhanced Card Links */
.modern-card .card-link-primary {
    color: theme('colors.primary');
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
    position: relative;
    display: inline-flex;
    align-items: center;
}

.modern-card .card-link-secondary {
    color: theme('colors.secondary');
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
    position: relative;
    display: inline-flex;
    align-items: center;
}

.modern-card .card-link-primary::after,
.modern-card .card-link-secondary::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    transition: width 0.3s ease;
    background: currentColor;
}

.modern-card .card-link-primary:hover::after,
.modern-card .card-link-secondary:hover::after {
    width: 100%;
}

.gradient-text {
    background: linear-gradient(90deg, theme('colors.primary'), theme('colors.secondary'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 300% 300%;
    animation: gradient 4s ease infinite;
}

/* Grid Layout */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Responsive Adjustments */
@media (min-width: 1024px) {
    .tools-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}