<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use <PERSON>Igniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

define('ENCRYPTION_KEY', 'r4nd0mS3cur3KeyForCooki3s1234');
define('CIPHER_METHOD', 'AES-256-CBC');

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var list<string>
     */
    protected $helpers = [];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * @return void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = service('session');
    }

    protected function encrypt_cookie_value($data)
    {
        $iv_length = openssl_cipher_iv_length(CIPHER_METHOD);
        $iv = openssl_random_pseudo_bytes($iv_length);

        $encrypted_data = openssl_encrypt($data, CIPHER_METHOD, ENCRYPTION_KEY, 0, $iv);

        if ($encrypted_data === false) {
            return false;
        }

        // Store both encrypted data and IV (base64 encoded)
        return base64_encode($encrypted_data . '::' . $iv);
    }

    protected function decrypt_cookie_value($encrypted)
    {
        $cookie_data = base64_decode($encrypted);
        if ($cookie_data === false || strpos($cookie_data, '::') === false) {
            return false; // Corrupt or invalid
        }

        list($encrypted_data, $iv) = explode('::', $cookie_data, 2);

        $decrypted_data = openssl_decrypt($encrypted_data, CIPHER_METHOD, ENCRYPTION_KEY, 0, $iv);

        return $decrypted_data !== false ? $decrypted_data : false;
    }

    protected function checkAuthentication()
    {
        helper('cookie'); // Load cookie helper if not autoloaded

        // Check if cookie exists
        $encryptedUserId = get_cookie('user_session');
        if (!$encryptedUserId) {
            return redirect()->to('Login');
        }

        // Decrypt the cookie
        $userId = $this->decrypt_cookie_value($encryptedUserId);

        // If decryption failed or empty userId
        if ($userId === false || empty($userId)) {
            delete_cookie('user_session');
            return redirect()->to('Login');
        }

        // Load user from database
        $userModel = new \App\Models\UserModel();
        $user = $userModel->find($userId);

        // If user not found
        if (!$user) {
            delete_cookie('user_session');
            return redirect()->to('Login');
        }

        // All checks passed
        return true;
    }

}
