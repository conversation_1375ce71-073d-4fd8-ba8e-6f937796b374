<div class="content-area flex flex-col min-h-screen">
    <!-- Header with glass effect -->
    <header class="glass sticky top-0 shadow-sm" style="z-index: 1;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <button id="sidebarToggle" class="sidebar-toggle mr-2 text-gray-600 dark:text-gray-300">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Market hours indicator -->
                    <!-- <div
                        class="hidden md:flex items-center px-3 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 text-sm">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2 animate-pulse"></div>
                        <span>Market Open</span>
                    </div> -->

                    <!-- Search bar -->
                    <div class="relative hidden md:block_">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                            class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-transparent"
                            placeholder="Search...">
                    </div>

                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2">
                            <label class="relative inline-block w-14 h-8">
                                <input type="checkbox" id="darkModeToggle" class="peer sr-only">

                                <!-- Track -->
                                <div class="w-full h-full bg-gray-300 dark:bg-gray-700 rounded-full transition-colors">
                                </div>

                                <!-- Thumb -->
                                <div class="absolute top-0.5 left-0.5 w-7 h-7 rounded-full bg-white dark:bg-gray-200 shadow-md flex items-center justify-center transition-all duration-300 peer-checked:translate-x-6"
                                    id="toggleThumb">
                                    <i class="fas fa-sun text-yellow-500" id="toggleSun"></i>
                                    <i class="fas fa-moon text-gray-700 hidden" id="toggleMoon"></i>
                                </div>
                            </label>
                        </div>
                        <div class="relative">
                            <button id="userMenuButton" class="flex items-center space-x-2 focus:outline-none">
                                <div
                                    class="w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center text-white overflow-hidden">
                                    <?php if (!empty($userDetails['profile'])): ?>
                                        <img src="<?= esc($userDetails['profile']) ?>" alt="Profile"
                                            class="w-full h-full object-cover rounded-full">
                                    <?php else: ?>
                                        <span><?= strtoupper(substr($userDetails['full_name'], 0, 1)) ?></span>
                                    <?php endif; ?>
                                </div>
                                <span class="hidden md:inline"><?= $userDetails['full_name'] ?></span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- User dropdown -->
                            <div id="userDropdown"
                                class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 py-1 border border-gray-200 dark:border-gray-700">
                                <a href="<?= base_url('MyProfile') ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                                <!-- <a href="#"
                                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a> -->
                                <a href="<?= base_url('logout') ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Market ticker -->
    <div class="bg-gray-100 dark:bg-gray-800 py-2 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="ticker-container">
                <div class="ticker" id="ticker-content">
                    <!-- Dynamic ticker items will be injected here -->
                </div>
            </div>
        </div>
    </div>


    <!-- Main Content -->
    <main class="flex-grow">