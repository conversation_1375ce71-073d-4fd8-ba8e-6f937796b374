<!-- Main Content -->
<div class="flex-1 overflow-auto">
    <!-- Mobile Menu Button -->
    <div class="md:hidden p-4">
        <button id="mobileMenuButton" class="mobile-menu-button text-gray-700">
            <i class="fas fa-bars text-xl"></i>
        </button>
    </div>

    <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Payout Details</h2>

        <!-- Balance Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">Available Balance</p>
                        <p id="availableBalance" class="text-2xl font-bold text-gray-800">₹0.00</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-wallet text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">Pending Payout</p>
                        <p id="pendingPayout" class="text-2xl font-bold text-gray-800">₹0.00</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">Total Earned</p>
                        <p id="totalEarned" class="text-2xl font-bold text-gray-800">₹0.00</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-rupee-sign text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>


        <!-- Bank Details Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800">Your Bank Details (India Only)</h2>
                <button id="addBankBtn"
                    class="hidden bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 text-sm">
                    <i class="fas fa-plus mr-1"></i> Add Bank Account
                </button>
            </div>

            <div id="bankDetailsContainer">
                <!-- This will be filled dynamically based on whether bank details exist -->
                <div id="noBankAccount" class="text-center py-8 bg-gray-50 rounded-lg">
                    <i class="fas fa-university text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-700">No Bank Account Added</h3>
                    <p class="text-gray-500 mb-4">Add your bank details to receive payouts</p>
                    <button id="addBankBtn2"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition duration-200">
                        Add Bank Account
                    </button>
                </div>

                <!-- This would be shown when bank details exist -->
                <div id="bankAccountInfo" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <p class="text-sm text-gray-500">Account Holder Name</p>
                            <p class="font-medium displayAccountName">John Doe</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Bank Name</p>
                            <p class="font-medium displayBankName">HDFC Bank</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Account Number</p>
                            <p class="font-medium displayAccountNumber">XXXXXX7890</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">IFSC Code</p>
                            <p class="font-medium displayIfscCode">HDFC0000123</p>
                        </div>
                    </div>
                    <button id="editBankBtn" class="text-blue-600 hover:text-blue-800 font-medium">
                        <i class="fas fa-edit mr-1"></i> Edit Details
                    </button>
                </div>
            </div>
        </div>

        <!-- Payout History -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Payout History</h2>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Transaction ID
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="payoutHistoryBody">
                        <!-- Filled by AJAX -->
                    </tbody>
                </table>
            </div>

            <div id="pagination" class="mt-4 flex justify-start flex-wrap gap-2 text-sm"></div>


            <p class="text-gray-500 text-sm mt-6 italic">Payouts are processed weekly to your registered Indian bank
                account. Minimum payout amount is ₹100.</p>

            <div class="mt-6 flex justify-end">
                <button id="openWithdrawModal"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition duration-200">
                    Request Payout
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Bank Account Popup -->
<div id="bankPopup" class="popup-overlay">
    <div class="popup-container">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800" id="popupTitle">Add Bank Account</h3>
                <button id="closePopupBtn" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="bankForm">
                <div class="space-y-4">
                    <div>
                        <label for="accountName" class="block text-sm font-medium text-gray-700 mb-1">Account Holder
                            Name</label>
                        <input type="text" id="accountName"
                            class="bank-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p id="accountNameError" class="text-sm text-red-500 mt-1 input-error"></p>
                    </div>

                    <div>
                        <label for="bankName" class="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                        <input type="text" id="bankName"
                            class="bank-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p id="bankNameError" class="text-sm text-red-500 mt-1 input-error"></p>
                    </div>

                    <div>
                        <label for="accountNumber" class="block text-sm font-medium text-gray-700 mb-1">Account
                            Number</label>
                        <input type="text" id="accountNumber"
                            class="bank-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p id="accountNumberError" class="text-sm text-red-500 mt-1 input-error"></p>
                    </div>

                    <div>
                        <label for="ifscCode" class="block text-sm font-medium text-gray-700 mb-1">IFSC Code</label>
                        <input type="text" id="ifscCode"
                            class="bank-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p id="ifscCodeError" class="text-sm text-red-500 mt-1 input-error"></p>
                    </div>

                    <div>
                        <label for="branchName" class="block text-sm font-medium text-gray-700 mb-1">Branch Name
                            (optional)</label>
                        <input type="text" id="branchName"
                            class="bank-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" id="cancelBtn"
                        class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition duration-200">
                        Save Details
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="withdrawModal" class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">

        <!-- Close button -->
        <button id="closeWithdrawModal" class="absolute top-2 right-2 text-gray-500 hover:text-gray-800 text-xl">
            &times;
        </button>

        <h2 class="text-xl font-semibold text-gray-800 mb-4">Withdraw Funds</h2>

        <!-- Show saved bank account info -->
        <div class="mb-4 p-3 bg-gray-100 rounded border border-gray-200 text-sm text-gray-700">
            <p><strong>Account Holder:</strong> <span class="displayAccountName"></span></p>
            <p><strong>Bank:</strong> <span class="displayBankName"></span></p>
            <p><strong>Account No:</strong> <span class="displayAccountNumber"></span></p>
            <p><strong>IFSC:</strong> <span class="displayIfscCode"></span></p>
        </div>

        <!-- Withdrawal Form -->
        <form id="withdrawForm">
            <div class="mb-4">
                <label for="withdrawAmount" class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                <input type="number" id="withdrawAmount" name="amount"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:outline-none"
                    placeholder="Enter withdrawal amount" min="100">
            </div>

            <button type="submit"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                Submit Withdrawal Request
            </button>
        </form>
    </div>
</div>