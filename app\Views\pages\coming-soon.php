<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coming Soon | Something Amazing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'fade-in': 'fadeIn 1s ease-in-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .particle {
            position: absolute;
            background: rgba(15, 23, 42, 0.1);
            border-radius: 50%;
            pointer-events: none;
        }

        .dark .particle {
            background: rgba(226, 232, 240, 0.1);
        }

        .card {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(241, 245, 249, 0.8);
        }

        .dark .card {
            background-color: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(30, 41, 59, 0.8);
        }
    </style>
</head>

<body class="bg-slate-50 dark:bg-slate-900 transition-colors duration-300 min-h-screen flex flex-col">
    <!-- Dark mode toggle -->
    <div class="absolute top-4 right-4 z-50">
        <button id="themeToggle"
            class="p-2 rounded-full bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors shadow-sm">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:block"></i>
        </button>
    </div>

    <!-- Main content -->
    <main class="flex-grow flex flex-col items-center justify-center px-4 py-12 relative overflow-hidden">
        <!-- Particles container -->
        <div id="particles-container" class="absolute inset-0 overflow-hidden"></div>

        <div class="max-w-4xl mx-auto text-center relative z-10 animate-fade-in">
            <div class="mb-8 animate-float">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto text-slate-800 dark:text-slate-200"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
            </div>

            <h1 class="text-5xl md:text-7xl font-bold mb-6 text-slate-800 dark:text-white">
                Coming Soon
            </h1>

            <p class="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-10 max-w-2xl mx-auto leading-relaxed">
                We're crafting something exceptional for you. Stay tuned for the launch!
            </p>

            <div class="card rounded-xl shadow-lg p-6 inline-block">
                <div class="flex items-center justify-center space-x-4">
                    <div class="text-center">
                        <div class="text-3xl md:text-5xl font-bold text-slate-800 dark:text-white" id="days">00</div>
                        <div class="text-slate-500 dark:text-slate-400 text-sm uppercase tracking-wider">Days</div>
                    </div>
                    <div class="text-3xl font-bold text-slate-400">:</div>
                    <div class="text-center">
                        <div class="text-3xl md:text-5xl font-bold text-slate-800 dark:text-white" id="hours">00</div>
                        <div class="text-slate-500 dark:text-slate-400 text-sm uppercase tracking-wider">Hours</div>
                    </div>
                    <div class="text-3xl font-bold text-slate-400">:</div>
                    <div class="text-center">
                        <div class="text-3xl md:text-5xl font-bold text-slate-800 dark:text-white" id="minutes">00</div>
                        <div class="text-slate-500 dark:text-slate-400 text-sm uppercase tracking-wider">Minutes</div>
                    </div>
                    <div class="text-3xl font-bold text-slate-400">:</div>
                    <div class="text-center">
                        <div class="text-3xl md:text-5xl font-bold text-slate-800 dark:text-white" id="seconds">00</div>
                        <div class="text-slate-500 dark:text-slate-400 text-sm uppercase tracking-wider">Seconds</div>
                    </div>
                </div>
                <div class="mt-4 text-sm text-slate-500 dark:text-slate-400">
                    Launching <span class="font-semibold text-slate-800 dark:text-white">Today</span>
                </div>
            </div>

            <div class="mt-12 hidden">
                <div class="relative group">
                    <div
                        class="absolute -inset-1 bg-slate-200 dark:bg-slate-700 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-200">
                    </div>
                    <button
                        class="relative px-8 py-3 bg-white dark:bg-slate-800 text-slate-800 dark:text-white font-medium rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-all transform hover:scale-105 shadow-sm">
                        Notify Me
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="py-6 text-center text-slate-500 dark:text-slate-400 text-sm">
        <p>© 2025 Trade Diary. All rights reserved.</p>
    </footer>

    <script>
        // Dark mode toggle
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;

        // Check for saved theme preference or use system preference
        if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }

        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('theme', html.classList.contains('dark') ? 'dark' : 'light');
        });

        // Countdown timer
        function updateCountdown() {
            // Set the launch date to June 21st at 8:00 PM
            const launchDate = new Date();
            launchDate.setMonth(5); // June (0-based index)
            launchDate.setDate(21);
            launchDate.setHours(18, 0, 0, 0); // 8:00 PM

            const now = new Date();
            const distance = launchDate - now;

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');

            if (distance < 0) {
                clearInterval(countdownInterval);
                document.getElementById('days').textContent = '00';
                document.getElementById('hours').textContent = '00';
                document.getElementById('minutes').textContent = '00';
                document.getElementById('seconds').textContent = '00';
            }
        }

        updateCountdown();
        const countdownInterval = setInterval(updateCountdown, 1000);

        // Create particles
        function createParticles() {
            const container = document.getElementById('particles-container');
            const particleCount = window.innerWidth < 768 ? 20 : 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.top = `${Math.random() * 100}%`;

                // Random animation
                const duration = Math.random() * 20 + 10;
                const delay = Math.random() * 5;
                particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;

                container.appendChild(particle);
            }
        }

        createParticles();

        // Notify button animation
        const notifyBtn = document.querySelector('button');
        notifyBtn.addEventListener('mouseenter', () => {
            notifyBtn.classList.add('animate-pulse-slow');
        });

        notifyBtn.addEventListener('mouseleave', () => {
            notifyBtn.classList.remove('animate-pulse-slow');
        });
    </script>
</body>

</html>