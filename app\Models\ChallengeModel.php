<?php

namespace App\Models;

use CodeIgniter\Model;

class ChallengeModel extends Model
{
    protected $table = 'challenges';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'start_amount',
        'target_amount',
        'timeframe',
        'custom_days',
        'risk_per_trade',
        'user_id',
    ];

}