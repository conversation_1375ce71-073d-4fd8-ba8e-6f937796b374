<?php

namespace App\Models;

use CodeIgniter\Model;

class CommentModel extends Model
{
    protected $table = 'community_comments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'post_id',
        'user_id',
        'parent_id',
        'content',
        'likes_count'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'post_id' => 'required|integer',
        'user_id' => 'required|integer',
        'content' => 'required|max_length[1000]'
    ];

    protected $validationMessages = [
        'post_id' => [
            'required' => 'Post ID is required',
            'integer' => 'Post ID must be an integer'
        ],
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'content' => [
            'required' => 'Comment content is required',
            'max_length' => 'Comment cannot exceed 1000 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = ['updatePostCommentsCount'];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = ['decrementPostCommentsCount'];

    /**
     * Get comments for a post with user information
     */
    public function getPostComments($postId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, u.full_name, u.profile, u.badge');
        $builder->join('users u', 'u.id = c.user_id');
        $builder->where('c.post_id', $postId);
        $builder->where('c.deleted_at', null);
        $builder->orderBy('c.created_at', 'ASC');
        $builder->limit($limit, $offset);
        
        $comments = $builder->get()->getResultArray();
        
        // Organize comments into parent-child structure
        return $this->organizeComments($comments);
    }

    /**
     * Get replies for a comment
     */
    public function getCommentReplies($parentId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, u.full_name, u.profile, u.badge');
        $builder->join('users u', 'u.id = c.user_id');
        $builder->where('c.parent_id', $parentId);
        $builder->where('c.deleted_at', null);
        $builder->orderBy('c.created_at', 'ASC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Organize comments into parent-child structure
     */
    private function organizeComments($comments)
    {
        $organized = [];
        $replies = [];
        
        // Separate parent comments and replies
        foreach ($comments as $comment) {
            if ($comment['parent_id'] === null) {
                $comment['replies'] = [];
                $organized[$comment['id']] = $comment;
            } else {
                $replies[] = $comment;
            }
        }
        
        // Add replies to their parent comments
        foreach ($replies as $reply) {
            if (isset($organized[$reply['parent_id']])) {
                $organized[$reply['parent_id']]['replies'][] = $reply;
            }
        }
        
        return array_values($organized);
    }

    /**
     * Get comment count for a post
     */
    public function getPostCommentCount($postId)
    {
        return $this->where('post_id', $postId)
                   ->where('deleted_at', null)
                   ->countAllResults();
    }

    /**
     * Increment likes count
     */
    public function incrementLikes($commentId)
    {
        return $this->set('likes_count', 'likes_count + 1', false)
                   ->where('id', $commentId)
                   ->update();
    }

    /**
     * Decrement likes count
     */
    public function decrementLikes($commentId)
    {
        return $this->set('likes_count', 'likes_count - 1', false)
                   ->where('id', $commentId)
                   ->where('likes_count >', 0)
                   ->update();
    }

    /**
     * Update post comments count after insert
     */
    protected function updatePostCommentsCount(array $data)
    {
        if (isset($data['data']['post_id'])) {
            $postModel = new PostModel();
            $postModel->incrementComments($data['data']['post_id']);
        }
        return $data;
    }

    /**
     * Decrement post comments count after delete
     */
    protected function decrementPostCommentsCount(array $data)
    {
        if (isset($data['id'])) {
            $comment = $this->withDeleted()->find($data['id']);
            if ($comment) {
                $postModel = new PostModel();
                $postModel->decrementComments($comment['post_id']);
            }
        }
        return $data;
    }

    /**
     * Get user's comments
     */
    public function getUserComments($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, p.title as post_title');
        $builder->join('community_posts p', 'p.id = c.post_id');
        $builder->where('c.user_id', $userId);
        $builder->where('c.deleted_at', null);
        $builder->orderBy('c.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Delete comment and all its replies
     */
    public function deleteCommentWithReplies($commentId)
    {
        // First delete all replies
        $this->where('parent_id', $commentId)->delete();

        // Then delete the parent comment
        return $this->delete($commentId);
    }

    /**
     * Permanently delete all comments for a post
     */
    public function permanentDeletePostComments($postId)
    {
        try {
            // Get all comments for this post (including soft deleted ones)
            $comments = $this->withDeleted()->where('post_id', $postId)->findAll();

            if (!empty($comments)) {
                // Delete associated likes for all comments
                $likeModel = new \App\Models\LikeModel();
                foreach ($comments as $comment) {
                    $likeModel->removeItemLikes('comment', $comment['id']);
                }

                // Permanently delete all comments for this post
                $result = $this->db->table($this->table)->where('post_id', $postId)->delete();

                if ($result) {
                    log_message('info', "All comments for post $postId permanently deleted");
                    return true;
                }
            }

            return true; // No comments to delete is also success
        } catch (\Exception $e) {
            log_message('error', 'Error permanently deleting post comments: ' . $e->getMessage());
            return false;
        }
    }
}
