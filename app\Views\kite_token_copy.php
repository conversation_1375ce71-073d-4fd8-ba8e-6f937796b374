<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Kite Request Token</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
    </style>
</head>

<body class="bg-gray-100 flex items-center justify-center min-h-screen px-4">
    <div class="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md text-center border border-gray-200">
        <h2 class="text-3xl font-semibold text-blue-600 mb-6">🔑 Request Token Generated</h2>

        <div class="text-left mb-4">
            <label for="requestToken" class="block text-sm font-medium text-gray-600 mb-1">Your Request Token</label>
            <div class="relative">
                <input id="requestToken" type="text" value="<?= esc($requestToken) ?>" readonly
                    class="w-full px-4 py-2 text-sm bg-gray-50 border border-gray-300 rounded-lg pr-20 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-400" />
                <button onclick="copyToken()"
                    class="absolute top-1/2 right-2 transform -translate-y-1/2 bg-blue-600 text-white text-xs font-medium px-3 py-1.5 rounded-md hover:bg-blue-700 transition">
                    Copy
                </button>
            </div>
        </div>

        <p id="copiedMessage" class="text-green-600 mt-6 font-medium hidden">
            ✅ Token copied! You can now close this popup.
        </p>
    </div>

    <script>
        function copyToken() {
            const input = document.getElementById('requestToken');
            input.select();
            input.setSelectionRange(0, 99999);
            document.execCommand('copy');

            const msg = document.getElementById('copiedMessage');
            msg.classList.remove('hidden');
        }
    </script>
</body>

</html>