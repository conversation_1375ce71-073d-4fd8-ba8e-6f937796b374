tailwind.config = {
    darkMode: 'class',
    theme: {
        extend: {
            colors: {
                primary: {
                    light: '#3b82f6',
                    dark: '#2563eb'
                },
                secondary: {
                    light: '#10b981',
                    dark: '#059669'
                },
                danger: {
                    light: '#ef4444',
                    dark: '#dc2626'
                },
                dark: {
                    800: '#1e293b',
                    900: '#0f172a'
                },
                glass: {
                    light: 'rgba(255, 255, 255, 0.2)',
                    dark: 'rgba(15, 23, 42, 0.5)'
                }
            },
            fontFamily: {
                sans: ['Inter', 'sans-serif'],
            },
            boxShadow: {
                'neumorphic': '8px 8px 16px rgba(0, 0, 0, 0.1), -8px -8px 16px rgba(255, 255, 255, 0.5)',
                'neumorphic-dark': '8px 8px 16px rgba(0, 0, 0, 0.3), -8px -8px 16px rgba(30, 41, 59, 0.3)',
                'glow': '0 0 15px rgba(59, 130, 246, 0.5)',
                'glow-green': '0 0 15px rgba(16, 185, 129, 0.5)',
                'inner-xl': 'inset 0 4px 6px rgba(0, 0, 0, 0.1)'
            },
            backdropBlur: {
                xs: '2px',
                sm: '4px',
                md: '8px',
                lg: '12px',
                xl: '16px',
            }
        }
    }
}