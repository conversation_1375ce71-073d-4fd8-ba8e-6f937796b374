<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;

use ResponseTrait;

helper('cookie');

class Home extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
    }
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Dashboard';
        $data['active'] = 'dashboard';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'dashboard';
        $data['main_content'] = 'pages/dashboard';

        $data['strategies'] = $db->query("SELECT id, strategy FROM strategies WHERE deleted_at IS NULL   AND (user_id IS NULL OR user_id = ?)", [$userId])->getResultArray();

        $data['summaries'] = $db->query("SELECT id, summary FROM summaries WHERE deleted_at IS NULL")->getResultArray();
        $data['rules'] = $db->query("SELECT id, rule FROM rules WHERE deleted_at IS NULL")->getResultArray();
        // Get both default mistakes (user_id IS NULL) and user's custom mistakes
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $data['mistakes'] = $db->query("
            SELECT id, name as mistake,
                   CASE WHEN user_id IS NULL THEN 'Default' ELSE 'Custom' END as mistake_type
            FROM mistakes
            WHERE is_active = 1
            AND (user_id IS NULL OR user_id = ?)
            ORDER BY user_id IS NULL DESC, name ASC
        ", [$userId])->getResultArray();
        $data['emotions'] = $db->query("SELECT id, emotion FROM emotions WHERE deleted_at IS NULL")->getResultArray();

        return view('includes/template', $data);
    }

    public function Login()
    {
        // return view('pages/coming-soon');
        $encryptedUserId = get_cookie('user_session');
        if ($encryptedUserId) {
            $userId = $this->decrypt_cookie_value($encryptedUserId);

            if ($userId !== false && !empty($userId)) {
                return redirect()->to('/dashboard');
            } else {
                // Cookie is tampered or invalid => delete it
                delete_cookie('user_session');
            }
        }

        return view('pages/login');
    }

    public function LoginX()
    {
        $encryptedUserId = get_cookie('user_session');
        if ($encryptedUserId) {
            $userId = $this->decrypt_cookie_value($encryptedUserId);

            if ($userId !== false && !empty($userId)) {
                return redirect()->to('/dashboard');
            } else {
                // Cookie is tampered or invalid => delete it
                delete_cookie('user_session');
            }
        }

        return view('pages/login');
    }

    public function signup()
    {
        $db = \Config\Database::connect();
        // Check if 'payment_id' cookie exists
        if (!$this->request->getCookie('osrgdthfudtrshf')) {
            return $this->response->setJSON([
                'success' => false,
                'paymentError' => 1,
                'message' => 'Payment not completed. Please complete payment to proceed.'
            ]);
        }

        helper(['form', 'phpmailer']);

        $validation = \Config\Services::validation();

        $validation->setRules([
            'registerName' => [
                'label' => 'Name',
                'rules' => 'required|min_length[3]',
                'errors' => [
                    'required' => 'Please enter your name.',
                    'min_length' => 'Name must be at least 3 characters long.'
                ]
            ],
            'registerEmail' => [
                'label' => 'Email Address',
                'rules' => 'required|valid_email|is_unique[users.email]',
                'errors' => [
                    'required' => 'Please enter your email address.',
                    'valid_email' => 'Please enter a valid email address.',
                    'is_unique' => 'This email is already registered.'
                ]
            ],
            'registerPassword' => [
                'label' => 'Password',
                'rules' => 'required|min_length[8]',
                'errors' => [
                    'required' => 'Please enter a password.',
                    'min_length' => 'Password must be at least 8 characters long.'
                ]
            ],
            'registerConfirmPassword' => [
                'label' => 'Confirm Password',
                'rules' => 'required|matches[registerPassword]',
                'errors' => [
                    'required' => 'Please confirm your password.',
                    'matches' => 'Passwords do not match.'
                ]
            ],
            'acceptTerms' => [
                'label' => 'Terms and Conditions',
                'rules' => 'required',
                'errors' => [
                    'required' => 'You must accept the terms and conditions.'
                ]
            ]
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'paymentError' => 0,
                'message' => implode('<br>', $validation->getErrors())
            ]);
        }

        // Generate verification token
        $token = bin2hex(random_bytes(16)); // 32-character token
        $email = $this->request->getVar('registerEmail');
        $name = $this->request->getVar('registerName');

        $payment_id = $this->request->getCookie('osrgdthfudtrshf');

        $paymentDetails = $db->query("SELECT * FROM transactions WHERE payment_id = '$payment_id' ")->getRowArray();

        $period = $paymentDetails['period'];

        $sub_start = date('Y-m-d H:i:s');

        if ($period == 'monthly') {
            $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
        } elseif ($period == 'annual') {
            $sub_end = date('Y-m-d 23:59:59', strtotime('+1 year'));
        } else {
            // Default/fallback if period is not recognized
            $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
        }

        $referralCode = $this->generateReferralCode($name);

        $refer_by = $this->request->getCookie('refered_by');

        $data = [
            'full_name' => $name,
            'email' => $email,
            'refer_code' => $referralCode,
            'password' => password_hash($this->request->getVar('registerPassword'), PASSWORD_BCRYPT),
            'verification_token' => $token,
            'sub_start' => $sub_start,
            'sub_end' => $sub_end,
            'payment_id' => $payment_id,
            'is_verified' => 0 // optional column to mark user as verified
        ];

        if (!empty($refer_by)) {
            $data['refer_by'] = $refer_by;
        }

        if ($this->usermodel->insert($data)) {
            $userId = $this->usermodel->getInsertID(); // Newly created user

            if (!empty($refer_by) && isset($paymentDetails['amount'])) {
                $this->calculateRef($refer_by, $userId, $paymentDetails['amount']);
            }

            $this->response->deleteCookie('osrgdthfudtrshf');
            // Send verification email
            helper('phpmailer');

            $verificationLink = base_url("verify-email/$token");
            $subject = 'Verify Your Email Address';
            $message = $this->generateVerificationEmailBody($name, $verificationLink);

            if (send_email($email, $subject, $message)) {
                return $this->response->setJSON([
                    'success' => true,
                    'paymentError' => 0,
                    'message' => 'Registration successful! Please check your email to verify your account.'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'paymentError' => 0,
                    'message' => 'Registration succeeded, but failed to send verification email.'
                ]);
            }
        } else {
            return $this->response->setJSON([
                'success' => false,
                'paymentError' => 0,
                'message' => 'Failed to register. Please try again later.'
            ]);
        }
    }

    private function calculateRef($referralCode, $referredUserId, $subscriptionAmount)
    {
        if (empty($referralCode) || empty($referredUserId) || $subscriptionAmount <= 0) {
            return;
        }

        $db = \Config\Database::connect();

        $referrer = $db->table('users')->where('refer_code', $referralCode)->get()->getRow();

        if (!$referrer) {
            return; // Invalid code
        }

        $referrerId = $referrer->id;

        // Check if referrer is influencer
        if (!empty($referrer->is_influencer) && $referrer->is_influencer == 1) {
            $rewardPercent = 20; // Flat 20% for influencers
        } else {
            // Count existing referrals
            $referralCount = $db->table('referrals')
                ->where('referrer_id', $referrerId)
                ->countAllResults();

            $newCount = $referralCount + 1;

            // Determine reward %
            if ($newCount <= 50) {
                $rewardPercent = 10;
            } elseif ($newCount <= 100) {
                $rewardPercent = 15;
            } else {
                $rewardPercent = 20;
            }
        }

        // Calculate reward amount
        $rewardAmount = round(($subscriptionAmount * $rewardPercent) / 100, 2);

        // Save referral record
        $db->table('referrals')->insert([
            'referrer_id' => $referrerId,
            'referred_user_id' => $referredUserId,
            'subscription_amount' => $subscriptionAmount,
            'reward_percent' => $rewardPercent,
            'reward_amount' => $rewardAmount,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Update wallet balance
        $db->table('users')
            ->where('id', $referrerId)
            ->set('wallet_balance', 'wallet_balance + ' . $rewardAmount, false)
            ->update();
    }


    private function generateReferralCode($name)
    {
        // Clean and lowercase the name
        $cleanName = strtolower(preg_replace('/[^a-zA-Z]/', '', $name));

        // Pad the name with "x" if less than 4 characters
        $prefix = str_pad(substr($cleanName, 0, 4), 4, 'x');

        // Generate a random 4-digit number
        $randomNumber = rand(1000, 9999);

        // Combine and return uppercase code
        return strtoupper($prefix . $randomNumber); // e.g., ALEx5732 or JOxX2183
    }


    private function generateVerificationEmailBody($name, $link)
    {
        return '
    <div style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 40px;">
        <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 6px rgba(0,0,0,0.1);">
            <div style="background-color: #6A4ADC; padding: 20px; color: #ffffff; text-align: center;">
                <h2 style="margin: 0;">Verify Your Email</h2>
            </div>
            <div style="padding: 30px;">
                <p style="font-size: 16px;">Hi <strong>' . htmlspecialchars($name) . '</strong>,</p>
                <p style="font-size: 16px; line-height: 1.5;">Thanks for signing up! Please click the button below to verify your email address and activate your account:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . $link . '" style="background-color: #6A4ADC; color: #ffffff; padding: 12px 25px; border-radius: 5px; text-decoration: none; font-weight: bold;">Verify Email</a>
                </div>
                <p style="font-size: 14px; color: #888;">If you did not register, no further action is required. This link will expire in 24 hours.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="font-size: 12px; color: #999; text-align: center;">&copy; ' . date('Y') . ' Trade Diary. All rights reserved.</p>
            </div>
        </div>
    </div>';
    }

    public function verifyEmail($token)
    {
        $user = $this->usermodel->where('verification_token', $token)->first();

        if ($user) {
            // Mark user as verified
            $this->usermodel->update($user['id'], [
                'is_verified' => 1,
                'verification_token' => null
            ]);

            // Encrypt user ID using custom function
            $encryptedId = $this->encrypt_cookie_value($user['id']);

            if ($encryptedId !== false) {
                // Set encrypted ID in cookie (7 days expiry)
                set_cookie('user_session', $encryptedId, 60 * 60 * 24 * 30); // in seconds
            }

            // Redirect to dashboard
            return redirect()->to('/dashboard');
        } else {
            return redirect()->to('/');
        }
    }

    public function signin()
    {
        helper(['form']);

        $validation = \Config\Services::validation();

        $validation->setRules([
            'loginEmail' => 'required|valid_email',
            'loginPassword' => 'required'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => implode('<br>', $validation->getErrors())
            ]);
        }

        $email = $this->request->getVar('loginEmail');
        $password = $this->request->getVar('loginPassword');

        $user = $this->usermodel->where('email', $email)->first();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid email address.'
            ]);
        }

        // Check if email is verified
        if (!$user['is_verified']) {
            return $this->response->setJSON([
                'success' => false,
                'code' => 'EMAIL_NOT_VERIFIED',
                'message' => 'Your email is not verified. Please check your inbox.'
            ]);
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You have entered an incorrect password.'
            ]);
        }

        // Create session or cookie here if needed
        $encryptedId = $this->encrypt_cookie_value($user['id']);

        if ($encryptedId !== false) {
            // Set encrypted ID in cookie (7 days expiry)
            set_cookie('user_session', $encryptedId, 60 * 60 * 24 * 30); // in seconds
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Logged in successfully.',
            'redirectTo' => base_url('dashboard')
        ]);
    }

    public function logout()
    {
        setcookie('user_session', '', time() - 3600, "/");

        return redirect()->to('Login');
    }

    function saveTrade()
    {
        // date_default_timezone_set('Asia/Kolkata');

        $uploadPath = WRITEPATH . 'uploads/screenshots/';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        helper(['form']);
        $db = \Config\Database::connect();
        $validation = \Config\Services::validation();

        $rules = [
            'market_type' => 'required',
            'symbol' => 'required',
            'datetime' => 'required',
            'entry_price' => 'required|decimal',
            'entry_quantity' => 'required|decimal',
            'entry_amount' => 'required|decimal',
            'exit_price' => 'required|decimal',
            'pnlAmount' => 'required|decimal',
            'pnlPercent' => 'required|decimal',
            'trade_type' => 'required|integer',
            // 'stop_loss' => 'required|decimal',
            // 'target' => 'required|decimal',
            'strategy' => 'required',
            'outcome' => 'required',
            // 'rationale' => 'required',
            'confidence' => 'required',
            'satisfaction' => 'required',
            'emotion' => 'required',
            // 'lesson' => 'required'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $validation->getErrors()
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $mkt = $this->request->getPost('market_type');
        if ($mkt == 1) {
            $currency = "₹";
        } else {
            $currency = "$";
        }

        $tradeData = [
            'user_id' => $userId,
            'symbol' => strtoupper($this->request->getPost('symbol')),
            'market_type' => $this->request->getPost('market_type'),
            'currency' => $currency,
            'datetime' => $this->request->getPost('datetime'),
            'entry_price' => $this->request->getPost('entry_price'),
            'entry_quantity' => $this->request->getPost('entry_quantity'),
            'entry_amount' => $this->request->getPost('entry_amount'),
            'exit_price' => $this->request->getPost('exit_price'),
            'pnl_amount' => $this->request->getPost('pnlAmount'),
            'pnl_percent' => $this->request->getPost('pnlPercent'),
            'trade_type' => $this->request->getPost('trade_type'),
            'stop_loss' => $this->request->getPost('stop_loss'),
            'target' => $this->request->getPost('target'),
            'strategy' => $this->request->getPost('strategy'),
            'outcome' => $this->request->getPost('outcome'),
            'rationale' => $this->request->getPost('rationale'),
            'confidence' => $this->request->getPost('confidence'),
            'satisfaction' => $this->request->getPost('satisfaction'),
            'emotion' => $this->request->getPost('emotion'),
            'lesson' => $this->request->getPost('lesson')
        ];

        $entryPrice = $tradeData['entry_price'] ?? 0;
        $exitPrice = $tradeData['exit_price'] ?? 0;
        $stopLoss = $tradeData['stop_loss'] ?? null;

        // Check if stop loss is valid
        if ($stopLoss === null || $stopLoss == 0 || $stopLoss == '') {
            // Fallback if SL is not provided
            $tradeData['rr_ratio'] = 0;
        } else {
            $denominator = abs($entryPrice - $stopLoss);
            $tradeData['rr_ratio'] = ($denominator > 0)
                ? abs($exitPrice - $entryPrice) / $denominator
                : 0;
        }


        $editId = $this->request->getPost('editId');
        if ($editId) {
            $this->trademodel->update($editId, $tradeData);
            $tradeId = $editId;

            // ✅ Clear old rules & mistakes
            $db->table('trade_rules')->where('trade_id', $tradeId)->delete();
            $db->table('trade_mistakes')->where('trade_id', $tradeId)->delete();

            // (Optional) clear old screenshots here if needed
        } else {
            $tradeId = $this->trademodel->insert($tradeData);
        }

        // ✅ Save selected rules
        $rules = $this->request->getPost('selected_rules');
        if (is_array($rules)) {
            foreach ($rules as $ruleId) {
                $db->table('trade_rules')->insert([
                    'trade_id' => $tradeId,
                    'rule_id' => $ruleId
                ]);
            }
        }

        // ✅ Save selected mistakes
        $mistakes = $this->request->getPost('mistakes');
        if (is_array($mistakes)) {
            foreach ($mistakes as $mistakeId) {
                $db->table('trade_mistakes')->insert([
                    'trade_id' => $tradeId,
                    'mistake_id' => $mistakeId
                ]);
            }
        }

        // ✅ Save uploaded screenshots
        $files = $this->request->getFileMultiple('screenshots');
        foreach ($files as $file) {
            if ($file->isValid() && !$file->hasMoved()) {
                $newName = $file->getRandomName();
                $file->move(WRITEPATH . 'uploads/screenshots', $newName);

                $db->table('trade_screenshots')->insert([
                    'trade_id' => $tradeId,
                    'file_path' => 'uploads/screenshots/' . $newName
                ]);
            }
        }

        $today = date('Y-m-d');

        // ✅ Today’s PnL (excluding deleted rows)
        $todayPnl = $db->table('trades')
            ->selectSum('pnl_amount')
            ->where('user_id', $userId)
            ->where('DATE(datetime)', $today)
            ->where('deleted_at', null)
            ->get()->getRow()->pnl_amount ?? 0;

        // ✅ Yesterday’s PnL or fallback session
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        $yesterdayPnl = $db->table('trades')
            ->selectSum('pnl_amount')
            ->where('user_id', $userId)
            ->where('DATE(datetime)', $yesterday)
            ->where('deleted_at', null)
            ->get()->getRow()->pnl_amount;

        if ($yesterdayPnl === null || $yesterdayPnl == 0) {
            // ⛔ No data for yesterday — find previous session (excluding deleted)
            $lastSession = $db->table('trades')
                ->select('DATE(datetime) as session_date')
                ->where('user_id', $userId)
                ->where('DATE(datetime) <', $today)
                ->where('deleted_at', null)
                ->orderBy('DATE(datetime)', 'DESC')
                ->limit(1)
                ->get()->getRow();

            if ($lastSession) {
                $lastDate = $lastSession->session_date;

                $yesterdayPnl = $db->table('trades')
                    ->selectSum('pnl_amount')
                    ->where('user_id', $userId)
                    ->where('DATE(datetime)', $lastDate)
                    ->where('deleted_at', null)
                    ->get()->getRow()->pnl_amount ?? 0;
            } else {
                $yesterdayPnl = 0;
            }
        }


        // ✅ Comparison Message
        $diff = $todayPnl - $yesterdayPnl;
        $diffFormatted = number_format(abs($diff), 2);

        if ($diff > 0) {
            $improvementMessage = "You saw a increase of {$diffFormatted} in your pnl compared to the last session!";
            // $improvementMessage = "You improved your profit by {$diffFormatted} compared to the last session!";
        } elseif ($diff < 0) {
            $improvementMessage = "You saw a decrease of {$diffFormatted} in your pnl compared to the last session!";
            // $improvementMessage = "Your profit decreased by {$diffFormatted} compared to the last session.";
        } else {
            $improvementMessage = "Your profit is the same as the last session.";
        }

        // ✅ Random motivational/response message
        $responseMsg = $db->table('response_messages')->orderBy('RAND()')->limit(1)->get()->getRow();
        $randomMessage = $responseMsg ? $responseMsg->message : null;

        // ✅ PnL color
        $todayColor = $todayPnl >= 0 ? 'green' : 'red';
        $yesterdayColor = $yesterdayPnl >= 0 ? 'green' : 'red';

        function formatPnl($amount)
        {
            return ($amount >= 0 ? '+' : '-') . number_format(abs($amount), 2);
        }

        $icon = 'fas fa-minus'; // default neutral
        if ($todayPnl > $yesterdayPnl) {
            $icon = 'fas fa-arrow-up';
        } elseif ($todayPnl < $yesterdayPnl) {
            $icon = 'fas fa-arrow-down';
        }

        return $this->response->setJSON([
            'status' => true,
            'today_pnl' => formatPnl($todayPnl),
            'today_color' => $todayColor,
            'yesterday_pnl' => formatPnl($yesterdayPnl),
            'yesterday_color' => $yesterdayColor,
            'message' => $improvementMessage,
            'response_message' => $randomMessage,
            'message_icon' => $icon
        ]);
    }
    public function ajaxTrades()
    {
        $page = (int) ($this->request->getGet('page') ?? 1);
        $perPage = (int) ($this->request->getGet('perPage') ?? 10);
        $offset = ($page - 1) * $perPage;

        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Filters
        $startDate = $this->request->getGet('startDate');
        $endDate = $this->request->getGet('endDate');
        $selectedStrategies = $this->request->getGet('selectedStrategies'); // e.g., "1,3,5"
        $direction = $this->request->getGet('direction'); // 0 = all, 1 = long, 2 = short
        $market_type = $this->request->getGet('market_type'); // 0 = all, 1 = long, 2 = short

        // Sorting
        $sortBy = $this->request->getGet('sortBy') ?? '';
        $sortMap = [
            'pnl-desc' => ['pnl_amount', 'DESC'],
            'pnl-asc' => ['pnl_amount', 'ASC'],
            'rr-desc' => ['rr_ratio', 'DESC'],
            'rr-asc' => ['rr_ratio', 'ASC'],
            'date-desc' => ['datetime', 'DESC'],
            'date-asc' => ['datetime', 'ASC'],
        ];

        // === Data query ===
        $builder = $db->table('trades');
        $builder->select('trades.id, trades.entry_quantity, trades.rr_ratio, trades.datetime, trades.symbol, trades.trade_type, trades.entry_price, trades.stop_loss, trades.target, trades.exit_price, pnl_amount, pnl_percent, strategies.strategy, summaries.summary, summaries.color');
        $builder->join('strategies', 'strategies.id = trades.strategy', 'left');
        $builder->join('summaries', 'summaries.id = trades.outcome', 'left');
        $builder->where('trades.user_id', $userId);
        $builder->where('trades.deleted_at IS NULL');

        // Date filter
        if ($startDate && !$endDate) {
            $builder->where('DATE(trades.datetime) >=', $startDate);
            $builder->where('DATE(trades.datetime) <=', date('Y-m-d'));
        } elseif (!$startDate && $endDate) {
            $builder->where('DATE(trades.datetime) <=', $endDate);
        } elseif ($startDate && $endDate) {
            $builder->where('DATE(trades.datetime) >=', $startDate);
            $builder->where('DATE(trades.datetime) <=', $endDate);
        }

        // Strategy filter
        if (!empty($selectedStrategies)) {
            $strategyIds = explode(',', $selectedStrategies);
            $builder->whereIn('trades.strategy', $strategyIds);
        }

        // Direction filter
        if (!empty($direction) && $direction != '0') {
            $builder->where('trades.trade_type', $direction);
        }

        // Direction filter
        if (!empty($market_type) && $market_type != '0') {
            $builder->where('trades.market_type', $market_type);
        }

        // Sorting
        if (isset($sortMap[$sortBy])) {
            $builder->orderBy($sortMap[$sortBy][0], $sortMap[$sortBy][1]);
        } else {
            $builder->orderBy('trades.id', 'DESC');
        }

        $builder->limit($perPage, $offset);
        $trades = $builder->get()->getResult();

        // === Count query ===
        $countBuilder = $db->table('trades');
        $countBuilder->join('strategies', 'strategies.id = trades.strategy', 'left');
        $countBuilder->join('summaries', 'summaries.id = trades.outcome', 'left');
        $countBuilder->where('trades.user_id', $userId);
        $countBuilder->where('trades.deleted_at IS NULL');

        if ($startDate && !$endDate) {
            $countBuilder->where('DATE(trades.datetime) >=', $startDate);
            $countBuilder->where('DATE(trades.datetime) <=', date('Y-m-d'));
        } elseif (!$startDate && $endDate) {
            $countBuilder->where('DATE(trades.datetime) <=', $endDate);
        } elseif ($startDate && $endDate) {
            $countBuilder->where('DATE(trades.datetime) >=', $startDate);
            $countBuilder->where('DATE(trades.datetime) <=', $endDate);
        }

        if (!empty($selectedStrategies)) {
            $strategyIds = explode(',', $selectedStrategies);
            $countBuilder->whereIn('trades.strategy', $strategyIds);
        }

        if (!empty($direction) && $direction != '0') {
            $countBuilder->where('trades.trade_type', $direction);
        }

        if (!empty($market_type) && $market_type != '0') {
            $countBuilder->where('trades.market_type', $market_type);
        }

        $total = $countBuilder->countAllResults();

        return $this->response->setJSON([
            'data' => $trades,
            'currentPage' => $page,
            'perPage' => $perPage,
            'total' => $total,
            'totalPages' => ceil($total / $perPage)
        ]);
    }

    function myTrades()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['strategies'] = $db->query("SELECT id, strategy FROM strategies WHERE deleted_at IS NULL   AND (user_id IS NULL OR user_id = ?)", [$userId])->getResultArray();
        $data['summaries'] = $db->query("SELECT id, summary FROM summaries WHERE deleted_at IS NULL")->getResultArray();
        $data['rules'] = $db->query("SELECT id, rule FROM rules WHERE deleted_at IS NULL")->getResultArray();
        // Get both default mistakes (user_id IS NULL) and user's custom mistakes
        $data['mistakes'] = $db->query("
            SELECT id, name as mistake,
                   CASE WHEN user_id IS NULL THEN 'Default' ELSE 'Custom' END as mistake_type
            FROM mistakes
            WHERE is_active = 1
            AND (user_id IS NULL OR user_id = ?)
            ORDER BY user_id IS NULL DESC, name ASC
        ", [$userId])->getResultArray();
        $data['emotions'] = $db->query("SELECT id, emotion FROM emotions WHERE deleted_at IS NULL")->getResultArray();

        $data['title'] = 'My Trades';
        $data['active'] = 'trades';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'trades';
        $data['main_content'] = 'pages/trades';

        return view('includes/template', $data);
    }

    public function deleteTrade()
    {
        try {
            $id = $this->request->getVar('id');

            // Check if ID is provided and is a valid number
            if (!$id || !is_numeric($id)) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Invalid or missing trade ID.'
                ])->setStatusCode(400); // Bad Request
            }

            // Attempt to delete
            if ($this->trademodel->delete($id)) {
                return $this->response->setJSON([
                    'status' => true,
                    'message' => 'Trade deleted successfully.'
                ])->setStatusCode(200); // OK
            } else {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Trade not found or could not be deleted.'
                ])->setStatusCode(404); // Not Found
            }
        } catch (\Exception $e) {
            // Handle any unexpected exceptions
            return $this->response->setJSON([
                'status' => false,
                'message' => 'An error occurred while deleting the trade.',
                'error' => $e->getMessage() // Optional: remove in production
            ])->setStatusCode(500); // Internal Server Error
        }
    }

    public function viewTrade()
    {
        $id = $this->request->getVar('id');

        $db = \Config\Database::connect();

        $builder = $db->table('trades');
        $builder->select('trades.id, trades.datetime, trades.entry_quantity, trades.lesson, trades.confidence, trades.satisfaction, emotions.emotion, trades.rationale, trades.symbol, trades.trade_type, trades.entry_price, trades.stop_loss, trades.target, trades.exit_price, pnl_amount, pnl_percent, strategies.strategy, summaries.summary');
        $builder->join('strategies', 'strategies.id = trades.strategy', 'left');
        $builder->join('summaries', 'summaries.id = trades.outcome', 'left');
        $builder->join('emotions', 'emotions.id = trades.emotion', 'left');
        $builder->where('trades.id', $id);
        $builder->where('trades.deleted_at IS NULL');
        $trades = $builder->get()->getRow();

        $mBuilder = $db->table('trade_mistakes');
        $mBuilder->select('mistakes.name as mistake, trade_mistakes.id');
        $mBuilder->join('mistakes', 'mistakes.id = trade_mistakes.mistake_id', 'left');
        $mBuilder->where('trade_mistakes.trade_id', $id);
        $mistakes = $mBuilder->get()->getResult();

        $iBuilder = $db->table('trade_screenshots');
        $iBuilder->select('trade_screenshots.file_path');
        $iBuilder->where('trade_screenshots.trade_id', $id);
        $screenshots = $iBuilder->get()->getResult();

        echo json_encode(['trade' => $trades, 'mistakes' => $mistakes, 'screenshots' => $screenshots]);
    }

    public function getEditTradeData()
    {
        $db = \Config\Database::connect();
        $id = $this->request->getVar('id');

        $trade = $this->trademodel->find($id);

        $selectedRules = $db->query("SELECT rule_id FROM trade_rules WHERE trade_id = $id")->getResultArray();

        $mistakes = $db->query("SELECT mistake_id FROM trade_mistakes WHERE trade_id = $id")->getResultArray();

        $screenshots = $db->query("SELECT file_path, id FROM trade_screenshots WHERE trade_id = $id")->getResultArray();

        echo json_encode(['trade' => $trade, 'rules' => $selectedRules, 'mistakes' => $mistakes, 'screenshots' => $screenshots]);
    }

    public function getDashboardMetrics()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $rangeFilter = $this->request->getPost('rangeFilter') ?? '1';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        switch ($rangeFilter) {
            case '3':
                $startOfThis = date('Y-m-01', strtotime('-2 months'));
                $startOfLast = date('Y-m-01', strtotime('-5 months'));
                $endOfThis = date('Y-m-t');
                $endOfLast = date('Y-m-t', strtotime('-3 months'));
                $label = 'vs last 3 months';
                break;
            case '12':
                $startOfThis = date('Y-m-01', strtotime('-11 months'));
                $startOfLast = date('Y-m-01', strtotime('-23 months'));
                $endOfThis = date('Y-m-t');
                $endOfLast = date('Y-m-t', strtotime('-12 months'));
                $label = 'vs last 12 months';
                break;
            case '':
                $startOfThis = null;
                $startOfLast = null;
                $endOfThis = null;
                $endOfLast = null;
                $label = 'lifetime stats';
                break;
            default:
                $startOfThis = date('Y-m-01');
                $endOfThis = date('Y-m-t');
                $startOfLast = date('Y-m-01', strtotime('-1 month'));
                $endOfLast = date('Y-m-t', strtotime('-1 month'));
                $label = 'vs last month';
                break;
        }

        // Build WHERE clause based on date range
        $dateFilterSql = function ($start, $end) {
            if ($start && $end)
                return " AND datetime BETWEEN '$start' AND '$end'";
            return '';
        };

        $commonWhere = " AND deleted_at IS NULL AND market_type = ?";

        // Highest PnL
        $sqlHighestPnl = "SELECT MAX(pnl_amount) AS pnl FROM trades 
                  WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfThis, $endOfThis);
        $highestPnlThis = $db->query($sqlHighestPnl, [$userId, $marketTypeFilter])->getRow()->pnl ?? 0;

        $sqlHighestPnlLast = "SELECT MAX(pnl_amount) AS pnl FROM trades 
                  WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfLast, $endOfLast);
        $highestPnlLast = $db->query($sqlHighestPnlLast, [$userId, $marketTypeFilter])->getRow()->pnl ?? 0;

        $pnlChange = ($highestPnlLast != 0)
            ? round((($highestPnlThis - $highestPnlLast) / $highestPnlLast) * 100, 1)
            : 0;

        // Win Rate
        $sqlWinRate = "SELECT 
    SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) AS wins,
    COUNT(*) AS total 
    FROM trades 
    WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfThis, $endOfThis);
        $winThis = $db->query($sqlWinRate, [$userId, $marketTypeFilter])->getRow();

        $sqlWinRateLast = "SELECT 
    SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) AS wins,
    COUNT(*) AS total 
    FROM trades 
    WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfLast, $endOfLast);
        $winLast = $db->query($sqlWinRateLast, [$userId, $marketTypeFilter])->getRow();

        $winRateThis = ($winThis->total > 0) ? round(($winThis->wins / $winThis->total) * 100, 1) : 0;
        $winRateLast = ($winLast->total > 0) ? round(($winLast->wins / $winLast->total) * 100, 1) : 0;
        $winRateChange = round($winRateThis - $winRateLast, 1);

        // Avg. Risk/Reward
        $sqlRR = "SELECT AVG((exit_price - entry_price) / (entry_price - stop_loss)) AS rr
          FROM trades
          WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfThis, $endOfThis);
        $avgRRThis = $db->query($sqlRR, [$userId, $marketTypeFilter])->getRow()->rr ?? 0;

        $sqlRRLast = "SELECT AVG((exit_price - entry_price) / (entry_price - stop_loss)) AS rr
          FROM trades
          WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfLast, $endOfLast);
        $avgRRLast = $db->query($sqlRRLast, [$userId, $marketTypeFilter])->getRow()->rr ?? 0;

        $riskRewardChange = round($avgRRThis - $avgRRLast, 2);

        // Trades Count
        $sqlTradeCountThis = "SELECT COUNT(*) AS count FROM trades 
                  WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfThis, $endOfThis);
        $tradesThisMonth = $db->query($sqlTradeCountThis, [$userId, $marketTypeFilter])->getRow()->count ?? 0;

        $sqlTradeCountLast = "SELECT COUNT(*) AS count FROM trades 
                  WHERE user_id = ?" . $commonWhere . $dateFilterSql($startOfLast, $endOfLast);
        $tradesLastMonth = $db->query($sqlTradeCountLast, [$userId, $marketTypeFilter])->getRow()->count ?? 0;

        $tradesChange = $tradesThisMonth - $tradesLastMonth;

        return $this->response->setJSON([
            'highestPnl' => [
                'value' => round($highestPnlThis, 2),
                'change' => $pnlChange
            ],
            'winRate' => [
                'value' => $winRateThis,
                'change' => $winRateChange
            ],
            'riskReward' => [
                'value' => round($avgRRThis, 2),
                'change' => $riskRewardChange
            ],
            'tradesThisMonth' => [
                'value' => $tradesThisMonth,
                'change' => $tradesChange
            ],
            'message' => $label
        ]);
    }


    public function getEquityChartData()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $filter = $this->request->getPost('pnlChartFilter') ?? 'D';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        $sql = "";
        $params = [$userId, $marketTypeFilter];

        if ($filter === 'D') {
            $sql = "SELECT DATE(datetime) AS label, SUM(pnl_amount) AS total_pnl
                FROM trades
                WHERE user_id = ? AND market_type = ? AND deleted_at IS NULL
                GROUP BY DATE(datetime)
                ORDER BY DATE(datetime) DESC
                LIMIT 5";
        } elseif ($filter === 'W') {
            $sql = "SELECT 
                    MIN(DATE(datetime)) AS week_start,
                    MAX(DATE(datetime)) AS week_end,
                    SUM(pnl_amount) AS total_pnl
                FROM trades
                WHERE user_id = ? AND market_type = ? AND deleted_at IS NULL
                GROUP BY YEARWEEK(datetime, 1)
                ORDER BY week_start DESC
                LIMIT 5";
        } elseif ($filter === 'M') {
            $sql = "SELECT DATE_FORMAT(datetime, '%m-%Y') AS label, 
                       SUM(pnl_amount) AS total_pnl
                FROM trades
                WHERE user_id = ? AND market_type = ? AND deleted_at IS NULL
                GROUP BY DATE_FORMAT(datetime, '%m-%Y')
                ORDER BY DATE_FORMAT(datetime, '%m-%Y') DESC
                LIMIT 5";
        }

        $results = $db->query($sql, $params)->getResultArray();
        $results = array_reverse($results); // Oldest first

        $labels = [];
        $data = [];
        $cumulativePnl = 0;

        foreach ($results as $row) {
            $cumulativePnl += round($row['total_pnl'], 2);
            if ($filter === 'W') {
                // Week label as "27 May - 02 Jun"
                $start = date('d M', strtotime($row['week_start']));
                $end = date('d M', strtotime($row['week_end']));
                $labels[] = "{$start} - {$end}";
            } else {
                $labels[] = $row['label'];
            }
            $data[] = $cumulativePnl;
        }

        return $this->response->setJSON([
            'labels' => $labels,
            'data' => $data
        ]);
    }


    public function fetchTopTrades()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $rangeFilter = $this->request->getPost('rangeFilter') ?? '1';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        switch ($rangeFilter) {
            case '3':
                $startDate = date('Y-m-d', strtotime('-3 months'));
                break;
            case '12':
                $startDate = date('Y-m-d', strtotime('-12 months'));
                break;
            case '':
                $startDate = null;
                break;
            default:
                $startDate = date('Y-m-d', strtotime('-1 month'));
                break;
        }

        $builder = $this->trademodel
            ->where('user_id', $userId)
            ->where('market_type', $marketTypeFilter)
            ->where('deleted_at IS NULL');

        if ($startDate) {
            $builder = $builder->where('datetime >=', $startDate);
        }

        $trades = $builder->orderBy('pnl_amount', 'DESC')
            ->limit(3)
            ->findAll();

        echo json_encode($trades);
    }



    public function getWinLossChartData()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $rangeFilter = $this->request->getPost('rangeFilter') ?? '1';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        switch ($rangeFilter) {
            case '3':
                $startDate = date('Y-m-d', strtotime('-3 months'));
                $label = 'vs last 3 months';
                break;
            case '12':
                $startDate = date('Y-m-d', strtotime('-12 months'));
                $label = 'vs last 12 months';
                break;
            case '':
                $startDate = null;
                $label = 'lifetime stats';
                break;
            default:
                $startDate = date('Y-m-d', strtotime('-1 month'));
                $label = 'vs last month';
                break;
        }

        $builder = $db->table('trades');
        $builder->select("
        SUM(CASE WHEN pnl_amount >= 0 THEN 1 ELSE 0 END) AS wins,
        SUM(CASE WHEN pnl_amount < 0 THEN 1 ELSE 0 END) AS losses
    ");
        $builder->where('user_id', $userId);
        $builder->where('market_type', $marketTypeFilter);
        $builder->where('deleted_at IS NULL');

        if ($startDate) {
            $builder->where('datetime >=', $startDate);
        }

        $result = $builder->get()->getRowArray();

        $wins = (int) $result['wins'];
        $losses = (int) $result['losses'];
        $total = $wins + $losses;

        $winPercent = $total > 0 ? round(($wins / $total) * 100, 2) : 0;
        $lossPercent = $total > 0 ? round(($losses / $total) * 100, 2) : 0;

        return $this->response->setJSON([
            'labels' => ['Winning Trades', 'Losing Trades'],
            'data' => [
                'percentages' => [$winPercent, $lossPercent],
                'counts' => [$wins, $losses]
            ],
            'message' => $label
        ]);
    }



    public function getMonthlyConfidenceScore()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Read filters from POST
        $rangeFilter = $this->request->getPost('rangeFilter') ?? '1';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        switch ($rangeFilter) {
            case '3':
                $startDate = date('Y-m-d', strtotime('-3 months'));
                break;
            case '12':
                $startDate = date('Y-m-d', strtotime('-12 months'));
                break;
            case '':
                $startDate = null;
                break;
            default:
                $startDate = date('Y-m-d', strtotime('-1 month'));
                break;
        }

        // Emotion mapping
        $emotionScores = [
            1 => 5,
            2 => 3,
            3 => 2,
            4 => 1,
            5 => 3,
            6 => 2,
            7 => 1,
            8 => 1,
            9 => 4
        ];

        $builder = $db->table('trades');
        $builder->select('confidence, satisfaction, emotion, pnl_amount');
        $builder->where('user_id', $userId);
        $builder->where('market_type', $marketTypeFilter);
        $builder->where('deleted_at IS NULL');

        if ($startDate) {
            $builder->where('datetime >=', $startDate);
        }

        $trades = $builder->get()->getResultArray();

        if (empty($trades)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'No trades found for the selected period.'
            ]);
        }

        $totalConfidence = 0;
        $totalSatisfaction = 0;
        $totalEmotionScore = 0;
        $positivePnls = 0;
        $count = count($trades);

        foreach ($trades as $trade) {
            $emotionId = (int) $trade['emotion'];
            $emotionScore = $emotionScores[$emotionId] ?? 3;

            $totalConfidence += (int) $trade['confidence'];
            $totalSatisfaction += (int) $trade['satisfaction'];
            $totalEmotionScore += $emotionScore;

            if ($trade['pnl_amount'] > 0) {
                $positivePnls++;
            }
        }

        // Averages
        $avgConfidence = $totalConfidence / $count;
        $avgSatisfaction = $totalSatisfaction / $count;
        $avgEmotionScore = $totalEmotionScore / $count;
        $winRatio = $positivePnls / $count;

        // Weighted score
        $confidenceScore = (
            ($avgConfidence / 10 * 40) +
            ($avgSatisfaction / 5 * 20) +
            ($avgEmotionScore / 5 * 20) +
            ($winRatio * 20)
        );

        // Message
        if ($confidenceScore >= 80) {
            $confidenceText = 'Very High Confidence – You are trading with excellent discipline and emotional stability.';
        } elseif ($confidenceScore >= 60) {
            $confidenceText = 'High Confidence – You are trading well with consistent focus.';
        } elseif ($confidenceScore >= 40) {
            $confidenceText = 'Moderate Confidence – There’s room to improve your consistency and mindset.';
        } else {
            $confidenceText = 'Low Confidence – Focus on emotional control and trade review.';
        }

        return $this->response->setJSON([
            'status' => true,
            'score' => round($confidenceScore, 2),
            'message' => $confidenceText,
            'breakdown' => [
                'avg_confidence' => round($avgConfidence, 1),
                'avg_satisfaction' => round($avgSatisfaction, 1),
                'avg_emotion_score' => round($avgEmotionScore, 1),
                'win_ratio' => round($winRatio * 100) . '%'
            ]
        ]);
    }



    public function getStrategyPerformance()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Fetch strategy-wise performance by joining strategies table
        $query = $db->query("
        SELECT 
            s.strategy AS strategy_name,
            COUNT(*) AS total_trades,
            SUM(CASE WHEN t.pnl_amount > 0 THEN 1 ELSE 0 END) AS winning_trades,
            AVG(t.pnl_amount) AS avg_pnl
        FROM trades t
        JOIN strategies s ON t.strategy = s.id
        WHERE t.user_id = ? 
            AND t.deleted_at IS NULL
            AND t.datetime >= ?
        GROUP BY s.strategy
        ORDER BY s.strategy ASC
    ", [$userId, date('Y-m-d', strtotime('-1 month'))]);

        $data = $query->getResultArray();

        $labels = [];
        $winRates = [];
        $avgPnls = [];

        foreach ($data as $row) {
            $labels[] = $row['strategy_name'];
            $winRate = $row['total_trades'] > 0
                ? round(($row['winning_trades'] / $row['total_trades']) * 100, 1)
                : 0;
            $winRates[] = $winRate;
            $avgPnls[] = round($row['avg_pnl'], 2);
        }

        return $this->response->setJSON([
            'labels' => $labels,
            'winRates' => $winRates,
            'avgPnls' => $avgPnls
        ]);
    }

    public function getDailyPnl()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        $query = $db->query("
        SELECT 
            DATE(t.datetime) AS trade_date,
            SUM(t.pnl_amount) AS daily_pnl
        FROM trades t
        WHERE t.user_id = ?
            AND t.market_type = ?
            AND t.deleted_at IS NULL
        GROUP BY DATE(t.datetime)
        ORDER BY trade_date DESC
        LIMIT 7
    ", [$userId, $marketTypeFilter]);

        $data = array_reverse($query->getResultArray()); // reverse to ASC order

        $labels = [];
        $pnls = [];

        foreach ($data as $row) {
            $labels[] = date('j M', strtotime($row['trade_date'])); // e.g. 5 Jun, 14 May
            $pnls[] = round($row['daily_pnl'], 2);
        }

        return $this->response->setJSON([
            'labels' => $labels,
            'pnls' => $pnls
        ]);
    }



    public function getStrategyPnls()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $rangeFilter = $this->request->getPost('rangeFilter') ?? '1';
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        switch ($rangeFilter) {
            case '3':
                $startDate = date('Y-m-d', strtotime('-3 months'));
                $label = 'vs last 3 months';
                break;
            case '12':
                $startDate = date('Y-m-d', strtotime('-12 months'));
                $label = 'vs last 12 months';
                break;
            case '':
                $startDate = null;
                $label = 'lifetime stats';
                break;
            default:
                $startDate = date('Y-m-d', strtotime('-1 month'));
                $label = 'vs last month';
                break;
        }

        $sql = "
        SELECT st.strategy, 
               SUM(t.pnl_amount) AS total_pnl
        FROM trades t
        JOIN strategies st ON t.strategy = st.id
        WHERE t.user_id = ?
          AND t.market_type = ?
          AND t.deleted_at IS NULL
    ";

        $params = [$userId, $marketTypeFilter];

        if ($startDate) {
            $sql .= " AND t.datetime >= ? ";
            $params[] = $startDate;
        }

        $sql .= "
        GROUP BY st.strategy
        ORDER BY total_pnl DESC
    ";

        $query = $db->query($sql, $params);
        $results = $query->getResultArray();

        $labels = [];
        $data = [];

        foreach ($results as $row) {
            $labels[] = $row['strategy'];
            $data[] = round($row['total_pnl'], 2);
        }

        return $this->response->setJSON([
            'labels' => $labels,
            'data' => $data,
            'message' => $label
        ]);
    }


    public function getMistakesPieData()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $rangeFilter = $this->request->getPost('rangeFilter'); // 1, 3, or 12
        $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

        $rangeMonths = is_numeric($rangeFilter) ? (int) $rangeFilter : 1;

        // Calculate start date
        $startDate = date('Y-m-d', strtotime("-{$rangeMonths} months"));

        // Generate human-readable label
        $rangeText = "Last {$rangeMonths} month" . ($rangeMonths > 1 ? "s" : "");

        $builder = $db->table('trade_mistakes tm');
        $builder->select('m.mistake, COUNT(*) AS mistake_count, SUM(t.pnl_amount) AS total_loss');
        $builder->join('mistakes m', 'm.id = tm.mistake_id');
        $builder->join('trades t', 't.id = tm.trade_id');
        $builder->where('t.user_id', $userId);
        $builder->where('t.market_type', $marketTypeFilter);
        $builder->where('t.deleted_at IS NULL');
        $builder->where('t.pnl_amount <', 0); // Only losses
        $builder->where('t.datetime >=', $startDate); // Apply date range filter
        $builder->groupBy('m.mistake');
        $builder->orderBy('mistake_count', 'DESC');
        $builder->limit(4);

        $results = $builder->get()->getResultArray();

        $maxCount = 0;
        $totalLoss = 0;
        foreach ($results as $row) {
            $maxCount = max($maxCount, $row['mistake_count']);
            $totalLoss += abs($row['total_loss']);
        }

        $labels = [];
        $amounts = [];
        $percentages = [];
        $counts = [];

        foreach ($results as $row) {
            $labels[] = $row['mistake'];
            $amount = abs($row['total_loss']);
            $amounts[] = $amount;
            $percentages[] = $totalLoss > 0 ? round(($amount / $totalLoss) * 100) : 0;
            $counts[] = $row['mistake_count'];
        }

        return $this->response->setJSON([
            'labels' => $labels,
            'amounts' => $amounts,
            'percentages' => $percentages,
            'counts' => $counts,
            'maxCount' => $maxCount,
            'rangeText' => $rangeText
        ]);
    }




    // Reports section
    public function Reports()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Reports';
        $data['active'] = 'reports';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'report';
        $data['main_content'] = 'pages/report';

        return view('includes/template', $data);
    }

    public function getTradePerformance()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $range = $this->request->getPost('rangeFilter') ?? 1;
        $range = in_array($range, [1, 3, 12]) ? intval($range) : 1;

        $fromDate = date('Y-m-d', strtotime("-{$range} months"));

        $builder = $db->table('trades');
        $builder->select('pnl_amount');
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $fromDate);
        $trades = $builder->get()->getResultArray();

        $win = $loss = $breakeven = 0;
        $totalWin = $totalLoss = 0;

        foreach ($trades as $t) {
            $pnl = floatval($t['pnl_amount']);
            if ($pnl > 0) {
                $win++;
                $totalWin += $pnl;
            } elseif ($pnl < 0) {
                $loss++;
                $totalLoss += abs($pnl);
            } else {
                $breakeven++;
            }
        }

        $total = $win + $loss;
        $avgWin = $win > 0 ? round($totalWin / $win, 2) : 0;
        $avgLoss = $loss > 0 ? round($totalLoss / $loss, 2) : 0;
        $winRate = $total > 0 ? round(($win / $total) * 100, 2) : 0;

        $expectancy = ($avgWin * ($winRate / 100)) - ($avgLoss * ((100 - $winRate) / 100));
        $expectancy = round($expectancy, 2);

        return $this->response->setJSON([
            'win' => $win,
            'loss' => $loss,
            'breakeven' => $breakeven,
            'avgWin' => $avgWin,
            'avgLoss' => $avgLoss,
            'winRate' => $winRate,
            'expectancy' => $expectancy
        ]);
    }

    public function getDailyPerformance()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $range = $this->request->getPost('rangeFilter') ?? 1;
        $range = in_array($range, [1, 3, 12]) ? intval($range) : 1;

        $fromDate = date('Y-m-d', strtotime("-{$range} months"));

        // Get daily total PnL
        $builder = $db->table('trades');
        $builder->select("DATE(datetime) as day, SUM(pnl_amount) as total_pnl");
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $fromDate);
        $builder->groupBy('DATE(datetime)');
        $dailyPnls = $builder->get()->getResultArray();

        $winDays = $lossDays = $breakevenDays = 0;
        $totalWin = $totalLoss = 0;
        $bestDay = $worstDay = 0;

        foreach ($dailyPnls as $row) {
            $pnl = floatval($row['total_pnl']);

            // Initialize best/worst on first loop
            if ($bestDay === 0 && $worstDay === 0) {
                $bestDay = $worstDay = $pnl;
            }

            if ($pnl > 0) {
                $winDays++;
                $totalWin += $pnl;
            } elseif ($pnl < 0) {
                $lossDays++;
                $totalLoss += abs($pnl);
            } else {
                $breakevenDays++;
            }

            if ($pnl > $bestDay)
                $bestDay = $pnl;
            if ($pnl < $worstDay)
                $worstDay = $pnl;
        }

        $avgWinDay = $winDays > 0 ? round($totalWin / $winDays, 2) : 0;
        $avgLossDay = $lossDays > 0 ? round($totalLoss / $lossDays, 2) : 0;

        return $this->response->setJSON([
            'winDays' => $winDays,
            'lossDays' => $lossDays,
            'breakevenDays' => $breakevenDays,
            'bestDay' => round($bestDay, 2),
            'worstDay' => round($worstDay, 2),
            'avgWinDay' => $avgWinDay,
            'avgLossDay' => $avgLossDay
        ]);
    }

    public function getTimeMetrics()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter') ?? 1; // in months
        $startDate = date('Y-m-d', strtotime("-$range months"));

        // Fetch trades aggregated by day
        $builder = $db->table('trades');
        $builder->select("DATE(datetime) as trade_day, SUM(pnl_amount) as daily_pnl");
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $startDate);
        $builder->groupBy('DATE(datetime)');
        $builder->orderBy('trade_day', 'ASC');
        $days = $builder->get()->getResultArray();

        $tradingDays = count($days);
        $consecWin = 0;
        $consecLoss = 0;
        $currentWinStreak = 0;
        $currentLossStreak = 0;

        foreach ($days as $d) {
            if ($d['daily_pnl'] > 0) {
                $currentWinStreak++;
                $currentLossStreak = 0;
            } elseif ($d['daily_pnl'] < 0) {
                $currentLossStreak++;
                $currentWinStreak = 0;
            } else {
                $currentWinStreak = 0;
                $currentLossStreak = 0;
            }

            $consecWin = max($consecWin, $currentWinStreak);
            $consecLoss = max($consecLoss, $currentLossStreak);
        }

        // Profit/loss by weekday
        $weekdayStats = [];
        foreach ($days as $d) {
            $dayName = date('l', strtotime($d['trade_day']));
            if (!isset($weekdayStats[$dayName])) {
                $weekdayStats[$dayName] = 0;
            }
            $weekdayStats[$dayName] += $d['daily_pnl'];
        }

        arsort($weekdayStats);
        $mostProfitable = array_key_first($weekdayStats);
        $leastProfitable = array_key_last($weekdayStats);

        return $this->response->setJSON([
            'tradingDays' => $tradingDays,
            'consecWinDays' => $consecWin,
            'consecLossDays' => $consecLoss,
            'mostProfitableDay' => $mostProfitable,
            'leastProfitableDay' => $leastProfitable
        ]);
    }
    public function getRiskManagementMetrics()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $builder = $db->table('trades');
        $builder->select('entry_price, exit_price, stop_loss, target, pnl_amount');
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $startDate);
        $builder->where('datetime <=', $endDate);
        $builder->orderBy('datetime', 'asc');
        $query = $builder->get();

        $trades = $query->getResult();

        $plannedRs = [];
        $realizedRs = [];
        $losses = [];

        // For max drawdown calculation
        $cumulativePnL = 0;
        $peak = 0;
        $maxDrawdown = 0;

        foreach ($trades as $trade) {
            if ($trade->stop_loss != null && $trade->stop_loss != 0 && $trade->entry_price != $trade->stop_loss) {
                $riskPerTrade = $trade->entry_price - $trade->stop_loss;

                // Planned R-Multiple
                if ($trade->target != null) {
                    $planned = ($trade->target - $trade->entry_price) / $riskPerTrade;
                    if (is_finite($planned)) {
                        $plannedRs[] = $planned;
                    }
                }

                // Realized R-Multiple
                if ($trade->exit_price != null) {
                    $realized = ($trade->exit_price - $trade->entry_price) / $riskPerTrade;
                    if (is_finite($realized)) {
                        $realizedRs[] = $realized;
                    }
                }
            }

            // Avg Loss
            if ($trade->pnl_amount < 0) {
                $losses[] = $trade->pnl_amount;
            }

            // Max Drawdown
            $cumulativePnL += $trade->pnl_amount;
            if ($cumulativePnL > $peak) {
                $peak = $cumulativePnL;
            }
            $drawdown = $peak - $cumulativePnL;
            if ($drawdown > $maxDrawdown) {
                $maxDrawdown = $drawdown;
            }
        }

        $plannedR = count($plannedRs) > 0 ? round(array_sum($plannedRs) / count($plannedRs), 2) : 0;
        $realizedR = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;
        $avgLoss = count($losses) > 0 ? round(array_sum($losses) / count($losses), 2) : 0;
        $expectancy = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;

        return $this->response->setJSON([
            'planned_r_multiple' => $plannedR,
            'realized_r_multiple' => $realizedR,
            'avg_loss' => $avgLoss,
            'max_drawdown' => round($maxDrawdown, 2),
            'expectancy' => $expectancy
        ]);
    }

    public function getEmotionalStateMetrics()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        // Subquery: Count emotions for user in date range
        $subQuery = $db->table('trades t')
            ->select('t.emotion, COUNT(*) as total')
            ->where('t.user_id', $userId)
            ->where('t.deleted_at IS NULL')
            ->where('t.datetime >=', $startDate)
            ->where('t.datetime <=', $endDate)
            ->groupBy('t.emotion');

        $emotionCounts = $subQuery->get()->getResult();

        // Create a map for counts
        $countsMap = [];
        $totalEmotions = 0;
        foreach ($emotionCounts as $row) {
            $countsMap[$row->emotion] = $row->total;
            $totalEmotions += $row->total;
        }

        // Get all emotions from master table
        $builder = $db->table('emotions');
        $builder->select('id, emotion');
        $builder->where('deleted_at IS NULL');
        $query = $builder->get();
        $emotions = $query->getResult();

        $emotionStats = [];
        foreach ($emotions as $e) {
            $count = isset($countsMap[$e->id]) ? $countsMap[$e->id] : 0;
            $percent = $totalEmotions > 0 ? round(($count / $totalEmotions) * 100) : 0;

            $emotionStats[] = [
                'emotion' => trim($e->emotion),
                'percentage' => $percent
            ];
        }

        // Order by percentage descending
        usort($emotionStats, function ($a, $b) {
            return $b['percentage'] <=> $a['percentage'];
        });

        return $this->response->setJSON($emotionStats);
    }

    public function getTargetOutcomeMetrics()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 months

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $builder = $db->table('trades');
        $builder->select('entry_price, exit_price, stop_loss, target');
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $startDate);
        $builder->where('datetime <=', $endDate);
        $query = $builder->get();

        $trades = $query->getResult();

        $achieved = 0;
        $missed = 0;
        $stoppedEarly = 0;

        $rAchieved = [];
        $rMissed = [];

        foreach ($trades as $trade) {
            if (
                $trade->target == null || $trade->stop_loss == null ||
                $trade->target == 0 || $trade->stop_loss == 0 ||
                $trade->exit_price == null
            ) {
                continue;
            }

            $risk = abs($trade->entry_price - $trade->stop_loss);
            if ($risk == 0)
                continue;

            $rMultiple = ($trade->exit_price - $trade->entry_price) / $risk;

            // Categorization logic
            if ($trade->exit_price >= $trade->target) {
                $achieved++;
                $rAchieved[] = $rMultiple;
            } elseif ($trade->exit_price < $trade->target && $trade->exit_price > $trade->entry_price) {
                $missed++;
                $rMissed[] = $rMultiple;
            } elseif ($trade->exit_price <= $trade->entry_price) {
                $stoppedEarly++;
                $rMissed[] = $rMultiple;
            }
        }

        $total = $achieved + $missed + $stoppedEarly;
        $percentAchieved = $total > 0 ? round(($achieved / $total) * 100) : 0;
        $percentMissed = $total > 0 ? round(($missed / $total) * 100) : 0;
        $percentStopped = $total > 0 ? round(($stoppedEarly / $total) * 100) : 0;

        $avgRAchieved = count($rAchieved) > 0 ? round(array_sum($rAchieved) / count($rAchieved), 2) : 0;
        $avgRMissed = count($rMissed) > 0 ? round(array_sum($rMissed) / count($rMissed), 2) : 0;

        return $this->response->setJSON([
            'target_achieved' => $percentAchieved,
            'target_missed' => $percentMissed,
            'stopped_before_target' => $percentStopped,
            'avg_r_on_achieved' => $avgRAchieved,
            'avg_r_on_missed' => $avgRMissed
        ]);
    }

    public function getSetupEffectiveness()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter');

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $builder = $db->table('strategies s');
        $builder->select('s.strategy, COUNT(t.id) as total_trades, 
                      SUM(CASE WHEN t.pnl_amount > 0 THEN 1 ELSE 0 END) as wins');
        $builder->join('trades t', 's.id = t.strategy AND t.deleted_at IS NULL AND t.user_id = ' . $db->escape($userId) . ' AND t.datetime >= "' . $startDate . '" AND t.datetime <= "' . $endDate . '"', 'left');
        $builder->where('s.deleted_at IS NULL');
        $builder->groupBy('s.id');
        $builder->orderBy('wins DESC');
        $builder->limit(5);
        $query = $builder->get();

        $result = [];

        foreach ($query->getResult() as $row) {
            $winRate = ($row->total_trades > 0) ? round(($row->wins / $row->total_trades) * 100, 1) : 0;
            $result[] = [
                'strategy' => $row->strategy,
                'win_rate' => $winRate
            ];
        }

        return $this->response->setJSON($result);
    }

    public function getSymbolFrequency()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $builder = $db->table('trades');
        $builder->select('symbol, COUNT(*) as total_trades, SUM(pnl_amount) as total_pnl,
        SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins');
        $builder->where('user_id', $userId);
        $builder->where('deleted_at IS NULL');
        $builder->where('datetime >=', $startDate);
        $builder->where('datetime <=', $endDate);
        $builder->groupBy('symbol');
        $query = $builder->get();

        $symbols = $query->getResult();
        $totalTrades = array_sum(array_column($symbols, 'total_trades'));

        $mostTraded = null;
        $mostProfitable = null;
        $leastProfitable = null;
        $highestWinRate = null;
        $lowestWinRate = null;

        foreach ($symbols as $symbol) {
            $symbol->win_rate = $symbol->total_trades > 0 ? round(($symbol->wins / $symbol->total_trades) * 100, 1) : 0;
        }

        if (count($symbols) > 0) {
            usort($symbols, fn($a, $b) => $b->total_trades <=> $a->total_trades);
            $mostTraded = [
                'symbol' => $symbols[0]->symbol,
                'percent' => round(($symbols[0]->total_trades / $totalTrades) * 100)
            ];

            usort($symbols, fn($a, $b) => $b->total_pnl <=> $a->total_pnl);
            $mostProfitable = [
                'symbol' => $symbols[0]->symbol,
                'amount' => round($symbols[0]->total_pnl, 2)
            ];

            usort($symbols, fn($a, $b) => $a->total_pnl <=> $b->total_pnl);
            $leastProfitable = [
                'symbol' => $symbols[0]->symbol,
                'amount' => round($symbols[0]->total_pnl, 2)
            ];

            usort($symbols, fn($a, $b) => $b->win_rate <=> $a->win_rate);
            $highestWinRate = [
                'symbol' => $symbols[0]->symbol,
                'rate' => $symbols[0]->win_rate
            ];

            usort($symbols, fn($a, $b) => $a->win_rate <=> $b->win_rate);
            $lowestWinRate = [
                'symbol' => $symbols[0]->symbol,
                'rate' => $symbols[0]->win_rate
            ];
        }

        return $this->response->setJSON([
            'most_traded' => $mostTraded,
            'most_profitable' => $mostProfitable,
            'least_profitable' => $leastProfitable,
            'highest_win_rate' => $highestWinRate,
            'lowest_win_rate' => $lowestWinRate
        ]);
    }

    public function getAvgRRByEmotion()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter');

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $builder = $db->table('emotions');
        $builder->select('emotions.id, emotions.emotion, COUNT(trades.id) as total, 
        AVG((trades.exit_price - trades.entry_price) / ABS(trades.entry_price - trades.stop_loss)) as avg_rr');
        $builder->join('trades', 'trades.emotion = emotions.id AND trades.user_id = ' . $db->escape($userId) . ' AND trades.datetime BETWEEN ' . $db->escape($startDate) . ' AND ' . $db->escape($endDate) . ' AND trades.deleted_at IS NULL', 'left');
        $builder->where('emotions.deleted_at IS NULL');
        $builder->groupBy('emotions.id');
        $builder->orderBy('avg_rr', 'DESC'); // Optional ordering
        $query = $builder->get();

        $results = $query->getResultArray();

        foreach ($results as &$row) {
            $row['avg_rr'] = $row['avg_rr'] !== null ? round($row['avg_rr'], 2) : 0;
        }

        return $this->response->setJSON($results);
    }

    public function Calender()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Calender';
        $data['active'] = 'calender';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'calender';
        $data['main_content'] = 'pages/calender';

        return view('includes/template', $data);
    }

    public function getTradesByMonth()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $year = $this->request->getGet('year');
        $month = $this->request->getGet('month');
        $marketTypeFilter = $this->request->getGet('marketTypeFilter');

        // Fetch current month trades (daily)
        $builder = $db->table('trades');
        $builder->select("DAY(datetime) as day, 
              SUM(pnl_amount) as total_pnl,
              COUNT(*) as total_trades,
              SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as winning_trades,
              GROUP_CONCAT(CONCAT(symbol, '|', trade_type, '|', entry_quantity, '|', entry_price, '|', exit_price, '|', pnl_amount, '|', (pnl_amount > 0)) SEPARATOR '||') as trade_details");
        $builder->where('user_id', $userId);
        $builder->where('YEAR(datetime)', $year);
        $builder->where('MONTH(datetime)', $month);
        $builder->where('deleted_at', null);
        if ($marketTypeFilter != '0') {
            $builder->where('market_type', $marketTypeFilter);
        }
        $builder->groupBy('DAY(datetime)');
        $query = $builder->get();

        $result = [];

        $totalPnl = 0;
        $totalTrades = 0;
        $totalWins = 0;
        $totalRR = 0;
        $rrCount = 0;

        foreach ($query->getResult() as $row) {
            $trades = [];

            if (!empty($row->trade_details)) {
                $tradeLines = explode('||', $row->trade_details);
                foreach ($tradeLines as $line) {
                    [$symbol, $type, $qty, $entry, $exit, $pnl, $win] = explode('|', $line);
                    $trades[] = [
                        'symbol' => $symbol,
                        'side' => ucfirst($type),
                        'size' => $qty,
                        'entry' => $entry,
                        'exit' => $exit,
                        'pnl' => $pnl,
                        'win' => $win === '1' || $win === 'true',
                    ];

                    // Estimate RR from entry/exit for demo purposes
                    if ($entry > 0) {
                        $rrRatio = abs(($exit - $entry) / ($entry * 0.01)); // 1% stop assumption
                        $totalRR += $rrRatio;
                        $rrCount++;
                    }

                    $totalPnl += $pnl;
                    $totalTrades++;
                    if ($pnl > 0) {
                        $totalWins++;
                    }
                }
            }

            $result[] = [
                'day' => (int) $row->day,
                'pnl' => round($row->total_pnl, 2),
                'avgRiskReward' => '1:' . number_format(rand(15, 40) / 10, 1), // Simulated
                'totalTrades' => (int) $row->total_trades,
                'winRate' => $row->total_trades > 0 ? round(($row->winning_trades / $row->total_trades) * 100) . '%' : '0%',
                'trades' => $trades
            ];
        }

        $avgRR = $rrCount > 0 ? round($totalRR / $rrCount, 1) : 0;
        $overallWinRate = $totalTrades > 0 ? ($totalWins / $totalTrades) * 100 : 0;

        // Fetch previous month for comparison
        $prevMonth = $month - 1;
        $prevYear = $year;
        if ($prevMonth == 0) {
            $prevMonth = 12;
            $prevYear = $year - 1;
        }

        $prevQuery = $db->table('trades')
            ->select("SUM(pnl_amount) as pnl, 
              COUNT(*) as trades, 
              SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins")
            ->where('user_id', $userId)
            ->where('deleted_at', null)
            ->where('YEAR(datetime)', $prevYear)
            ->where('MONTH(datetime)', $prevMonth);
        if ($marketTypeFilter != '0') {
            $prevQuery->where('market_type', $marketTypeFilter);
        }
        $prevQuery = $prevQuery->get()->getRow();

        $prevPnl = (float) ($prevQuery->pnl ?? 0);
        $prevTrades = (int) ($prevQuery->trades ?? 0);
        $prevWins = (int) ($prevQuery->wins ?? 0);
        $prevWinRate = $prevTrades > 0 ? ($prevWins / $prevTrades) * 100 : 0;
        $prevAvgRR = $this->calculateAvgRR($db, $userId, $prevYear, $prevMonth, $marketTypeFilter);

        // Change calculation
        function percentChange($current, $previous)
        {
            if ($previous == 0) {
                return $current == 0 ? 0 : 100;
            }
            return round((($current - $previous) / abs($previous)) * 100, 1);
        }

        // Card data
        $cards = [
            'totalPnl' => round($totalPnl, 2),
            'totalPnlChange' => percentChange($totalPnl, $prevPnl),

            'winRate' => round($overallWinRate, 1) . '%',
            'winRateChange' => percentChange($overallWinRate, $prevWinRate),

            'totalTrades' => $totalTrades,
            'totalTradesChange' => percentChange($totalTrades, $prevTrades),

            'avgRiskReward' => '1:' . $avgRR,
            'avgRiskRewardChange' => percentChange($avgRR, $prevAvgRR)
        ];

        return $this->response->setJSON([
            'dailyData' => $result,
            'cards' => $cards
        ]);
    }


    private function calculateAvgRR($db, $userId, $year, $month, $marketType = null)
    {
        $builder = $db->table('trades');
        $builder->select("entry_price, stop_loss, exit_price");
        $builder->where('user_id', $userId);
        $builder->where('deleted_at', null);
        $builder->where('YEAR(datetime)', $year);
        $builder->where('MONTH(datetime)', $month);

        if ($marketType !== null && $marketType !== '0') {
            $builder->where('market_type', $marketType);
        }

        $trades = $builder->get()->getResult();

        $totalRR = 0;
        $count = 0;

        foreach ($trades as $trade) {
            if ($trade->entry_price > 0 && $trade->stop_loss > 0) {
                $rr = abs(($trade->exit_price - $trade->entry_price) / ($trade->entry_price - $trade->stop_loss));
                $totalRR += $rr;
                $count++;
            }
        }

        return $count > 0 ? round($totalRR / $count, 1) : 0;
    }


    public function Tools()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Tools';
        $data['active'] = 'tools';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'tools';
        $data['main_content'] = 'pages/tools';

        return view('includes/template', $data);
    }

    public function Tutorials()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Tutorials';
        $data['active'] = 'tutorial';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'tutorial';
        $data['main_content'] = 'pages/tutorial';

        return view('includes/template', $data);
    }

    public function Calculator()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Calculator';
        $data['active'] = 'tools';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'calculator';
        $data['main_content'] = 'pages/calculator';

        return view('includes/template', $data);
    }

    public function ReturnsCalculator()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Returns Calculator';
        $data['active'] = 'tools';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'r_calculator';
        $data['main_content'] = 'pages/r_calculator';

        return view('includes/template', $data);
    }
}
