<?php
// Simple script to check database tables
require_once 'vendor/autoload.php';

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'diary';

try {
    // Create connection
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database: $database\n\n";
    
    // Show all tables
    echo "=== ALL TABLES ===\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n=== MISTAKE-RELATED TABLES ===\n";
    
    // Check for mistakes table
    if (in_array('mistakes', $tables)) {
        echo "\n✅ 'mistakes' table exists\n";
        $stmt = $pdo->query("DESCRIBE mistakes");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Columns:\n";
        foreach ($columns as $col) {
            echo "  - {$col['Field']} ({$col['Type']}) {$col['Null']} {$col['Key']}\n";
        }
        
        // Show sample data
        $stmt = $pdo->query("SELECT * FROM mistakes LIMIT 5");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\nSample data:\n";
        foreach ($data as $row) {
            echo "  ID: {$row['id']}, Mistake: " . ($row['mistake'] ?? 'N/A') . "\n";
        }
    } else {
        echo "❌ 'mistakes' table does NOT exist\n";
    }
    
    // Check for trade_mistakes table
    if (in_array('trade_mistakes', $tables)) {
        echo "\n✅ 'trade_mistakes' table exists\n";
        $stmt = $pdo->query("DESCRIBE trade_mistakes");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Columns:\n";
        foreach ($columns as $col) {
            echo "  - {$col['Field']} ({$col['Type']}) {$col['Null']} {$col['Key']}\n";
        }
        
        // Show sample data
        $stmt = $pdo->query("SELECT * FROM trade_mistakes LIMIT 5");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\nSample data:\n";
        foreach ($data as $row) {
            echo "  ID: {$row['id']}, Trade ID: {$row['trade_id']}, Mistake ID: {$row['mistake_id']}\n";
        }
    } else {
        echo "❌ 'trade_mistakes' table does NOT exist\n";
    }
    
    // Check for trades table
    if (in_array('trades', $tables)) {
        echo "\n✅ 'trades' table exists\n";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM trades");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total trades: {$count['count']}\n";
    } else {
        echo "❌ 'trades' table does NOT exist\n";
    }
    
    // Check for rules table
    if (in_array('rules', $tables)) {
        echo "\n✅ 'rules' table exists\n";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM rules");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total rules: {$count['count']}\n";
    } else {
        echo "❌ 'rules' table does NOT exist\n";
    }
    
    // Check for trade_rules table
    if (in_array('trade_rules', $tables)) {
        echo "\n✅ 'trade_rules' table exists\n";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM trade_rules");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total trade-rule associations: {$count['count']}\n";
    } else {
        echo "❌ 'trade_rules' table does NOT exist\n";
    }
    
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
    echo "Please check your database configuration.\n";
}
?>
