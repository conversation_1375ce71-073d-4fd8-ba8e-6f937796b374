<style>
    .gradient-bg {
        background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
    }

    .popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .popup-overlay.active {
        opacity: 1;
        pointer-events: all;
    }

    .popup-content {
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .popup-overlay.active .popup-content {
        transform: scale(1);
    }

    .input-error {
        border-color: #ef4444;
    }

    .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .toggle-checkbox:checked {
        right: 0;
        border-color: #68D391;
    }

    .toggle-checkbox:checked+.toggle-label {
        background-color: #68D391;
    }

    .animate-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }
</style>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="flex-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-100">User Profile</h2>
                <button id="edit-profile-btn"
                    class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition">
                    <i class="fas fa-edit"></i>
                    <span class="hidden sm:inline">Edit Profile</span>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-4">Personal Information</h3>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Full Name</p>
                            <p class="font-medium text-gray-900 dark:text-gray-100" id="display-name">
                                <?= $userDetails['full_name'] ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Email</p>
                            <p class="font-medium text-gray-900 dark:text-gray-100" id="display-email">
                                <?= $userDetails['email'] ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Member Since</p>
                            <p class="font-medium text-gray-900 dark:text-gray-100">
                                <?= date('M d, Y', strtotime($userDetails['created_at'])) ?>
                            </p>
                        </div>
                        <?php
                        $expired = strtotime($userDetails['sub_end']) < strtotime(date('Y-m-d'));
                        ?>

                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Subscription valid upto</p>
                            <?php if ($expired): ?>
                                <p class="font-medium text-red-600">Expired</p>
                            <?php else: ?>
                                <p class="font-medium text-gray-900 dark:text-gray-100">
                                    <?= date('M d, Y', strtotime($userDetails['sub_end'])) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-4">Account Security</h3>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Password</p>
                            <p class="font-medium text-gray-900 dark:text-gray-100">••••••••</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Two-Factor Authentication</p>
                            <p class="font-medium text-green-600 dark:text-green-400">Enabled</p>
                        </div>
                        <!-- <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Last Login</p>
                            <p class="font-medium text-gray-900 dark:text-gray-100">Today, 10:45 AM</p>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-custom-dark p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Broker Connections</h2>
            <button id="connect-broker-btn"
                class="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition">
                <i class="fas fa-plug"></i>
                <span class="hidden sm:inline">Connect with Broker</span>
            </button>

        </div>

        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-4">Connected Broker</h3>

            <div id="connected-broker-section">
                Empty state message (only show this if no broker is connected)
                <div
                    class="p-4 bg-white dark:bg-gray-600 rounded-lg text-center text-gray-500 dark:text-gray-300 italic">
                    No broker connected yet. Connect a broker to start syncing trades.
                </div>
            </div>
        </div>

    </div>
</div>

<div id="edit-profile-popup"
    class="popup-overlay fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
    <div class="popup-content w-full max-w-md mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">Edit Profile</h3>
                    <button id="close-popup"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <form id="edit-profile-form">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full
                            Name</label>
                        <input type="text" id="name" name="name"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-gray-100"
                            value="<?= $userDetails['full_name'] ?>">
                        <div id="name-error" class="error-message text-red-600 text-sm mt-1 hidden">Please enter your
                            name</div>
                    </div>
                    <div class="mb-4 hidden">
                        <label for="email"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                        <input type="email" id="email" name="email"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-gray-100"
                            value="<?= $userDetails['email'] ?>">
                        <div id="email-error" class="error-message text-red-600 text-sm mt-1 hidden">Please enter a
                            valid email</div>
                    </div>
                    <div class="mb-4">
                        <label for="password"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                        <input type="password" id="password" name="password"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Leave blank to keep current">
                        <div id="password-error" class="error-message text-red-600 text-sm mt-1 hidden">Password must be
                            at least 8 characters</div>
                    </div>
                    <div class="mb-6">
                        <label for="confirm-password"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm
                            Password</label>
                        <input type="password" id="confirm-password" name="confirm-password"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Confirm new password">
                        <div id="confirm-password-error" class="error-message text-red-600 text-sm mt-1 hidden">
                            Passwords don't match</div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancel-edit"
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition">Cancel</button>
                        <button type="submit"
                            class="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-400 transition flex items-center">
                            <span>Save Changes</span>
                            <i id="save-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Broker Selection Popup -->
<div id="broker-selection-popup" class="mpopup popup-overlay">
    <div class="popup-content w-full max-w-2xl">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">Select Your Broker</h3>
                    <button onclick="closeModal('#broker-selection-popup')" id="close-broker-selection"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <!-- Broker Cards -->
                    <div onclick="openBModal('#dhan-popup')"
                        class="broker-card cursor-pointer p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                        data-broker="dhan">
                        <div class="flex items-center space-x-3">
                            <div
                                class="w-12 h-12 rounded-full bg-blue-100 dark:bg-gray-600 flex items-center justify-center">
                                <img src="<?= base_url() ?>assets/images/broker/dhan-icon.svg" alt="Dhan">
                            </div>
                            <div>
                                <h4 class="font-semibold dark:text-gray-100">Dhan</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Trade like a Super Trader!</p>
                            </div>
                        </div>
                    </div>

                    <!-- angel -->
                    <div onclick="openBModal('#angel-popup')"
                        class="broker-card cursor-pointer p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                        data-broker="angel_one">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 rounded-full bg-white flex items-center justify-center">
                                <img src="<?= base_url() ?>assets/images/broker/angel-icon.svg?x=ffdf"
                                    alt="Angel broking">
                            </div>
                            <div>
                                <h4 class="font-semibold dark:text-gray-100">Angel One</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">India's largest retail stockbroker
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- KITE -->

                    <?php if ($userDetails['id'] == 1) { ?>
                        <div onclick="openBModal('#kite-popup')"
                            class="broker-card cursor-pointer p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition"
                            data-broker="kite">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full bg-white flex items-center justify-center p-1">
                                    <img style="mix-blend-mode: multiply;"
                                        src="<?= base_url() ?>assets/images/broker/kite-icon.png?x=ffdf" alt="Zerodha">
                                </div>
                                <div>
                                    <h4 class="font-semibold dark:text-gray-100">Zerodha</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Connect with Zerodha Kite
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>

                <!-- Coming Soon Message -->
                <div
                    class="mt-6 p-4 text-center text-gray-600 dark:text-gray-300 text-sm border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                    More brokers coming soon... Stay tuned!
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Broker API Connection Popup -->
<div id="dhan-popup" class="popup-overlay">
    <div class="popup-content w-full max-w-md">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100" id="broker-api-title">Connect to
                        Dhan</h3>
                    <button onclick="closeModal('#dhan-popup')" id="close-broker-api"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <form id="dhan-form">
                    <div class="mb-4">
                        <label for="api-key"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Client ID</label>
                        <input type="text" name="clientID"
                            class="dhanReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your Client ID">
                        <span class="error-message hidden">Client ID is required</span>
                        <a href="https://youtu.be/Sl80qVsrFlQ?si=am7UJ50xbyv3Tz7w" target="_blank"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 inline-block">
                            <i class="fas fa-info-circle mr-1"></i> How to get Client ID?
                        </a>
                    </div>
                    <div class="mb-4">
                        <label for="api-secret"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Access Token</label>
                        <input type="password" name="accessToken"
                            class="dhanReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your Access Token">
                        <span class="error-message hidden">Access Token is required</span>
                        <a href="https://youtu.be/-2yf2-W46bs?si=F-LWm_8AQMtphYqw" target="_blank"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 inline-block">
                            <i class="fas fa-info-circle mr-1"></i> Where to find Access Token?
                        </a>
                    </div>
                    <div class="mb-6">
                        <label for="redirect-url"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Redirect URL</label>
                        <div class="flex">
                            <input type="text" id="dhan-url" name="redirect-url"
                                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                                value="https://tradediary.in/dhan/callback" readonly>
                            <button onclick="copyUrl('dhan-url')" type="button"
                                class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 transition">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Copy this URL and paste in your
                            broker's developer settings</p>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button"
                            onclick="saveBrokerDetails('dhan','<?= base_url('saveBrokerDetails') ?>','.dhanReq','#dhan-form')"
                            class="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-600 transition flex items-center">
                            <span>Connect</span>
                            <i id="connect-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Angel One API Connection Popup -->
<div id="angel-popup" class="popup-overlay">
    <div class="popup-content w-full max-w-md max-h-[90vh] flex flex-col">
        <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden flex flex-col max-h-full">
            <!-- Simple Header -->
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">Connect to Angel One</h3>
                    <button onclick="closeModal('#angel-popup')"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Scrollable Content Area -->
            <div class="flex-1 overflow-y-auto px-6 py-4" id="angel-form-container">
                <form id="angel-form" class="space-y-4">
                    <!-- API Key Field -->
                    <div>
                        <label for="angel-api-key"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Key</label>
                        <input type="text" id="angel-api-key" name="api_key"
                            class="angelReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your Angel One API Key" required>
                        <p class="error-message text-xs text-red-500 mt-1 hidden">API Key is required</p>
                    </div>

                    <!-- Client Code Field -->
                    <div>
                        <label for="angel-client-code"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Client Code</label>
                        <input type="text" id="angel-client-code" name="client_code" autocomplete="new-password"
                            class="angelReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your Angel One Client Code" required readonly
                            onfocus="this.removeAttribute('readonly');">
                        <p class="error-message text-xs text-red-500 mt-1 hidden">Client Code is required</p>
                    </div>

                    <!-- M-PIN Field -->
                    <div>
                        <label for="angel-mpin"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">M-PIN</label>
                        <input type="password" id="angel-mpin" name="mpin" autocomplete="new-password"
                            class="angelReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your 4-digit M-PIN" maxlength="4" required readonly
                            onfocus="this.removeAttribute('readonly');">
                        <p class="error-message text-xs text-red-500 mt-1 hidden">M-PIN is required</p>
                    </div>


                    <!-- Redirect URL Field -->
                    <div>
                        <label for="angel-redirect-url"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Redirect URL</label>
                        <div class="flex">
                            <input type="text" id="angel-redirect-url" name="redirect_url"
                                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                                value="https://tradediary.in/" readonly>
                            <button type="button" onclick="copyUrl('angel-redirect-url')"
                                class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 transition">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- TOTP Secret Key Field -->
                    <div>
                        <label for="angel-totp-key"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">TOTP Secret
                            Key</label>
                        <input type="text" id="angel-totp-key" name="totp_key"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your TOTP secret key from Angel One">
                    </div>

                    <!-- TOTP Code Field -->
                    <div>
                        <label for="angel-totp"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">TOTP (6-digit
                            code)</label>
                        <div class="flex space-x-2">
                            <input type="text" id="angel-totp" name="totp"
                                class="angelReq flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-gray-100"
                                placeholder="Enter 6-digit TOTP code" maxlength="6" required readonly>
                            <button type="button" id="generate-totp-btn"
                                class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition text-sm flex items-center"
                                onclick="generateTOTPCode()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <p class="error-message text-xs text-red-500 mt-1 hidden">TOTP is required</p>

                        <!-- TOTP Display -->
                        <div id="totp-display"
                            class="mt-2 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md hidden">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-blue-800 dark:text-blue-200">Current TOTP Code:
                                    </p>
                                    <p id="current-totp"
                                        class="text-lg font-mono font-bold text-blue-900 dark:text-blue-100">------</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-blue-600 dark:text-blue-300">Expires in:</p>
                                    <p id="totp-countdown" class="text-sm font-bold text-blue-800 dark:text-blue-200">
                                        --s</p>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div id="totp-progress" class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-1">
                                    <div id="totp-progress-bar"
                                        class="bg-blue-600 dark:bg-blue-400 h-1 rounded-full transition-all duration-1000"
                                        style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Fixed Footer with Connect Button -->
            <div class="px-6 py-4">
                <button type="button"
                    onclick="saveBrokerDetails('angel_one','<?= base_url('saveBrokerDetails') ?>','.angelReq','#angel-form')"
                    class="w-full px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-600 transition flex items-center justify-center">
                    <span>Connect</span>
                    <i id="angel-connect-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Broker API Connection Popup -->
<div id="kite-popup" class="popup-overlay">
    <div class="popup-content w-full max-w-md">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100" id="broker-api-title">Connect to
                        Zerodha</h3>
                    <button onclick="closeModal('#kite-popup')" id="close-broker-api"
                        class="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <form id="kite-form">
                    <div class="mb-4">
                        <label for="kiteApiKey"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">API Key</label>
                        <input type="text" id="kiteApiKey" name="kiteApiKey"
                            class="kiteReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your API Key">
                        <span class="error-message hidden">API Key is required</span>
                        <!-- <a href="https://youtu.be/Sl80qVsrFlQ?si=am7UJ50xbyv3Tz7w" target="_blank"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 inline-block">
                            <i class="fas fa-info-circle mr-1"></i> How to get API Key?
                        </a> -->
                    </div>
                    <div class="mb-4">
                        <label for="kiteSecretKey"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Secret Key</label>
                        <input type="text" name="kiteSecretKey"
                            class="kiteReq w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                            placeholder="Enter your Secret Key">
                        <span class="error-message hidden">Secret Key is required</span>
                        <!-- <a href="https://youtu.be/-2yf2-W46bs?si=F-LWm_8AQMtphYqw" target="_blank"
                            class="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 inline-block">
                            <i class="fas fa-info-circle mr-1"></i> Where to find Secret Key?
                        </a> -->
                    </div>
                    <div class="mb-6">
                        <label for="redirect-url"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Redirect URL</label>
                        <div class="flex">
                            <input type="text" id="kite-url" name="redirect-url"
                                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-primary-dark dark:bg-gray-700 dark:text-gray-100"
                                value="https://tradediary.in" readonly>
                            <button onclick="copyUrl('kite-url')" type="button"
                                class="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md hover:bg-gray-300 dark:hover:bg-gray-500 transition">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Copy this URL and paste in your
                            broker's developer settings</p>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button"
                            onclick="saveBrokerDetails('kite','<?= base_url('saveBrokerDetails') ?>','.kiteReq','#kite-form')"
                            class="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-600 transition flex items-center">
                            <span>Connect</span>
                            <i id="connect-spinner" class="fas fa-spinner animate-spin ml-2 hidden"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<!-- Success Popup -->
<div id="success-popup" class="popup-overlay">
    <div class="popup-content w-full max-w-md">
        <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-xl dark:shadow-custom-dark overflow-hidden text-center p-6">
            <div
                class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 dark:text-green-400 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2" id="success-title">Connection
                Successful!</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6" id="success-message">Your broker account has been
                successfully connected.</p>
            <button id="close-success-popup"
                class="px-4 py-2 bg-blue-600 dark:bg-gray-600 text-white rounded-md hover:bg-blue-700 dark:hover:bg-gray-500 transition w-full">
                Continue to Dashboard
            </button>
        </div>
    </div>
</div>