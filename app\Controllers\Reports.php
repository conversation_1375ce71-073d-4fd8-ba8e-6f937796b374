<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;

use ResponseTrait;
helper('cookie');

class Reports extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
    }

    public function Reports()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Reports';
        $data['active'] = 'reports';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'report';
        $data['main_content'] = 'pages/report';

        return view('includes/template', $data);
    }

    public function getTradePerformance()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

    $range = $this->request->getPost('rangeFilter') ?? 1;
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';
    $range = in_array($range, [1, 3, 12]) ? intval($range) : 1;

    $fromDate = date('Y-m-d', strtotime("-{$range} months"));

    $builder = $db->table('trades');
    $builder->select('pnl_amount');
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $fromDate);
    
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $trades = $builder->get()->getResultArray();

    $win = $loss = $breakeven = 0;
    $totalWin = $totalLoss = 0;

    foreach ($trades as $t) {
        $pnl = floatval($t['pnl_amount']);
        if ($pnl > 0) {
            $win++;
            $totalWin += $pnl;
        } elseif ($pnl < 0) {
            $loss++;
            $totalLoss += abs($pnl);
        } else {
            $breakeven++;
        }
    }

    $total = $win + $loss;
    $avgWin = $win > 0 ? round($totalWin / $win, 2) : 0;
    $avgLoss = $loss > 0 ? round($totalLoss / $loss, 2) : 0;
    $winRate = $total > 0 ? round(($win / $total) * 100, 2) : 0;

    $expectancy = ($avgWin * ($winRate / 100)) - ($avgLoss * ((100 - $winRate) / 100));
    $expectancy = round($expectancy, 2);

    return $this->response->setJSON([
        'win' => $win,
        'loss' => $loss,
        'breakeven' => $breakeven,
        'avgWin' => $avgWin,
        'avgLoss' => $avgLoss,
        'winRate' => $winRate,
        'expectancy' => $expectancy
    ]);
}


    public function getDailyPerformance()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

    $range = $this->request->getPost('rangeFilter') ?? 1;
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';
    $range = in_array($range, [1, 3, 12]) ? intval($range) : 1;

    $fromDate = date('Y-m-d', strtotime("-{$range} months"));

    // Get daily total PnL
    $builder = $db->table('trades');
    $builder->select("DATE(datetime) as day, SUM(pnl_amount) as total_pnl");
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $fromDate);

    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $builder->groupBy('DATE(datetime)');
    $dailyPnls = $builder->get()->getResultArray();

    $winDays = $lossDays = $breakevenDays = 0;
    $totalWin = $totalLoss = 0;
    $bestDay = $worstDay = 0;

    foreach ($dailyPnls as $row) {
        $pnl = floatval($row['total_pnl']);

        // Initialize best/worst on first loop
        if ($bestDay === 0 && $worstDay === 0) {
            $bestDay = $worstDay = $pnl;
        }

        if ($pnl > 0) {
            $winDays++;
            $totalWin += $pnl;
        } elseif ($pnl < 0) {
            $lossDays++;
            $totalLoss += abs($pnl);
        } else {
            $breakevenDays++;
        }

        if ($pnl > $bestDay)
            $bestDay = $pnl;
        if ($pnl < $worstDay)
            $worstDay = $pnl;
    }

    $avgWinDay = $winDays > 0 ? round($totalWin / $winDays, 2) : 0;
    $avgLossDay = $lossDays > 0 ? round($totalLoss / $lossDays, 2) : 0;

    return $this->response->setJSON([
        'winDays' => $winDays,
        'lossDays' => $lossDays,
        'breakevenDays' => $breakevenDays,
        'bestDay' => round($bestDay, 2),
        'worstDay' => round($worstDay, 2),
        'avgWinDay' => $avgWinDay,
        'avgLossDay' => $avgLossDay
    ]);
}


    public function getTimeMetrics()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter') ?? 1; // in months
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';
    $startDate = date('Y-m-d', strtotime("-$range months"));

    // Fetch trades aggregated by day
    $builder = $db->table('trades');
    $builder->select("DATE(datetime) as trade_day, SUM(pnl_amount) as daily_pnl");
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $startDate);

    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $builder->groupBy('DATE(datetime)');
    $builder->orderBy('trade_day', 'ASC');
    $days = $builder->get()->getResultArray();

    $tradingDays = count($days);
    $consecWin = 0;
    $consecLoss = 0;
    $currentWinStreak = 0;
    $currentLossStreak = 0;

    foreach ($days as $d) {
        if ($d['daily_pnl'] > 0) {
            $currentWinStreak++;
            $currentLossStreak = 0;
        } elseif ($d['daily_pnl'] < 0) {
            $currentLossStreak++;
            $currentWinStreak = 0;
        } else {
            $currentWinStreak = 0;
            $currentLossStreak = 0;
        }

        $consecWin = max($consecWin, $currentWinStreak);
        $consecLoss = max($consecLoss, $currentLossStreak);
    }

    // Profit/loss by weekday
    $weekdayStats = [];
    foreach ($days as $d) {
        $dayName = date('l', strtotime($d['trade_day']));
        if (!isset($weekdayStats[$dayName])) {
            $weekdayStats[$dayName] = 0;
        }
        $weekdayStats[$dayName] += $d['daily_pnl'];
    }

    arsort($weekdayStats);
    $mostProfitable = array_key_first($weekdayStats);
    $leastProfitable = array_key_last($weekdayStats);

    return $this->response->setJSON([
        'tradingDays' => $tradingDays,
        'consecWinDays' => $consecWin,
        'consecLossDays' => $consecLoss,
        'mostProfitableDay' => $mostProfitable,
        'leastProfitableDay' => $leastProfitable
    ]);
}

    public function getRiskManagementMetrics()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    $builder = $db->table('trades');
    $builder->select('entry_price, exit_price, stop_loss, target, pnl_amount');
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $startDate);
    $builder->where('datetime <=', $endDate);

    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $builder->orderBy('datetime', 'asc');
    $query = $builder->get();

    $trades = $query->getResult();

    $plannedRs = [];
    $realizedRs = [];
    $losses = [];

    // For max drawdown calculation
    $cumulativePnL = 0;
    $peak = 0;
    $maxDrawdown = 0;

    foreach ($trades as $trade) {
        if ($trade->stop_loss != null && $trade->stop_loss != 0 && $trade->entry_price != $trade->stop_loss) {
            $riskPerTrade = $trade->entry_price - $trade->stop_loss;

            // Planned R-Multiple
            if ($trade->target != null) {
                $planned = ($trade->target - $trade->entry_price) / $riskPerTrade;
                if (is_finite($planned)) {
                    $plannedRs[] = $planned;
                }
            }

            // Realized R-Multiple
            if ($trade->exit_price != null) {
                $realized = ($trade->exit_price - $trade->entry_price) / $riskPerTrade;
                if (is_finite($realized)) {
                    $realizedRs[] = $realized;
                }
            }
        }

        // Avg Loss
        if ($trade->pnl_amount < 0) {
            $losses[] = $trade->pnl_amount;
        }

        // Max Drawdown
        $cumulativePnL += $trade->pnl_amount;
        if ($cumulativePnL > $peak) {
            $peak = $cumulativePnL;
        }
        $drawdown = $peak - $cumulativePnL;
        if ($drawdown > $maxDrawdown) {
            $maxDrawdown = $drawdown;
        }
    }

    $plannedR = count($plannedRs) > 0 ? round(array_sum($plannedRs) / count($plannedRs), 2) : 0;
    $realizedR = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;
    $avgLoss = count($losses) > 0 ? round(array_sum($losses) / count($losses), 2) : 0;
    $expectancy = count($realizedRs) > 0 ? round(array_sum($realizedRs) / count($realizedRs), 2) : 0;

    return $this->response->setJSON([
        'planned_r_multiple' => $plannedR,
        'realized_r_multiple' => $realizedR,
        'avg_loss' => $avgLoss,
        'max_drawdown' => round($maxDrawdown, 2),
        'expectancy' => $expectancy
    ]);
}


    public function getEmotionalStateMetrics()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    // Subquery: Count emotions for user in date range
    $subQuery = $db->table('trades t')
        ->select('t.emotion, COUNT(*) as total')
        ->where('t.user_id', $userId)
        ->where('t.deleted_at IS NULL')
        ->where('t.datetime >=', $startDate)
        ->where('t.datetime <=', $endDate);

    if ($marketTypeFilter !== '0') {
        $subQuery->where('t.market_type', $marketTypeFilter);
    }

    $subQuery->groupBy('t.emotion');

    $emotionCounts = $subQuery->get()->getResult();

    // Create a map for counts
    $countsMap = [];
    $totalEmotions = 0;
    foreach ($emotionCounts as $row) {
        $countsMap[$row->emotion] = $row->total;
        $totalEmotions += $row->total;
    }

    // Get all emotions from master table
    $builder = $db->table('emotions');
    $builder->select('id, emotion');
    $builder->where('deleted_at IS NULL');
    $query = $builder->get();
    $emotions = $query->getResult();

    $emotionStats = [];
    foreach ($emotions as $e) {
        $count = isset($countsMap[$e->id]) ? $countsMap[$e->id] : 0;
        $percent = $totalEmotions > 0 ? round(($count / $totalEmotions) * 100) : 0;

        $emotionStats[] = [
            'emotion' => trim($e->emotion),
            'percentage' => $percent
        ];
    }

    // Order by percentage descending
    usort($emotionStats, function ($a, $b) {
        return $b['percentage'] <=> $a['percentage'];
    });

    return $this->response->setJSON($emotionStats);
}


    public function getTargetOutcomeMetrics()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 months
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    $builder = $db->table('trades');
    $builder->select('entry_price, exit_price, stop_loss, target');
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $startDate);
    $builder->where('datetime <=', $endDate);
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }
    $query = $builder->get();

    $trades = $query->getResult();

    $achieved = 0;
    $missed = 0;
    $stoppedEarly = 0;

    $rAchieved = [];
    $rMissed = [];

    foreach ($trades as $trade) {
        if (
            $trade->target == null || $trade->stop_loss == null ||
            $trade->target == 0 || $trade->stop_loss == 0 ||
            $trade->exit_price == null
        ) {
            continue;
        }

        $risk = abs($trade->entry_price - $trade->stop_loss);
        if ($risk == 0)
            continue;

        $rMultiple = ($trade->exit_price - $trade->entry_price) / $risk;

        // Categorization logic
        if ($trade->exit_price >= $trade->target) {
            $achieved++;
            $rAchieved[] = $rMultiple;
        } elseif ($trade->exit_price < $trade->target && $trade->exit_price > $trade->entry_price) {
            $missed++;
            $rMissed[] = $rMultiple;
        } elseif ($trade->exit_price <= $trade->entry_price) {
            $stoppedEarly++;
            $rMissed[] = $rMultiple;
        }
    }

    $total = $achieved + $missed + $stoppedEarly;
    $percentAchieved = $total > 0 ? round(($achieved / $total) * 100) : 0;
    $percentMissed = $total > 0 ? round(($missed / $total) * 100) : 0;
    $percentStopped = $total > 0 ? round(($stoppedEarly / $total) * 100) : 0;

    $avgRAchieved = count($rAchieved) > 0 ? round(array_sum($rAchieved) / count($rAchieved), 2) : 0;
    $avgRMissed = count($rMissed) > 0 ? round(array_sum($rMissed) / count($rMissed), 2) : 0;

    return $this->response->setJSON([
        'target_achieved' => $percentAchieved,
        'target_missed' => $percentMissed,
        'stopped_before_target' => $percentStopped,
        'avg_r_on_achieved' => $avgRAchieved,
        'avg_r_on_missed' => $avgRMissed
    ]);
}


    public function getSetupEffectiveness()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    $joinCondition = 's.id = t.strategy 
        AND t.deleted_at IS NULL 
        AND t.user_id = ' . $db->escape($userId) . ' 
        AND t.datetime >= "' . $startDate . '" 
        AND t.datetime <= "' . $endDate . '"';

    if ($marketTypeFilter !== '0') {
        $joinCondition .= ' AND t.market_type = ' . $db->escape($marketTypeFilter);
    }

    $builder = $db->table('strategies s');
    $builder->select('s.strategy, COUNT(t.id) as total_trades, 
                  SUM(CASE WHEN t.pnl_amount > 0 THEN 1 ELSE 0 END) as wins');
    $builder->join('trades t', $joinCondition, 'left');
    $builder->where('s.deleted_at IS NULL');
    $builder->groupBy('s.id');
    $builder->orderBy('wins DESC');
    $builder->limit(5);
    $query = $builder->get();

    $result = [];

    foreach ($query->getResult() as $row) {
        $winRate = ($row->total_trades > 0) ? round(($row->wins / $row->total_trades) * 100, 1) : 0;
        $result[] = [
            'strategy' => $row->strategy,
            'win_rate' => $winRate
        ];
    }

    return $this->response->setJSON($result);
}


    public function getSymbolFrequency()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // 1, 3, or 12 (months)
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    $builder = $db->table('trades');
    $builder->select('symbol, COUNT(*) as total_trades, SUM(pnl_amount) as total_pnl,
        SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins');
    $builder->where('user_id', $userId);
    $builder->where('deleted_at IS NULL');
    $builder->where('datetime >=', $startDate);
    $builder->where('datetime <=', $endDate);
    
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $builder->groupBy('symbol');
    $query = $builder->get();

    $symbols = $query->getResult();
    $totalTrades = array_sum(array_column($symbols, 'total_trades'));

    $mostTraded = null;
    $mostProfitable = null;
    $leastProfitable = null;
    $highestWinRate = null;
    $lowestWinRate = null;

    foreach ($symbols as $symbol) {
        $symbol->win_rate = $symbol->total_trades > 0 ? round(($symbol->wins / $symbol->total_trades) * 100, 1) : 0;
    }

    if (count($symbols) > 0) {
        usort($symbols, fn($a, $b) => $b->total_trades <=> $a->total_trades);
        $mostTraded = [
            'symbol' => $symbols[0]->symbol,
            'percent' => round(($symbols[0]->total_trades / $totalTrades) * 100)
        ];

        usort($symbols, fn($a, $b) => $b->total_pnl <=> $a->total_pnl);
        $mostProfitable = [
            'symbol' => $symbols[0]->symbol,
            'amount' => round($symbols[0]->total_pnl, 2)
        ];

        usort($symbols, fn($a, $b) => $a->total_pnl <=> $b->total_pnl);
        $leastProfitable = [
            'symbol' => $symbols[0]->symbol,
            'amount' => round($symbols[0]->total_pnl, 2)
        ];

        usort($symbols, fn($a, $b) => $b->win_rate <=> $a->win_rate);
        $highestWinRate = [
            'symbol' => $symbols[0]->symbol,
            'rate' => $symbols[0]->win_rate
        ];

        usort($symbols, fn($a, $b) => $a->win_rate <=> $b->win_rate);
        $lowestWinRate = [
            'symbol' => $symbols[0]->symbol,
            'rate' => $symbols[0]->win_rate
        ];
    }

    return $this->response->setJSON([
        'most_traded' => $mostTraded,
        'most_profitable' => $mostProfitable,
        'least_profitable' => $leastProfitable,
        'highest_win_rate' => $highestWinRate,
        'lowest_win_rate' => $lowestWinRate
    ]);
}


    public function getAvgRRByEmotion()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    $builder = $db->table('emotions');
    $builder->select('emotions.id, emotions.emotion, COUNT(trades.id) as total, 
        AVG((trades.exit_price - trades.entry_price) / ABS(trades.entry_price - trades.stop_loss)) as avg_rr');

    $conditions = 'trades.emotion = emotions.id AND trades.user_id = ' . $db->escape($userId) . 
                  ' AND trades.datetime BETWEEN ' . $db->escape($startDate) . ' AND ' . $db->escape($endDate) . 
                  ' AND trades.deleted_at IS NULL';

    if ($marketTypeFilter !== '0') {
        $conditions .= ' AND trades.market_type = ' . $db->escape($marketTypeFilter);
    }

    $builder->join('trades', $conditions, 'left');
    $builder->where('emotions.deleted_at IS NULL');
    $builder->groupBy('emotions.id');
    $builder->orderBy('avg_rr', 'DESC');
    $query = $builder->get();

    $results = $query->getResultArray();

    foreach ($results as &$row) {
        $row['avg_rr'] = $row['avg_rr'] !== null ? round($row['avg_rr'], 2) : 0;
    }

    return $this->response->setJSON($results);
}


    public function getTradingOutcomes()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $startDate = date('Y-m-d', strtotime("-$range months"));
    $endDate = date('Y-m-d');

    // Total trades in range
    $totalBuilder = $db->table('trades');
    $totalBuilder->selectCount('id', 'total');
    $totalBuilder->where('user_id', $userId);
    $totalBuilder->where('deleted_at', null);
    $totalBuilder->where("DATE(datetime) BETWEEN '$startDate' AND '$endDate'");
    if ($marketTypeFilter !== '0') {
        $totalBuilder->where('market_type', $marketTypeFilter);
    }
    $total = $totalBuilder->get()->getRow()->total;

    // Count per summary type
    $builder = $db->table('summaries');
    $builder->select('summaries.id, summaries.summary, COUNT(trades.id) as count');

    $conditions = "trades.outcome = summaries.id AND trades.user_id = $userId AND trades.deleted_at IS NULL AND DATE(trades.datetime) BETWEEN '$startDate' AND '$endDate'";
    if ($marketTypeFilter !== '0') {
        $conditions .= " AND trades.market_type = " . $db->escape($marketTypeFilter);
    }

    $builder->join('trades', $conditions, 'left');
    $builder->where('summaries.deleted_at IS NULL');
    $builder->groupBy('summaries.id');
    $builder->orderBy('count', 'DESC');
    $query = $builder->get();

    $result = $query->getResultArray();

    return $this->response->setJSON([
        'total_trades' => (int) $total,
        'summaries' => $result
    ]);
}


    public function getTradesPerDay()
    {
        $db = \Config\Database::connect();
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $range = $this->request->getPost('rangeFilter');

        $startDate = date('Y-m-d', strtotime("-$range months"));
        $endDate = date('Y-m-d');

        $trades = $db->table('trades')
            ->select("
            DATE(datetime) as trade_date,
            COUNT(*) as total,
            SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins,
            SUM(CASE WHEN pnl_amount < 0 THEN 1 ELSE 0 END) as losses
        ")
            ->where('user_id', $userId)
            ->where('deleted_at', null)
            ->where("DATE(datetime) BETWEEN '$startDate' AND '$endDate'")
            ->groupBy('DATE(datetime)')
            ->get()
            ->getResultArray();

        $max = 0;
        $min = null;
        $sum = 0;
        $winSum = 0;
        $lossSum = 0;
        $days = count($trades);

        foreach ($trades as $day) {
            $count = (int) $day['total'];
            $wins = (int) $day['wins'];
            $losses = (int) $day['losses'];

            $max = max($max, $count);
            $min = is_null($min) ? $count : min($min, $count);
            $sum += $count;
            $winSum += $wins;
            $lossSum += $losses;
        }

        return $this->response->setJSON([
            'max' => (int) round($max),
            'min' => (int) round($min ?? 0),
            'avg' => (int) round($days ? $sum / $days : 0),
            'wins' => (int) round($days ? $winSum / $days : 0),
            'losses' => (int) round($days ? $lossSum / $days : 0),
        ]);
    }

    public function getQuantityAnalysis()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // e.g., 1, 3, 12
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $builder = $db->table('trades')
        ->select('entry_quantity, pnl_amount')
        ->where('user_id', $userId)
        ->where('deleted_at', null)
        ->where('entry_quantity >', 0);

    // Apply range filter in months
    if (is_numeric($range) && $range > 0) {
        $startDate = date('Y-m-d', strtotime("-{$range} months"));
        $builder->where("DATE(datetime) >=", $startDate);
    }

    // Apply market type filter if not "0"
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $trades = $builder->get()->getResultArray();

    if (empty($trades)) {
        return $this->response->setJSON([
            'max_qty' => 0,
            'min_qty' => 0,
            'avg_qty' => 0,
            'max_qty_pnl' => 0,
            'min_qty_pnl' => 0
        ]);
    }

    $quantities = array_column($trades, 'entry_quantity');
    $maxQty = max($quantities);
    $minQty = min($quantities);
    $avgQty = round(array_sum($quantities) / count($quantities));

    $maxQtyPnl = 0;
    $minQtyPnl = 0;

    foreach ($trades as $trade) {
        if ($trade['entry_quantity'] == $maxQty) {
            $maxQtyPnl += (float) $trade['pnl_amount'];
        }
        if ($trade['entry_quantity'] == $minQty) {
            $minQtyPnl += (float) $trade['pnl_amount'];
        }
    }

    return $this->response->setJSON([
        'max_qty' => (int) $maxQty,
        'min_qty' => (int) $minQty,
        'avg_qty' => (int) $avgQty,
        'max_qty_pnl' => round($maxQtyPnl),
        'min_qty_pnl' => round($minQtyPnl),
    ]);
}


    public function getCapitalUsage()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // Expected as number of months: 1, 3, 12...
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $builder = $db->table('trades')->where('user_id', $userId);

    // If range is numeric, filter by last X months
    if (is_numeric($range) && $range > 0) {
        $startDate = date('Y-m-d', strtotime("-{$range} months"));
        $builder->where("DATE(datetime) >=", $startDate);
    }

    // Apply market type filter if not '0' (i.e., not "All")
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $trades = $builder
        ->select('entry_amount, pnl_amount')
        ->where('entry_amount IS NOT NULL')
        ->where('deleted_at', null)
        ->get()
        ->getResult();

    if (empty($trades)) {
        return $this->response->setJSON([
            'max_capital' => 0,
            'min_capital' => 0,
            'avg_capital' => 0,
            'max_capital_pnl' => 0,
            'min_capital_pnl' => 0,
        ]);
    }

    $amounts = array_column($trades, 'entry_amount');
    $maxCapital = max($amounts);
    $minCapital = min($amounts);
    $avgCapital = array_sum($amounts) / count($amounts);

    $maxCapitalPnl = 0;
    $minCapitalPnl = 0;

    foreach ($trades as $trade) {
        if ($trade->entry_amount == $maxCapital) {
            $maxCapitalPnl += $trade->pnl_amount;
        }
        if ($trade->entry_amount == $minCapital) {
            $minCapitalPnl += $trade->pnl_amount;
        }
    }

    return $this->response->setJSON([
        'max_capital' => round($maxCapital),
        'min_capital' => round($minCapital),
        'avg_capital' => round($avgCapital),
        'max_capital_pnl' => round($maxCapitalPnl),
        'min_capital_pnl' => round($minCapitalPnl),
    ]);
}


    public function getDailyTradeActivity()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter'); // months like 1, 3, 12
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $builder = $db->table('trades')
        ->select("DATE(datetime) as trade_date, COUNT(*) as trade_count")
        ->where('user_id', $userId)
        ->where('deleted_at', null);

    // Apply month filter if provided
    if (is_numeric($range) && $range > 0) {
        $startDate = date('Y-m-d', strtotime("-{$range} months"));
        $builder->where("DATE(datetime) >=", $startDate);
    }

    // Apply marketTypeFilter if not "0" (i.e., not "All")
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    // Group by date to get trades per day
    $result = $builder
        ->groupBy('DATE(datetime)')
        ->get()
        ->getResultArray();

    if (empty($result)) {
        return $this->response->setJSON([
            'avg_per_day' => 0,
            'max_per_day' => 0,
            'one_trade_days' => 0,
            'overtrading_days' => 0,
        ]);
    }

    $tradeCounts = array_column($result, 'trade_count');

    $avg = round(array_sum($tradeCounts) / count($tradeCounts));
    $max = max($tradeCounts);
    $oneTradeDays = count(array_filter($tradeCounts, fn($val) => $val == 1));
    $overtradingDays = count(array_filter($tradeCounts, fn($val) => $val > 7));

    return $this->response->setJSON([
        'avg_per_day' => $avg,
        'max_per_day' => (int) $max,
        'one_trade_days' => $oneTradeDays,
        'overtrading_days' => $overtradingDays,
    ]);
}


    public function getTradeExecution()
{
    $db = \Config\Database::connect();
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $builder = $db->table('trades')
        ->where('user_id', $userId)
        ->where('deleted_at', null);

    // Apply date range filter
    if (!empty($range) && is_numeric($range)) {
        $startDate = date('Y-m-d', strtotime("-$range months"));
        $builder->where('datetime >=', $startDate);
    }

    // Apply market type filter if not "All"
    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $trades = $builder
        ->select('entry_amount, pnl_amount, strategy, outcome')
        ->get()
        ->getResultArray();

    if (empty($trades)) {
        return $this->response->setJSON([
            'total_trades' => 0,
            'avg_capital' => 0,
            'best_strategy' => '-',
            'consecutive_wins' => 0,
            'consecutive_losses' => 0
        ]);
    }

    $total = count($trades);
    $capitalSum = array_sum(array_column($trades, 'entry_amount'));
    $avgCapital = $capitalSum / $total;

    // Strategy-wise PnL aggregation
    $strategyPnls = [];
    foreach ($trades as $trade) {
        $strategyId = $trade['strategy'];
        $strategyPnls[$strategyId] = ($strategyPnls[$strategyId] ?? 0) + $trade['pnl_amount'];
    }

    // Best strategy determination
    $bestStrategyId = array_keys($strategyPnls, max($strategyPnls))[0] ?? null;
    $strategyName = '-';
    if ($bestStrategyId !== null) {
        $strategy = $db->table('strategies')->select('strategy')->where('id', $bestStrategyId)->get()->getRow();
        if ($strategy) {
            $strategyName = $strategy->strategy;
        }
    }

    // Win/loss streak calculation
    $maxWinStreak = 0;
    $maxLossStreak = 0;
    $currentWin = 0;
    $currentLoss = 0;

    foreach ($trades as $trade) {
        if ($trade['pnl_amount'] > 0) {
            $currentWin++;
            $maxWinStreak = max($maxWinStreak, $currentWin);
            $currentLoss = 0;
        } elseif ($trade['pnl_amount'] < 0) {
            $currentLoss++;
            $maxLossStreak = max($maxLossStreak, $currentLoss);
            $currentWin = 0;
        } else {
            $currentWin = 0;
            $currentLoss = 0;
        }
    }

    return $this->response->setJSON([
        'total_trades' => $total,
        'avg_capital' => round($avgCapital),
        'best_strategy' => $strategyName,
        'consecutive_wins' => $maxWinStreak,
        'consecutive_losses' => $maxLossStreak
    ]);
}


    public function getWeekdayPerformance()
{
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $range = $this->request->getPost('rangeFilter');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '0';

    $dateLimit = match ($range) {
        '1' => date('Y-m-d', strtotime('-1 month')),
        '3' => date('Y-m-d', strtotime('-3 months')),
        '12' => date('Y-m-d', strtotime('-1 year')),
        default => date('Y-m-d', strtotime('-1 month'))
    };

    $db = \Config\Database::connect();
    $builder = $db->table('trades');
    $builder->select("DAYNAME(datetime) as weekday, COUNT(*) as total, SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as wins, AVG(rr_ratio) as avg_rr")
        ->where('deleted_at', null)
        ->where('user_id', $userId)
        ->where('datetime >=', $dateLimit);

    if ($marketTypeFilter !== '0') {
        $builder->where('market_type', $marketTypeFilter);
    }

    $builder->groupBy("DAYNAME(datetime)");
    $query = $builder->get()->getResultArray();

    $final = [];

    foreach (['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] as $day) {
        $match = array_filter($query, fn($q) => $q['weekday'] === $day);
        $row = reset($match);

        $final[$day] = [
            'win_rate' => isset($row['total']) && $row['total'] > 0 ? round(($row['wins'] / $row['total']) * 100) : 0,
            'avg_rr' => isset($row['avg_rr']) ? round($row['avg_rr'], 2) : 0
        ];
    }

    return $this->response->setJSON($final);
}

}