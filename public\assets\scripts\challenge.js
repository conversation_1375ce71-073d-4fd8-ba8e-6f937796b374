// Challenge Page JavaScript
let challengeData = null;

// Store challenge data globally for access across functions
if (typeof window !== 'undefined') {
    window.challengeData = null;
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the challenge page
    initializeChallenge();

    // Modal functionality
    initializeModal();

    // Load real trade data
    loadRealTradeData();
});

// Initialize challenge functionality
function initializeChallenge() {
    // Set up event listeners
    const setChallengeBtn = document.getElementById('setChallengeBtn');
    if (setChallengeBtn) {
        setChallengeBtn.addEventListener('click', openChallengeModal);
    }
}

// Modal functionality
function initializeModal() {
    const modal = document.getElementById('challenge-modal');
    const openModalBtn = document.getElementById('setChallengeBtn');
    const closeModalBtn = document.getElementById('close-modal');
    const cancelBtn = document.getElementById('cancel-challenge');
    const timeframeSelect = document.getElementById('timeframe');
    const customDaysContainer = document.getElementById('custom-days-container');
    const challengeForm = document.getElementById('challenge-form');

    // Open modal
    if (openModalBtn) {
        openModalBtn.addEventListener('click', () => {
            modal.classList.remove('opacity-0', 'invisible');
            modal.classList.add('opacity-100', 'visible');
        });
    }

    // Close modal
    function closeModal() {
        modal.classList.add('opacity-0', 'invisible');
        modal.classList.remove('opacity-100', 'visible');
    }

    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeModal);
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Handle timeframe selection
    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', (e) => {
            if (e.target.value === 'custom') {
                customDaysContainer.classList.remove('hidden');
            } else {
                customDaysContainer.classList.add('hidden');
            }
        });
    }

    // Handle form submission
    if (challengeForm) {
        challengeForm.addEventListener('submit', handleChallengeSubmission);
    }
}

// Handle challenge form submission
function handleChallengeSubmission(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const newChallengeData = {
        startAmount: parseFloat(formData.get('start-amount') || document.getElementById('start-amount').value),
        targetAmount: parseFloat(formData.get('target-amount') || document.getElementById('target-amount').value),
        timeframe: formData.get('timeframe') || document.getElementById('timeframe').value,
        customDays: formData.get('custom-days') || document.getElementById('custom-days').value,
        riskPerTrade: parseFloat(formData.get('risk-per-trade') || document.getElementById('risk-per-trade').value),
        startDate: new Date().toISOString().split('T')[0] // Set start date to today
    };

    // Validate the data
    if (!newChallengeData.startAmount || !newChallengeData.targetAmount) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    if (newChallengeData.targetAmount <= newChallengeData.startAmount) {
        showNotification('Target amount must be greater than starting amount', 'error');
        return;
    }

    // Save challenge data
    saveChallengeData(newChallengeData);

    // Close modal
    document.getElementById('challenge-modal').classList.add('opacity-0', 'invisible');
    document.getElementById('challenge-modal').classList.remove('opacity-100', 'visible');

    // Update UI with new challenge settings
    updateChallengeSettings(newChallengeData);

    // Recalculate and update analytics
    updateChallengeAnalytics(newChallengeData);

    // Note: Success notification is handled in saveChallengeData function
}

// Save challenge data
function saveChallengeData(data) {
    // Add timestamp if not present
    if (!data.startDate) {
        data.startDate = new Date().toISOString().split('T')[0];
    }

    // Store in localStorage as backup
    localStorage.setItem('tradingChallenge', JSON.stringify(data));
    console.log('Challenge data saved to localStorage:', data);

    // Save to database
    const formData = new FormData();
    formData.append('start_amount', data.startAmount);
    formData.append('target_amount', data.targetAmount);
    formData.append('timeframe', data.timeframe);
    formData.append('custom_days', data.customDays || '');
    formData.append('risk_per_trade', data.riskPerTrade);

    fetch('/submitChallenge', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log('Challenge saved to database successfully:', result);
            showNotification('Challenge saved successfully!', 'success');

            // Refresh the equity chart and trade history to show data from challenge start date
            refreshEquityChart('Challenge created successfully');
            refreshTradeHistory('Challenge created - refreshing trade history');
        } else {
            console.error('Failed to save challenge to database:', result.message);
            showNotification('Challenge saved locally, but failed to sync to server', 'warning');
        }
    })
    .catch(error => {
        console.error('Error saving challenge to database:', error);
        showNotification('Challenge saved locally, but failed to sync to server', 'warning');
    });
}

// Update UI with real challenge data
function updateChallengeUI(data) {
    // If this is challenge settings data, handle differently
    if (data.startAmount && data.targetAmount) {
        updateChallengeSettings(data);
        return;
    }

    // Update analytics with real trade data
    updateAnalyticsUI(data);

    // Check if we have a saved challenge and update it
    const savedChallenge = localStorage.getItem('tradingChallenge');
    if (savedChallenge) {
        const challengeSettings = JSON.parse(savedChallenge);
        updateChallengeSettings(challengeSettings);
    }
}

function updateChallengeSettings(data) {
    const startAmount = parseFloat(data.startAmount);
    const targetAmount = parseFloat(data.targetAmount);

    // Calculate current amount based on starting capital + total PnL from trades
    let currentAmount = startAmount;
    if (challengeData && challengeData.totalPnl !== undefined) {
        // Use real data from trades
        currentAmount = startAmount + challengeData.totalPnl;
    }

    // Update progress elements
    const startingCapitalElement = document.getElementById('startingCapital');
    const targetCapitalElement = document.getElementById('targetCapital');
    const currentCapitalElement = document.getElementById('currentCapital');
    const progressTextElement = document.getElementById('progressText');
    const progressToTargetElement = document.getElementById('progressToTarget');

    if (startingCapitalElement) startingCapitalElement.innerHTML = formatCurrency(startAmount);
    if (targetCapitalElement) targetCapitalElement.innerHTML = formatCurrency(targetAmount);
    if (currentCapitalElement) currentCapitalElement.innerHTML = formatCurrency(currentAmount);

    // Calculate progress percentage
    const progress = Math.max(0, Math.min(100, ((currentAmount - startAmount) / (targetAmount - startAmount)) * 100));
    if (progressTextElement) progressTextElement.textContent = `${Math.round(progress)}%`;
    if (progressToTargetElement) progressToTargetElement.textContent = `${Math.round(progress)}%`;

    // Update progress bars
    updateProgressBars(progress);

    // Calculate daily target
    const days = data.timeframe === 'custom' ? parseInt(data.customDays) : parseInt(data.timeframe);
    const remainingAmount = Math.max(0, targetAmount - currentAmount);
    const dailyTarget = remainingAmount / days;
    const dailyTargetElement = document.getElementById('dailyTarget');
    if (dailyTargetElement) dailyTargetElement.innerHTML = `${formatCurrency(dailyTarget)}/day`;

    console.log('Challenge settings updated:', {
        startAmount,
        targetAmount,
        currentAmount,
        progress: Math.round(progress),
        dailyTarget: formatNumber(dailyTarget)
    });
}

function updateAnalyticsUI(data) {
    // Update KPI cards with real data
    updateElement('progressToTarget', `${data.winRate}%`);
    updateElement('avgRiskReward', data.avgRiskReward > 0 ? `1:${data.avgRiskReward}` : 'N/A');
    updateElement('highestProfitDay', formatCurrency(data.highestProfitDay));
    updateElement('highestProfitDate', data.highestProfitDate || 'N/A');
    updateElement('maxDrawdown', `-${data.maxDrawdown}%`);
    updateElement('winRate', `${data.winRate}%`);

    // Update progress bars
    updateProgressBars(data.winRate);

    // Update win rate progress bar
    const winRateProgress = document.getElementById('winRateProgress');
    if (winRateProgress) {
        winRateProgress.style.width = `${data.winRate}%`;
    }

    // Update confidence level based on win rate and recent performance
    updateConfidenceLevel(data);
}

// Update progress bars
function updateProgressBars(progress) {
    const progressCircle = document.getElementById('progressCircle');
    const progressBar = document.getElementById('progressBar');
    const capitalProgress = document.getElementById('capitalProgress');
    
    if (progressCircle) {
        const circumference = 2 * Math.PI * 40; // radius = 40
        const offset = circumference - (progress / 100) * circumference;
        progressCircle.style.strokeDashoffset = offset;
    }
    
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
    }
    
    if (capitalProgress) {
        capitalProgress.style.width = `${progress}%`;
    }
}

// Initialize equity chart with real data
function initializeEquityChart() {
    const ctx = document.getElementById('equityChart');
    if (!ctx) {
        console.error('Equity chart canvas not found');
        return;
    }

    // Show loading state
    showChartLoading();

    // Fetch equity data from API with retry
    fetchWithRetry('/getChallengeEquityData')
        .then(response => response.json())
        .then(data => {
            console.log('Raw equity data response:', data);

            if (data.success) {
                // Validate data
                if (!data.labels || !data.data || data.labels.length === 0 || data.data.length === 0) {
                    throw new Error('Invalid or empty equity data received');
                }

                console.log('Processing equity data:', {
                    labels: data.labels,
                    data: data.data,
                    challenge_active: data.challenge_active,
                    start_amount: data.start_amount,
                    trade_count: data.trade_count
                });

                // Clear any existing error messages
                clearChartErrorMessage();

                // Update chart title based on challenge status
                updateChartTitle(data.challenge_active, data.start_date);

                // Create the chart
                createEquityChart(ctx, data.labels, data.data, data.start_amount);
                updateChartQuickStats(data.data);
                updateChartStatistics(data.data);

                // Show informational message based on challenge status
                if (!data.challenge_active) {
                    showNoChallengeMessage();
                } else if (data.trade_count === 0) {
                    showNoTradesMessage(data.start_date);
                } else {
                    clearChartMessage();
                }

                // Store current chart state for potential refreshes
                window.lastEquityData = data;

            } else {
                throw new Error(data.message || 'Failed to load equity data');
            }
        })
        .catch(error => {
            console.error('Error loading equity data:', error);

            // Show error message to user
            showChartErrorMessage(error.message);

            // Create fallback chart
            const fallbackData = [100000, 102000, 98000, 105000, 110000, 108000];
            const fallbackLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];

            createEquityChart(ctx, fallbackLabels, fallbackData, 100000);
            updateChartQuickStats(fallbackData);
            updateChartStatistics(fallbackData);
        })
        .finally(() => {
            hideChartLoading();
            // Initialize chart controls after chart is created
            setTimeout(() => {
                initializeChartControls();
            }, 500);

            // Set up automatic refresh every 5 minutes if challenge is active
            setupAutoRefresh();
        });
}

let equityChart = null;
let equityChartData = null;

function createEquityChart(ctx, labels, data, startAmount = 100000) {
    // Ensure we have valid data
    if (!labels || !data || labels.length === 0 || data.length === 0) {
        console.warn('No valid data for equity chart, using default values');
        labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        data = [startAmount, startAmount, startAmount, startAmount, startAmount, startAmount];
    }

    // Ensure data has some variation to prevent flat line
    let allSame = true;
    for (let i = 1; i < data.length; i++) {
        if (data[i] !== data[0]) {
            allSame = false;
            break;
        }
    }

    // If all values are the same, add slight variation to make the chart more readable
    if (allSame && data.length > 1) {
        console.log('Adding variation to flat equity data');
        // Add small random variations (±0.5%) to avoid completely flat line
        for (let i = 1; i < data.length; i++) {
            const variation = startAmount * (Math.random() * 0.01 - 0.005);
            data[i] = Math.round(data[i] + variation);
        }
    }

    // Store data for filtering
    equityChartData = { labels, data, startAmount };

    // Destroy existing chart if it exists
    if (equityChart) {
        equityChart.destroy();
    }

    equityChart = new Chart(ctx.getContext('2d'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Portfolio Value',
                data: data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#fff',
                pointHoverRadius: 8,
                pointHoverBorderWidth: 3,
                pointRadius: 5,
                pointHoverBackgroundColor: '#1d4ed8',
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                    titleColor: '#f1f5f9',
                    bodyColor: '#e2e8f0',
                    borderColor: '#475569',
                    borderWidth: 1,
                    padding: 16,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return `${context[0].label}`;
                        },
                        label: function(context) {
                            const value = context.parsed.y;
                            const prevValue = context.dataIndex > 0 ?
                                context.dataset.data[context.dataIndex - 1] :
                                context.dataset.data[0];
                            const change = value - prevValue;
                            const changePercent = prevValue > 0 ? ((change / prevValue) * 100).toFixed(2) : 0;

                            return [
                                `Portfolio: &#8377;${value.toLocaleString('en-IN')}`,
                                `Change: ${change >= 0 ? '+' : ''}&#8377;${change.toLocaleString('en-IN')} (${changePercent >= 0 ? '+' : ''}${changePercent}%)`
                            ];
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(156, 163, 175, 0.1)',
                        borderColor: 'rgba(156, 163, 175, 0.2)'
                    },
                    ticks: {
                        color: '#9ca3af',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(156, 163, 175, 0.1)',
                        borderColor: 'rgba(156, 163, 175, 0.2)'
                    },
                    ticks: {
                        color: '#9ca3af',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return '&#8377;' + (value / 1000).toFixed(0) + 'K';
                        }
                    },
                    // Set min value to be slightly below the starting amount
                    // This ensures the chart shows changes from the challenge start amount
                    suggestedMin: startAmount * 0.95
                }
            },
            interaction: {
                intersect: false,
                mode: 'index',
            },
            onHover: (event, activeElements) => {
                event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
            },
            onClick: (event, activeElements) => {
                if (activeElements.length > 0) {
                    const dataIndex = activeElements[0].index;
                    const value = equityChart.data.datasets[0].data[dataIndex];
                    const label = equityChart.data.labels[dataIndex];
                    showEquityPointDetails(label, value, dataIndex);
                }
            }
        }
    });

    // Initialize time period filters
    initializeTimeFilters();
}

// Initialize interactive time period filters
function initializeTimeFilters() {
    const filterButtons = document.querySelectorAll('.equity-filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => {
                btn.classList.remove('bg-blue-100', 'dark:bg-blue-900/20', 'text-blue-600', 'dark:text-blue-400');
                btn.classList.add('bg-gray-100', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'text-gray-600', 'dark:text-gray-300');
            this.classList.add('bg-blue-100', 'dark:bg-blue-900/20', 'text-blue-600', 'dark:text-blue-400');

            // Filter data based on period
            const period = this.dataset.period;
            filterEquityData(period);
        });
    });
}

// Filter equity data based on time period
function filterEquityData(period) {
    if (!equityChartData) return;

    let filteredLabels = [...equityChartData.labels];
    let filteredData = [...equityChartData.data];

    // Calculate how many data points to show based on period
    let pointsToShow;
    switch(period) {
        case '1M':
            pointsToShow = Math.min(30, filteredLabels.length);
            break;
        case '3M':
            pointsToShow = Math.min(90, filteredLabels.length);
            break;
        case '6M':
            pointsToShow = Math.min(180, filteredLabels.length);
            break;
        case '1Y':
            pointsToShow = filteredLabels.length;
            break;
        default:
            pointsToShow = filteredLabels.length;
    }

    // Get the last N points
    if (pointsToShow < filteredLabels.length) {
        filteredLabels = filteredLabels.slice(-pointsToShow);
        filteredData = filteredData.slice(-pointsToShow);
    }

    // Update chart with filtered data
    equityChart.data.labels = filteredLabels;
    equityChart.data.datasets[0].data = filteredData;
    equityChart.update('active');

    // Update chart statistics and quick stats
    updateChartStatistics(filteredData);
    updateChartQuickStats(filteredData);
}

// Update chart statistics display
function updateChartStatistics(data) {
    if (data.length < 2) return;

    const startValue = data[0];
    const endValue = data[data.length - 1];
    const change = endValue - startValue;
    const changePercent = ((change / startValue) * 100).toFixed(2);

    // Update statistics in the chart header
    const statsContainer = document.querySelector('.equity-stats');
    if (statsContainer) {
        const changeClass = change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
        const changeSymbol = change >= 0 ? '+' : '';

        statsContainer.innerHTML = `
            <div class="flex items-center space-x-4 text-sm">
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Current:</span>
                    <span class="font-medium text-gray-900 dark:text-white">&#8377;${endValue.toLocaleString('en-IN')}</span>
                </div>
                <div>
                    <span class="text-gray-500 dark:text-gray-400">Change:</span>
                    <span class="${changeClass} font-medium">${changeSymbol}&#8377;${Math.abs(change).toLocaleString('en-IN')} (${changeSymbol}${changePercent}%)</span>
                </div>
            </div>
        `;
    }
}

// Show detailed information for a specific equity point
function showEquityPointDetails(label, value, index) {
    // Create or update details modal
    let modal = document.getElementById('equity-details-modal');

    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'equity-details-modal';
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 opacity-0 invisible transition-all duration-300';
        document.body.appendChild(modal);
    }

    // Calculate additional metrics
    const prevValue = index > 0 ? equityChart.data.datasets[0].data[index - 1] : value;
    const change = value - prevValue;
    const changePercent = prevValue > 0 ? ((change / prevValue) * 100).toFixed(2) : 0;

    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 transform transition-all">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">Portfolio Details</h3>
                <button onclick="closeEquityDetails()" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times text-gray-400"></i>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="text-sm text-gray-500 dark:text-gray-400">Date</label>
                    <p class="text-lg font-medium text-gray-900 dark:text-white">${label}</p>
                </div>

                <div>
                    <label class="text-sm text-gray-500 dark:text-gray-400">Portfolio Value</label>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">&#8377;${value.toLocaleString('en-IN')}</p>
                </div>

                <div>
                    <label class="text-sm text-gray-500 dark:text-gray-400">Change from Previous</label>
                    <p class="text-lg font-medium ${change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                        ${change >= 0 ? '+' : ''}&#8377;${Math.abs(change).toLocaleString('en-IN')} (${change >= 0 ? '+' : ''}${changePercent}%)
                    </p>
                </div>

                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button onclick="closeEquityDetails()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;

    // Show modal
    modal.classList.remove('opacity-0', 'invisible');
    modal.classList.add('opacity-100', 'visible');
}

// Close equity details modal
function closeEquityDetails() {
    const modal = document.getElementById('equity-details-modal');
    if (modal) {
        modal.classList.add('opacity-0', 'invisible');
        modal.classList.remove('opacity-100', 'visible');
    }
}

// Initialize additional chart controls
function initializeChartControls() {
    // Reset zoom button
    const resetZoomBtn = document.getElementById('resetZoom');
    if (resetZoomBtn) {
        resetZoomBtn.addEventListener('click', () => {
            if (equityChart) {
                equityChart.resetZoom();
                showNotification('Chart zoom reset', 'info');
            }
        });
    }

    // Download chart button
    const downloadBtn = document.getElementById('downloadChart');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', () => {
            if (equityChart) {
                const url = equityChart.toBase64Image();
                const link = document.createElement('a');
                link.download = `equity-curve-${new Date().toISOString().split('T')[0]}.png`;
                link.href = url;
                link.click();
                showNotification('Chart downloaded', 'success');
            }
        });
    }

    // Fullscreen chart button
    const fullscreenBtn = document.getElementById('fullscreenChart');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', () => {
            toggleChartFullscreen();
        });
    }

    // Show chart overlay on first load
    setTimeout(() => {
        const overlay = document.getElementById('chartOverlay');
        if (overlay) {
            overlay.classList.remove('opacity-0', 'invisible');
            overlay.classList.add('opacity-100', 'visible');

            // Hide after 3 seconds
            setTimeout(() => {
                overlay.classList.add('opacity-0', 'invisible');
                overlay.classList.remove('opacity-100', 'visible');
            }, 3000);
        }
    }, 1000);
}

// Toggle chart fullscreen
function toggleChartFullscreen() {
    const chartContainer = document.querySelector('.bg-white.dark\\:bg-gray-800.rounded-xl');

    if (!document.fullscreenElement) {
        chartContainer.requestFullscreen().then(() => {
            chartContainer.classList.add('fullscreen-chart');
            if (equityChart) {
                setTimeout(() => equityChart.resize(), 100);
            }
            showNotification('Chart in fullscreen mode', 'info');
        }).catch(err => {
            console.error('Error entering fullscreen:', err);
            showNotification('Fullscreen not supported', 'error');
        });
    } else {
        document.exitFullscreen().then(() => {
            chartContainer.classList.remove('fullscreen-chart');
            if (equityChart) {
                setTimeout(() => equityChart.resize(), 100);
            }
        });
    }
}

// Calculate and update chart statistics
function updateChartQuickStats(data) {
    if (!data || data.length === 0) return;

    const highest = Math.max(...data);
    const lowest = Math.min(...data);
    const volatility = calculateVolatility(data);
    const trend = calculateTrend(data);

    // Update quick stats
    updateElement('chartHighest', `&#8377;${highest.toLocaleString('en-IN')}`);
    updateElement('chartLowest', `&#8377;${lowest.toLocaleString('en-IN')}`);
    updateElement('chartVolatility', `${volatility.toFixed(1)}%`);

    const trendElement = document.getElementById('chartTrend');
    if (trendElement) {
        const trendIcon = trend > 0 ? 'fas fa-arrow-up' : trend < 0 ? 'fas fa-arrow-down' : 'fas fa-minus';
        const trendText = trend > 0 ? 'Bullish' : trend < 0 ? 'Bearish' : 'Sideways';
        const trendColor = trend > 0 ? 'text-green-600 dark:text-green-400' :
                          trend < 0 ? 'text-red-600 dark:text-red-400' :
                          'text-gray-600 dark:text-gray-400';

        trendElement.className = `text-lg font-bold ${trendColor}`;
        trendElement.innerHTML = `<i class="${trendIcon}"></i> ${trendText}`;
    }
}

// Calculate volatility (standard deviation)
function calculateVolatility(data) {
    if (data.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < data.length; i++) {
        const returnValue = (data[i] - data[i-1]) / data[i-1];
        returns.push(returnValue);
    }

    const mean = returns.reduce((sum, val) => sum + val, 0) / returns.length;
    const variance = returns.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / returns.length;

    return Math.sqrt(variance) * 100; // Convert to percentage
}

// Calculate trend (simple linear regression slope)
function calculateTrend(data) {
    if (data.length < 2) return 0;

    const n = data.length;
    const x = Array.from({length: n}, (_, i) => i);
    const y = data;

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

    return slope;
}

// Show chart loading state
function showChartLoading() {
    const loadingState = document.getElementById('chartLoadingState');
    if (loadingState) {
        loadingState.classList.remove('opacity-0', 'invisible');
        loadingState.classList.add('opacity-100', 'visible');
    }
}

// Hide chart loading state
function hideChartLoading() {
    const loadingState = document.getElementById('chartLoadingState');
    if (loadingState) {
        loadingState.classList.add('opacity-0', 'invisible');
        loadingState.classList.remove('opacity-100', 'visible');
    }
}

// Load real trade data
function loadRealTradeData() {
    console.log('Loading real trade data...');

    // Check if data is already available from PHP
    if (window.challengeAnalytics) {
        console.log('Challenge analytics available from PHP:', window.challengeAnalytics);
        challengeData = window.challengeAnalytics;

        // Store challenge data globally
        window.challengeData = challengeData;

        // Load real trade data first
        loadRealTrades(challengeData.recentTrades);
        loadRealTopTrades(challengeData.topTrades);
        loadRealMostTradedSymbols(challengeData.mostTradedSymbols);
        initializeEquityChart();

        // Then load existing challenge settings (this will use the trade data)
        loadExistingChallenge();
        return;
    }

    // Fallback to API if data not available
    showLoadingState();

    fetch('/getChallengeData')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Challenge data loaded from API:', data.data);
                challengeData = data.data;

                // Store challenge data globally
                window.challengeData = challengeData;

                // Load real trade data first
                loadRealTrades(challengeData.recentTrades);
                loadRealTopTrades(challengeData.topTrades);
                loadRealMostTradedSymbols(challengeData.mostTradedSymbols);
                initializeEquityChart();

                // Then load existing challenge settings (this will use the trade data)
                loadExistingChallenge();
            } else {
                console.error('Failed to load challenge data:', data.message);
                showNotification('Failed to load challenge data', 'error');
                loadExistingChallenge(); // Still try to load existing challenge
            }
        })
        .catch(error => {
            console.error('Error loading challenge data:', error);
            showNotification('Error loading challenge data', 'error');
            loadExistingChallenge(); // Still try to load existing challenge
        })
        .finally(() => {
            hideLoadingState();
        });
}

// Load existing challenge from database or localStorage
function loadExistingChallenge() {
    // Check if challenge data is available from PHP
    if (window.challengeAnalytics && window.challengeAnalytics.activeChallenge) {
        console.log('Active challenge available from PHP:', window.challengeAnalytics.activeChallenge);

        // Convert database challenge to frontend format
        const challengeSettings = {
            startAmount: parseFloat(window.challengeAnalytics.activeChallenge.start_amount),
            targetAmount: parseFloat(window.challengeAnalytics.activeChallenge.target_amount),
            currentAmount: parseFloat(window.challengeAnalytics.activeChallenge.current_amount),
            timeframe: window.challengeAnalytics.activeChallenge.timeframe,
            customDays: window.challengeAnalytics.activeChallenge.custom_days,
            riskPerTrade: parseFloat(window.challengeAnalytics.activeChallenge.risk_per_trade),
            startDate: window.challengeAnalytics.activeChallenge.start_date,
            endDate: window.challengeAnalytics.activeChallenge.end_date,
            progress: parseFloat(window.challengeAnalytics.activeChallenge.progress),
            status: window.challengeAnalytics.activeChallenge.status
        };

        // Update localStorage with the latest data
        localStorage.setItem('tradingChallenge', JSON.stringify(challengeSettings));

        // Update challenge settings UI
        updateChallengeSettings(challengeSettings);

        // Update analytics with the challenge settings
        updateChallengeAnalytics(challengeSettings);

        return;
    }

    // Fallback to localStorage if no data from PHP
    const savedChallenge = localStorage.getItem('tradingChallenge');
    if (savedChallenge) {
        try {
            const challengeSettings = JSON.parse(savedChallenge);
            console.log('Loading existing challenge from localStorage:', challengeSettings);

            // Update challenge settings UI
            updateChallengeSettings(challengeSettings);

            // Update analytics with the challenge settings
            updateChallengeAnalytics(challengeSettings);

        } catch (error) {
            console.error('Error loading saved challenge:', error);
            localStorage.removeItem('tradingChallenge'); // Remove corrupted data
            showDefaultChallengeValues();
        }
    } else {
        // No saved challenge, show default values
        showDefaultChallengeValues();
    }
}

// Show default challenge values when no challenge is set
function showDefaultChallengeValues() {
    // Set default values
    updateElement('daysRemaining', '30');
    updateElement('projectedDate', new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
    updateElement('startingCapital', '&#8377;0');
    updateElement('targetCapital', '&#8377;0');
    updateElement('currentCapital', '&#8377;0');
    updateElement('progressText', '0%');
    updateElement('progressToTarget', '0%');
    updateElement('dailyTarget', '&#8377;0/day');
    updateElement('todayPnl', '&#8377;0 today');
    updateElement('scheduleStatus', 'Set a challenge to start');

    // Reset progress bars
    updateProgressBars(0);

    // Clear challenge data
    window.challengeData = null;

    // Clear trade history
    loadRealTrades([]);

    // Reset trade history title
    updateTradeHistoryTitle(null);
}

// Reset challenge function
function resetChallenge() {
    if (confirm('Are you sure you want to reset your current challenge? This action cannot be undone.')) {
        // Show loading state
        showLoadingState();

        // Call server endpoint to reset challenge
        fetch('/resetChallenge', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear local storage
                localStorage.removeItem('tradingChallenge');

                // Reset UI
                showDefaultChallengeValues();

                // Refresh equity chart and trade history
                refreshEquityChart('Challenge reset - refreshing chart', 500);
                refreshTradeHistory('Challenge reset - refreshing trade history', 800);

                showNotification('Challenge reset successfully!', 'success');
            } else {
                console.error('Failed to reset challenge:', data.message);
                showNotification('Failed to reset challenge: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error resetting challenge:', error);
            showNotification('Error resetting challenge. Please try again.', 'error');
        })
        .finally(() => {
            hideLoadingState();
        });
    }
}

// Add reset button functionality if needed
document.addEventListener('DOMContentLoaded', function() {
    const resetBtn = document.getElementById('resetChallengeBtn');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetChallenge);
    }
});

// Load real trades data
function loadRealTrades(trades) {
    const tbody = document.getElementById('tradesTableBody');
    if (!tbody) return;

    // Check if we have challenge data
    const challengeActive = window.challengeData && window.challengeData.challengeActive;
    const challengeStartDate = window.challengeData && window.challengeData.challengeStartDate;

    // Update trade history title based on challenge status
    if (challengeActive && challengeStartDate) {
        updateTradeHistoryTitle(challengeStartDate);
    } else {
        updateTradeHistoryTitle(null);
    }

    if (!trades || trades.length === 0) {
        let message = 'No trades found';
        let subMessage = 'Set a challenge to start tracking your trades!';
        let icon = 'fas fa-bullseye';

        if (challengeActive && challengeStartDate) {
            const formattedDate = new Date(challengeStartDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
            subMessage = `No trades found since challenge start (${formattedDate})`;
            icon = 'fas fa-chart-line';
        } else if (!challengeActive) {
            message = 'No active challenge';
            subMessage = 'Set a challenge to start tracking your trading progress!';
            icon = 'fas fa-bullseye';
        }

        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div class="flex flex-col items-center">
                        <i class="${icon} text-4xl mb-2 opacity-50"></i>
                        <p>${message}</p>
                        <p class="text-sm">${subMessage}</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = trades.map(trade => {
        const pnl = parseFloat(trade.pnl_amount || 0);
        const pnlClass = pnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
        const pnlText = pnl >= 0 ? `+&#8377;${formatNumber(Math.abs(pnl))}` : `-&#8377;${formatNumber(Math.abs(pnl))}`;
        const tradeDate = new Date(trade.datetime).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
        const rrRatio = trade.rr_ratio ? `1:${trade.rr_ratio}` : 'N/A';
        const tradeType = trade.trade_type || 'BUY';
        const entryPrice = parseFloat(trade.entry_price || 0);
        const exitPrice = parseFloat(trade.exit_price || 0);
        const quantity = parseInt(trade.entry_quantity || 0);

        return `
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${tradeDate}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">${trade.symbol || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${tradeType}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${entryPrice > 0 ? formatNumber(entryPrice) : 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${exitPrice > 0 ? formatNumber(exitPrice) : 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${quantity > 0 ? formatNumber(quantity) : 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm ${pnlClass}">${pnlText}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${rrRatio}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${trade.rationale || trade.lesson || 'No notes'}</td>
            </tr>
        `;
    }).join('');
}

// Load real top trades
function loadRealTopTrades(topTrades) {
    const container = document.getElementById('topTradesContainer');
    if (!container) return;

    if (!topTrades || topTrades.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-trophy text-4xl mb-2 opacity-50"></i>
                <p>No profitable trades yet</p>
                <p class="text-sm">Keep trading to see your top performers!</p>
            </div>
        `;
        return;
    }

    const icons = ['fas fa-trophy', 'fas fa-medal', 'fas fa-award'];

    container.innerHTML = topTrades.map((trade, index) => {
        const pnl = parseFloat(trade.pnl_amount || 0);
        const pnlText = pnl >= 0 ? `+&#8377;${formatNumber(Math.abs(pnl))}` : `-&#8377;${formatNumber(Math.abs(pnl))}`;
        const pnlClass = pnl >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
        const tradeDate = new Date(trade.datetime).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
        const rrRatio = trade.rr_ratio ? `1:${trade.rr_ratio}` : 'N/A';
        const icon = icons[index] || 'fas fa-star';

        return `
            <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 group transition-colors">
                <div class="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 mr-4">
                    <i class="${icon} text-xl"></i>
                </div>
                <div class="flex-1">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100">${trade.symbol || 'N/A'}</h4>
                        <span class="${pnlClass} font-medium">${pnlText}</span>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">${tradeDate} • ${rrRatio} R:R</p>
                </div>
            </div>
        `;
    }).join('');
}

// Load real most traded symbols
function loadRealMostTradedSymbols(symbolsData) {
    const container = document.getElementById('mostTradedSymbols');
    if (!container) return;

    if (!symbolsData || Object.keys(symbolsData).length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-chart-bar text-4xl mb-2 opacity-50"></i>
                <p>No trading data yet</p>
                <p class="text-sm">Start trading to see symbol frequency!</p>
            </div>
        `;
        return;
    }

    // Convert object to array and calculate percentages
    const symbolsArray = Object.entries(symbolsData).map(([symbol, count]) => ({
        name: symbol,
        trades: count
    }));

    // Calculate max trades for percentage calculation
    const maxTrades = Math.max(...symbolsArray.map(s => s.trades));

    container.innerHTML = symbolsArray.map(symbol => {
        const percentage = maxTrades > 0 ? (symbol.trades / maxTrades) * 100 : 0;

        return `
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-900 dark:text-gray-100">${symbol.name}</span>
                    <span class="text-blue-600 dark:text-blue-400">${symbol.trades} trade${symbol.trades !== 1 ? 's' : ''}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    }).join('');
}

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat('en-IN').format(num);
}

// Utility function to format currency with rupee symbol
function formatCurrency(num) {
    if (num === null || num === undefined || isNaN(num)) return '&#8377;0';
    return `&#8377;${formatNumber(num)}`;
}

// Update chart title based on challenge status
function updateChartTitle(challengeActive, startDate) {
    const titleElement = document.querySelector('.equity-chart-title');
    if (titleElement) {
        if (challengeActive && startDate) {
            const formattedDate = new Date(startDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
            titleElement.textContent = `Portfolio Equity Curve (Since Challenge Start: ${formattedDate})`;

            // Also update trade history title
            updateTradeHistoryTitle(startDate);
        } else {
            titleElement.textContent = 'Portfolio Equity Curve (Last 12 Months)';

            // Reset trade history title
            updateTradeHistoryTitle(null);
        }
    }
}

// Update trade history title based on challenge status
function updateTradeHistoryTitle(startDate) {
    const titleElement = document.querySelector('.trade-history-title');
    if (titleElement) {
        if (startDate) {
            const formattedDate = new Date(startDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
            titleElement.textContent = `Trade History (Since ${formattedDate})`;
        } else {
            titleElement.textContent = 'Trade History';
        }
    }
}

// Show message when no challenge is active
function showNoChallengeMessage() {
    const messageContainer = document.querySelector('.equity-chart-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2"></i>
                    <span class="text-blue-800 dark:text-blue-200 text-sm">
                        Set a challenge to see your equity curve from the challenge start date. Currently showing last 12 months of trading data.
                    </span>
                </div>
            </div>
        `;
    }
}

// Show message when no trades exist after challenge start
function showNoTradesMessage(startDate) {
    const messageContainer = document.querySelector('.equity-chart-message');
    if (messageContainer) {
        const formattedDate = new Date(startDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
        messageContainer.innerHTML = `
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                    <span class="text-yellow-800 dark:text-yellow-200 text-sm">
                        No trades found since challenge start date (${formattedDate}). The chart shows your starting capital amount.
                    </span>
                </div>
            </div>
        `;
    }
}

// Show error message
function showChartErrorMessage(errorMessage) {
    const messageContainer = document.querySelector('.equity-chart-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 mr-2"></i>
                    <span class="text-red-800 dark:text-red-200 text-sm">
                        Error loading chart data: ${errorMessage}. Showing sample data.
                    </span>
                </div>
            </div>
        `;
    }
}

// Clear chart messages
function clearChartMessage() {
    const messageContainer = document.querySelector('.equity-chart-message');
    if (messageContainer) {
        messageContainer.innerHTML = '';
    }
}

// Clear error messages
function clearChartErrorMessage() {
    clearChartMessage();
}

// Refresh equity chart with optional delay and message
function refreshEquityChart(message = 'Refreshing chart...', delay = 1000) {
    console.log(message);

    setTimeout(() => {
        console.log('Executing equity chart refresh...');
        initializeEquityChart();
    }, delay);
}

// Refresh trade history data
function refreshTradeHistory(message = 'Refreshing trade history...', delay = 500) {
    console.log(message);

    setTimeout(() => {
        console.log('Executing trade history refresh...');
        // Fetch updated challenge data
        fetch('/getChallengeData')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Updated challenge data loaded:', data.data);
                    window.challengeData = data.data;

                    // Reload trades with updated data (will be empty if no active challenge)
                    loadRealTrades(data.data.recentTrades || []);
                    loadRealTopTrades(data.data.topTrades || []);
                    loadRealMostTradedSymbols(data.data.mostTradedSymbols || []);
                } else {
                    console.error('Failed to refresh trade history:', data.message);
                    // Clear trade history on error
                    loadRealTrades([]);
                    loadRealTopTrades([]);
                    loadRealMostTradedSymbols([]);
                }
            })
            .catch(error => {
                console.error('Error refreshing trade history:', error);
                // Clear trade history on error
                loadRealTrades([]);
                loadRealTopTrades([]);
                loadRealMostTradedSymbols([]);
            });
    }, delay);
}

// Function to check if we're on the challenge page
function isChallengePage() {
    return window.location.pathname.includes('/Challenge');
}

// Setup event listeners for trade updates
document.addEventListener('DOMContentLoaded', function() {
    // Listen for custom event when trades are updated
    document.addEventListener('tradesUpdated', function(e) {
        console.log('Trades updated event received');
        if (isChallengePage()) {
            refreshEquityChart('Trades updated - refreshing equity chart');
            refreshTradeHistory('Trades updated - refreshing trade history');
        }
    });

    // Listen for trade form submission success
    if (typeof $ !== 'undefined') {
        $(document).ajaxSuccess(function(event, xhr, settings) {
            // Check if this is a trade submission
            if (settings.url &&
                (settings.url.includes('/submitTrade') ||
                 settings.url.includes('/syncTrades') ||
                 settings.url.includes('/importTrades'))) {
                console.log('Trade submission detected');
                if (isChallengePage()) {
                    refreshEquityChart('New trade added - refreshing equity chart');
                    refreshTradeHistory('New trade added - refreshing trade history');
                }
            }
        });
    }
});

// Auto-refresh timer
let equityChartRefreshTimer = null;

// Set up automatic refresh for equity chart
function setupAutoRefresh() {
    // Clear any existing timer
    if (equityChartRefreshTimer) {
        clearInterval(equityChartRefreshTimer);
    }

    // Check if we have an active challenge
    const isActiveChallenge = document.querySelector('.equity-chart-title').textContent.includes('Challenge Start');

    if (isActiveChallenge) {
        // Set up a refresh every 5 minutes (300000 ms)
        equityChartRefreshTimer = setInterval(() => {
            console.log('Auto-refreshing equity chart...');
            initializeEquityChart();
        }, 300000);

        console.log('Automatic equity chart refresh enabled');
    }
}

// Handle network errors and retries
function fetchWithRetry(url, options = {}, maxRetries = 3) {
    return new Promise((resolve, reject) => {
        const attemptFetch = (retriesLeft) => {
            fetch(url, options)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    resolve(response);
                })
                .catch(error => {
                    if (retriesLeft > 0) {
                        console.log(`Retrying fetch (${maxRetries - retriesLeft + 1}/${maxRetries})...`);
                        setTimeout(() => attemptFetch(retriesLeft - 1), 1000);
                    } else {
                        console.error('Fetch failed after retries:', error);
                        reject(error);
                    }
                });
        };

        attemptFetch(maxRetries);
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
    
    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Open challenge modal function (for external use)
function openChallengeModal() {
    const modal = document.getElementById('challenge-modal');
    if (modal) {
        // Pre-populate form with existing challenge data if available
        const savedChallenge = localStorage.getItem('tradingChallenge');
        if (savedChallenge) {
            try {
                const challengeSettings = JSON.parse(savedChallenge);
                document.getElementById('start-amount').value = challengeSettings.startAmount || '';
                document.getElementById('target-amount').value = challengeSettings.targetAmount || '';
                document.getElementById('timeframe').value = challengeSettings.timeframe || '30';
                document.getElementById('custom-days').value = challengeSettings.customDays || '';
                document.getElementById('risk-per-trade').value = challengeSettings.riskPerTrade || '';

                // Show/hide custom days container based on timeframe
                const customDaysContainer = document.getElementById('custom-days-container');
                if (challengeSettings.timeframe === 'custom') {
                    customDaysContainer.classList.remove('hidden');
                } else {
                    customDaysContainer.classList.add('hidden');
                }
            } catch (error) {
                console.error('Error loading challenge data for form:', error);
            }
        }

        modal.classList.remove('opacity-0', 'invisible');
        modal.classList.add('opacity-100', 'visible');
    }
}

// Additional utility functions for real data integration
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        // Check if value contains HTML entities (like &#8377;)
        if (typeof value === 'string' && value.includes('&#')) {
            element.innerHTML = value;
        } else {
            element.textContent = value;
        }
    }
}

function updateConfidenceLevel(data) {
    // Calculate confidence based on win rate and recent performance
    let confidenceLevel = 'Low';
    let confidencePosition = 25; // Default position on confidence meter

    if (data.winRate >= 70) {
        confidenceLevel = 'High';
        confidencePosition = 80;
    } else if (data.winRate >= 50) {
        confidenceLevel = 'Medium';
        confidencePosition = 60;
    } else if (data.winRate >= 30) {
        confidenceLevel = 'Fair';
        confidencePosition = 40;
    }

    // Update confidence display
    const confidenceDot = document.querySelector('.confidence-dot');
    if (confidenceDot) {
        confidenceDot.style.left = `${confidencePosition}%`;
    }

    // Update confidence text
    const confidenceText = document.querySelector('.confidence-meter + div span');
    if (confidenceText) {
        confidenceText.textContent = confidenceLevel;
        confidenceText.className = getConfidenceColorClass(confidenceLevel);
    }
}

function getConfidenceColorClass(level) {
    switch (level) {
        case 'High': return 'text-green-600 dark:text-green-400 font-medium';
        case 'Medium': return 'text-yellow-600 dark:text-yellow-400 font-medium';
        case 'Fair': return 'text-orange-600 dark:text-orange-400 font-medium';
        default: return 'text-red-600 dark:text-red-400 font-medium';
    }
}

function showLoadingState() {
    // Add loading indicators to key sections
    const sections = ['tradesTableBody', 'topTradesContainer', 'mostTradedSymbols'];
    sections.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
                </div>
            `;
        }
    });
}

function hideLoadingState() {
    // Loading state will be replaced by actual data
    // This function is called after data is loaded
}

// Enhanced challenge data update for real analytics
function updateChallengeAnalytics(challengeSettings) {
    // Update days remaining and projected date based on challenge settings
    const savedChallenge = challengeSettings || JSON.parse(localStorage.getItem('tradingChallenge') || '{}');

    if (savedChallenge && savedChallenge.startAmount && savedChallenge.targetAmount) {
        const startDate = new Date(savedChallenge.startDate || Date.now());
        const days = savedChallenge.timeframe === 'custom' ?
                    parseInt(savedChallenge.customDays) :
                    parseInt(savedChallenge.timeframe);

        const endDate = new Date(startDate.getTime() + (days * 24 * 60 * 60 * 1000));
        const today = new Date();
        const remainingDays = Math.max(0, Math.ceil((endDate - today) / (24 * 60 * 60 * 1000)));

        updateElement('daysRemaining', remainingDays.toString());
        updateElement('projectedDate', endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));

        // Calculate daily target
        const startAmount = parseFloat(savedChallenge.startAmount);
        const targetAmount = parseFloat(savedChallenge.targetAmount);
        const totalTarget = targetAmount - startAmount;
        const avgDailyTarget = totalTarget / days;

        // Update today's PnL if available
        const today_str = today.toISOString().split('T')[0];
        let todayPnl = 0;

        if (challengeData && challengeData.dailyPnl && challengeData.dailyPnl[today_str]) {
            todayPnl = challengeData.dailyPnl[today_str];
        }

        const todayPnlText = todayPnl >= 0 ? `+&#8377;${formatNumber(Math.abs(todayPnl))}` : `-&#8377;${formatNumber(Math.abs(todayPnl))}`;
        updateElement('todayPnl', `${todayPnlText} today`);

        // Update schedule status and daily progress
        const scheduleStatus = todayPnl >= avgDailyTarget ? 'Ahead of schedule' : 'Behind schedule';
        updateElement('scheduleStatus', scheduleStatus);

        // Update daily progress bar
        const dailyProgress = Math.min(100, Math.max(0, (todayPnl / avgDailyTarget) * 100));
        const dailyProgressBar = document.getElementById('dailyProgress');
        if (dailyProgressBar) {
            dailyProgressBar.style.width = `${dailyProgress}%`;
        }

        // Update daily target display
        updateElement('dailyTarget', `&#8377;${formatNumber(avgDailyTarget)}/day`);
    }
}
