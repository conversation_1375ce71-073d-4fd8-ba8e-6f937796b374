<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Page Header -->
    <div class="flex justify-end items-center mb-6">
        <!-- <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Take a Challenge</h1>
            <p class="text-gray-500 dark:text-gray-400">Track your progress towards your trading goals with real-time
                analytics and performance metrics.</p>
        </div> -->
        <div class="flex space-x-3">
            <button id="setChallengeBtn"
                class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-md text-sm font-medium hover:bg-primary-dark dark:hover:bg-primary-light transition-all shadow hover:shadow-lg">
                <i class="fas fa-bullseye mr-2"></i> Set Challenge
            </button>
        </div>
    </div>

    <!-- Challenge Setting Modal -->
    <div id="challenge-modal"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 opacity-0 invisible transition-all duration-300">
        <div
            class="modal-content bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4 transform transition-all">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">Set Trading Challenge</h3>
                <button id="close-modal"
                    class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-times text-gray-400"></i>
                </button>
            </div>

            <form id="challenge-form" class="space-y-4">
                <div>
                    <label for="start-amount"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Starting Capital</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">₹</span>
                        </div>
                        <input type="number" id="start-amount"
                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg pl-8 pr-4 py-2 w-full focus:border-primary-light dark:focus:border-primary-dark focus:ring-1 focus:ring-primary-light dark:focus:ring-primary-dark text-gray-900 dark:text-white"
                            placeholder="1,00,000" required>
                    </div>
                </div>

                <div>
                    <label for="target-amount"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Target Capital</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">₹</span>
                        </div>
                        <input type="number" id="target-amount"
                            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg pl-8 pr-4 py-2 w-full focus:border-primary-light dark:focus:border-primary-dark focus:ring-1 focus:ring-primary-light dark:focus:ring-primary-dark text-gray-900 dark:text-white"
                            placeholder="2,50,000" required>
                    </div>
                </div>

                <div>
                    <label for="timeframe"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Timeframe</label>
                    <select id="timeframe"
                        class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 w-full focus:border-primary-light dark:focus:border-primary-dark focus:ring-1 focus:ring-primary-light dark:focus:ring-primary-dark text-gray-900 dark:text-white">
                        <option value="7">1 Week</option>
                        <option value="30" selected>1 Month</option>
                        <option value="90">3 Months</option>
                        <option value="180">6 Months</option>
                        <option value="365">1 Year</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>

                <div id="custom-days-container" class="hidden">
                    <label for="custom-days"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Custom Days</label>
                    <input type="number" id="custom-days"
                        class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 w-full focus:border-primary-light dark:focus:border-primary-dark focus:ring-1 focus:ring-primary-light dark:focus:ring-primary-dark text-gray-900 dark:text-white"
                        placeholder="Enter number of days">
                </div>

                <div>
                    <label for="risk-per-trade"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Max Risk Per Trade
                        (%)</label>
                    <input type="number" id="risk-per-trade"
                        class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 w-full focus:border-primary-light dark:focus:border-primary-dark focus:ring-1 focus:ring-primary-light dark:focus:ring-primary-dark text-gray-900 dark:text-white"
                        placeholder="2" min="0.1" max="10" step="0.1">
                </div>

                <div class="flex space-x-3 pt-2">
                    <button type="submit"
                        class="flex-1 px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-lg font-medium hover:bg-primary-dark dark:hover:bg-primary-light transition-all flex items-center justify-center space-x-2">
                        <i class="fas fa-check"></i>
                        <span>Set Challenge</span>
                    </button>
                    <button type="button" id="cancel-challenge"
                        class="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-400 dark:hover:bg-gray-500 transition-all">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 md:p-8 relative overflow-hidden shadow-lg">
            <div
                class="absolute inset-0 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 opacity-50">
            </div>
            <div class="relative">
                <div class="flex flex-col space-y-6 md:space-y-0 md:flex-row md:justify-between md:items-center">
                    <div class="space-y-4">
                        <h2 class="text-2xl md:text-4xl font-bold text-gray-800 dark:text-white">Capital Growth
                            Challenge</h2>
                        <p class="text-gray-600 dark:text-gray-400 text-sm md:text-base max-w-lg">
                            Track your progress towards your trading goals with real-time analytics and performance
                            metrics.
                        </p>
                    </div>

                    <div
                        class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 w-full md:w-auto">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <p class="text-xs text-gray-500 dark:text-gray-400">Days Remaining</p>
                                <p class="text-xl font-bold text-yellow-600 dark:text-yellow-400" id="daysRemaining">--
                                </p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500 dark:text-gray-400">Projected Date</p>
                                <p class="text-xl font-bold text-gray-800 dark:text-white" id="projectedDate">--</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress visualization -->
                <div class="mt-8 md:mt-12 flex flex-col md:flex-row items-center justify-between gap-8">
                    <!-- Progress ring -->
                    <div class="w-full md:w-1/3 flex flex-col items-center">
                        <div class="relative w-40 h-40 md:w-48 md:h-48">
                            <svg class="w-full h-full" viewBox="0 0 100 100">
                                <circle class="text-gray-300 dark:text-gray-600" stroke-width="10" stroke="currentColor"
                                    fill="transparent" r="40" cx="50" cy="50" />
                                <circle class="progress-ring__circle text-blue-600 dark:text-blue-400" stroke-width="10"
                                    stroke-linecap="round" stroke="currentColor" fill="transparent" r="40" cx="50"
                                    cy="50" stroke-dasharray="251.2" stroke-dashoffset="251.2" id="progressCircle" />
                                <text x="50" y="50" text-anchor="middle" dy=".3em"
                                    class="text-1xl md:text-1xl font-bold fill-current text-gray-800 dark:text-white"
                                    id="progressText">0%</text>
                            </svg>
                        </div>
                        <p class="mt-4 text-gray-500 dark:text-gray-400 text-sm">Progress to target</p>
                    </div>

                    <!-- Progress bars -->
                    <div class="w-full md:w-2/3 space-y-6">
                        <!-- Capital Progress -->
                        <div>
                            <div class="grid grid-cols-3 gap-2 mb-2 text-sm md:text-base">
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Starting</span>
                                    <span class="block md:inline md:ml-2 text-blue-600 dark:text-blue-400 font-medium"
                                        id="startingCapital">₹0</span>
                                </div>
                                <div class="text-center">
                                    <span class="text-gray-500 dark:text-gray-400">Current</span>
                                    <span class="block md:inline md:ml-2 text-gray-800 dark:text-white font-medium"
                                        id="currentCapital">₹0</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-gray-500 dark:text-gray-400">Target</span>
                                    <span class="block md:inline md:ml-2 text-green-600 dark:text-green-400 font-medium"
                                        id="targetCapital">₹0</span>
                                </div>
                            </div>
                            <div class="relative h-2 md:h-3 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                <div class="absolute inset-0">
                                    <div class="bg-green-600 dark:bg-green-400 h-full" style="width: 0%"
                                        id="capitalProgress"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Daily Target -->
                        <div>
                            <div class="flex justify-between mb-1 text-sm md:text-base">
                                <span class="text-gray-500 dark:text-gray-400">Daily Target</span>
                                <span class="text-gray-800 dark:text-white" id="dailyTarget">₹0/day</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div class="bg-purple-600 dark:bg-purple-400 h-2 rounded-full" style="width: 0%"
                                    id="dailyProgress"></div>
                            </div>
                            <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <span id="scheduleStatus">Set challenge to track</span>
                                <span id="todayPnl">₹0 today</span>
                            </div>
                        </div>

                        <!-- Win Rate -->
                        <div>
                            <div class="flex justify-between mb-1 text-sm md:text-base">
                                <span class="text-gray-500 dark:text-gray-400">Win Rate</span>
                                <span class="text-gray-800 dark:text-white" id="winRate">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div class="bg-green-600 dark:bg-green-400 h-2 rounded-full" style="width: 0%"
                                    id="winRateProgress"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- KPI Cards -->
    <section class="mb-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Progress to Target -->
            <div
                class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">Progress to Target</p>
                        <h3 class="text-2xl font-bold mt-1 text-gray-800 dark:text-white" id="progressToTarget">0%</h3>
                    </div>
                    <div
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/20 transition-colors">
                        <i class="fas fa-bullseye text-blue-600 dark:text-blue-400 text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full" style="width: 0%" id="progressBar">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Risk:Reward -->
            <div
                class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">Avg Risk:Reward</p>
                        <h3 class="text-2xl font-bold mt-1 text-gray-800 dark:text-white" id="avgRiskReward">N/A</h3>
                    </div>
                    <div
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/20 transition-colors">
                        <i class="fas fa-balance-scale-right text-blue-600 dark:text-blue-400 text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-500 dark:text-gray-400">Start trading to see metrics</p>
                </div>
            </div>

            <!-- Highest Profit Day -->
            <div
                class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">Highest Profit Day</p>
                        <h3 class="text-2xl font-bold mt-1 text-gray-800 dark:text-white" id="highestProfitDay">₹0</h3>
                    </div>
                    <div
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 group-hover:bg-green-100 dark:group-hover:bg-green-900/20 transition-colors">
                        <i class="fas fa-arrow-up text-green-600 dark:text-green-400 text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-500 dark:text-gray-400" id="highestProfitDate">No trades yet</p>
                </div>
            </div>

            <!-- Max Drawdown -->
            <div
                class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">Max Drawdown</p>
                        <h3 class="text-2xl font-bold mt-1 text-gray-800 dark:text-white" id="maxDrawdown">0%</h3>
                    </div>
                    <div
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 group-hover:bg-red-100 dark:group-hover:bg-red-900/20 transition-colors">
                        <i class="fas fa-arrow-down text-red-600 dark:text-red-400 text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-500 dark:text-gray-400">No drawdown data</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Equity Curve -->
    <section class="mb-6 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg">
            <!-- Chart Header with Controls -->
            <div
                class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
                <div>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Portfolio Equity Curve</h3>
                    <div class="equity-stats">
                        <!-- Statistics will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Time Period Filters -->
                
            </div>

            <!-- Chart Container with Loading State -->
            <div class="relative">
                <div id="chartLoadingState"
                    class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg opacity-0 invisible transition-all duration-300">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span class="text-gray-600 dark:text-gray-400">Loading chart data...</span>
                    </div>
                </div>

                <div class="relative w-full h-72 md:h-96">
                    <canvas id="equityChart" class="w-full h-full block"></canvas>

                    <!-- Chart Overlay Info -->
                    <div class="absolute top-4 left-4 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg opacity-0 invisible transition-all duration-300"
                        id="chartOverlay">
                        <div class="text-sm">
                            <div class="text-gray-500 dark:text-gray-400">Hover over points for details</div>
                            <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">Click points for more information
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart Legend and Quick Stats -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Highest</div>
                        <div id="chartHighest" class="text-lg font-bold text-green-600 dark:text-green-400">₹0</div>
                    </div>
                    <div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Lowest</div>
                        <div id="chartLowest" class="text-lg font-bold text-red-600 dark:text-red-400">₹0</div>
                    </div>
                    <div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Volatility</div>
                        <div id="chartVolatility" class="text-lg font-bold text-blue-600 dark:text-blue-400">0%</div>
                    </div>
                    <div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Trend</div>
                        <div id="chartTrend" class="text-lg font-bold text-gray-600 dark:text-gray-400">
                            <i class="fas fa-arrow-up"></i> Bullish
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Confidence Meter -->
    <section class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">Trading Confidence Index</h3>
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">Low Confidence</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">High Confidence</span>
            </div>
            <div class="relative mb-4">
                <div
                    class="confidence-meter w-full h-2 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500">
                    <div class="confidence-dot absolute w-3 h-3 bg-white border-2 border-gray-800 dark:border-gray-200 rounded-full top-[-2px] transform -translate-x-1/2"
                        style="left: 75%;"></div>
                </div>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">Your current confidence level: <span
                        id="confidenceLabel" class="text-yellow-600 dark:text-yellow-400 font-medium">High</span></p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">Based on your recent trading performance and
                    consistency</p>
            </div>
        </div>
    </section>

    <!-- Trades Table -->
    <section class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg">
            <div
                class="p-5 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">Trade History</h3>
                <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Date</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Symbol</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Type</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Entry</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Exit</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Qty</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                P&L</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                R:R</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Notes</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
                        id="tradesTableBody">
                        <!-- Trade rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div
                class="px-5 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <div class="text-sm text-gray-600 dark:text-gray-400 pagination-info">Loading...</div>
                <div class="flex space-x-1 pagination-controls"></div>
            </div>
        </div>
    </section>

    <!-- Insights Section -->
    <section class="mb-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Top 3 Trades -->
            <div class="bg-white dark:bg-gray-800 rounded-xl p-5 lg:col-span-2 shadow-lg">
                <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">Top 3 Trades</h3>
                <div class="space-y-4" id="topTradesContainer">
                    <!-- Top trades will be populated by JavaScript -->
                </div>
            </div>

            <!-- Most Traded Symbols -->
            <div class="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-lg">
                <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">Most Traded Symbols</h3>
                <div class="space-y-3" id="mostTradedSymbols">
                    <!-- Most traded symbols will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </section>
</div>

<script>
    // Pass challenge data from PHP to JavaScript
    // window.challengeAnalytics = <?//= json_encode($challengeData ?? []) ?>;
</script>