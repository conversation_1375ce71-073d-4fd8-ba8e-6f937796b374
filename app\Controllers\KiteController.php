<?php

namespace App\Controllers;

use App\Models\KiteBrokerModel;

helper('cookie');

class KiteController extends BaseController
{

    public function kiteLogin()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $apiKey = (new KiteBrokerModel())->where('user_id', $userId)->first()['api_key'];
        $encryptedUserId = base64_encode($userId);
        $loginUrl = "https://kite.trade/connect/login?api_key=$apiKey&v=3&state=$encryptedUserId";

        return view('kite_login', ['loginUrl' => $loginUrl]);
    }

    public function saveAccessToken()
    {
        $requestToken = $this->request->getPost('request_token');
        $state = $this->request->getPost('state'); // encrypted user ID

        if (!$requestToken || !$state) {
            return $this->response->setJSON(['error' => 'Missing token or user']);
        }

        try {
            $userId = base64_decode($state);
            $userModel = new KiteBrokerModel();
            $user = $userModel->where('user_id', $userId)->first();

            require_once APPPATH . 'Libraries/KiteConnect/KiteConnect.php';

            $kite = new \KiteConnect\KiteConnect($user['api_key']);
            $data = $kite->generateSession($requestToken, $user['api_secret']);

            $userModel->update($user['id'], [
                'access_token' => $data['access_token'],
                'token_generated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->response->setJSON(['success' => true]);
        } catch (\Exception $e) {
            return $this->response->setJSON(['error' => $e->getMessage()]);
        }
    }

    public function kiteCallback()
    {
        $requestToken = $this->request->getGet('request_token');
        $status = $this->request->getGet('status');

        if (!$requestToken || $status !== 'success') {
            return redirect()->to('/invalidRequest')->with('error', 'Login failed or cancelled');
        }

        return view('kite_token_copy', ['requestToken' => $requestToken]);
    }


}