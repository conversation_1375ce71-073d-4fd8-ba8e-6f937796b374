import { <PERSON><PERSON> } from "@/components/ui/button"
import { History, HelpCircle, TrendingUp } from "lucide-react"

export function Header() {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 rounded-xl bg-blue-600 flex items-center justify-center shadow-lg">
          <TrendingUp className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-white mb-1">AI Trade Analysis</h1>
          <p className="text-slate-400 text-lg">Comprehensive trading performance insights</p>
        </div>
      </div>

      <div className="flex space-x-3">
        <Button variant="outline" className="border-slate-600 bg-slate-800 hover:bg-slate-700 text-slate-300">
          <History className="w-4 h-4 mr-2" />
          History
        </Button>
        <Button variant="outline" className="border-slate-600 bg-slate-800 hover:bg-slate-700 text-slate-300">
          <HelpCircle className="w-4 h-4 mr-2" />
          Help
        </Button>
      </div>
    </div>
  )
}
