const toggle = document.getElementById('toggle');
const html = document.documentElement;

// Sync toggle state with current mode
toggle.checked = html.classList.contains('dark');

toggle.addEventListener('change', function () {
    if (this.checked) {
        html.classList.add('dark');
        localStorage.setItem('darkMode', 'true');
    } else {
        html.classList.remove('dark');
        localStorage.setItem('darkMode', 'false');
    }
});

// Mobile menu toggle
const mobileMenuButton = document.getElementById('mobile-menu-button');
const mobileMenu = document.getElementById('mobile-menu');

mobileMenuButton.addEventListener('click', function () {
    mobileMenu.classList.toggle('hidden');
});

// FAQ accordion
const faqToggles = document.querySelectorAll('.faq-toggle');

faqToggles.forEach(toggle => {
    toggle.addEventListener('click', () => {
        const faqItem = toggle.closest('.bg-white, .bg-dark-800');
        const answer = faqItem.querySelector('.faq-answer');
        const icon = toggle.querySelector('i');

        // Toggle active class on answer
        answer.classList.toggle('active');

        // Toggle active class on toggle for icon rotation
        toggle.classList.toggle('active');

        // Close other open FAQs when opening a new one
        if (answer.classList.contains('active')) {
            document.querySelectorAll('.faq-answer').forEach(otherAnswer => {
                if (otherAnswer !== answer && otherAnswer.classList.contains('active')) {
                    otherAnswer.classList.remove('active');
                    const otherToggle = otherAnswer.closest('.bg-white, .bg-dark-800').querySelector('.faq-toggle');
                    otherToggle.classList.remove('active');
                }
            });
        }
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        if (targetId === '#') return;

        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth'
            });

            // Close mobile menu if open
            if (!mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
            }
        }
    });
});

// Onboarding Questionnaire
const onboardingOverlay = document.getElementById('onboarding-overlay');
const closeOnboarding = document.getElementById('close-onboarding');
const startJournalingBtn = document.getElementById('start-journaling-btn');
const startJournalingBtn2 = document.getElementById('start-journaling-btn-2');
const progressBarFill = document.getElementById('progress-bar-fill');
const questions = [
    document.getElementById('question-1'),
    document.getElementById('question-2'),
    document.getElementById('question-3'),
    document.getElementById('question-4'),
    document.getElementById('question-5'),
    document.getElementById('completion-screen')
];

// User responses object
const userResponses = {
    experience: null,
    markets: [],
    style: null,
    goal: null,
    frequency: null
};

// Open onboarding
function openOnboarding() {
    onboardingOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Close onboarding
function closeOnboardingHandler() {
    onboardingOverlay.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// Update progress bar
function updateProgress(currentQuestion) {
    const progress = (currentQuestion / (questions.length - 1)) * 100;
    progressBarFill.style.width = `${progress}%`;
}

// Show question
function showQuestion(index) {
    questions.forEach((question, i) => {
        if (i === index) {
            question.classList.add('active');
        } else {
            question.classList.remove('active');
        }
    });
    updateProgress(index);
}

// Initialize option cards
function initOptionCards() {
    const optionCards = document.querySelectorAll('.option-card');

    optionCards.forEach(card => {
        card.addEventListener('click', function () {
            // For single-select questions, remove selected from siblings
            if (!this.parentElement.classList.contains('multi-select')) {
                const siblings = this.parentElement.querySelectorAll('.option-card');
                siblings.forEach(sibling => {
                    sibling.classList.remove('selected');
                });
            }

            this.classList.toggle('selected');

            // Store the selected value
            const questionContainer = this.closest('.question-container');
            const questionId = questionContainer.id;
            const value = this.getAttribute('data-value');

            if (questionId === 'question-1') {
                userResponses.experience = value;
            } else if (questionId === 'question-2') {
                // For multi-select markets
                if (this.classList.contains('selected')) {
                    if (!userResponses.markets.includes(value)) {
                        userResponses.markets.push(value);
                    }
                } else {
                    userResponses.markets = userResponses.markets.filter(market => market !== value);
                }
            } else if (questionId === 'question-3') {
                userResponses.style = value;
            } else if (questionId === 'question-4') {
                userResponses.goal = value;
            } else if (questionId === 'question-5') {
                userResponses.frequency = value;
            }
        });
    });
}

// Event listeners
startJournalingBtn.addEventListener('click', openOnboarding);
startJournalingBtn2.addEventListener('click', openOnboarding);
closeOnboarding.addEventListener('click', closeOnboardingHandler);

// Navigation between questions
document.getElementById('next-1').addEventListener('click', () => showQuestion(1));
document.getElementById('next-2').addEventListener('click', () => showQuestion(2));
document.getElementById('next-3').addEventListener('click', () => showQuestion(3));
document.getElementById('next-4').addEventListener('click', () => showQuestion(4));

document.getElementById('back-2').addEventListener('click', () => showQuestion(0));
document.getElementById('back-3').addEventListener('click', () => showQuestion(1));
document.getElementById('back-4').addEventListener('click', () => showQuestion(2));
document.getElementById('back-5').addEventListener('click', () => showQuestion(3));

// Complete onboarding
document.getElementById('complete-onboarding').addEventListener('click', () => {
    // Here you would typically send userResponses to your backend
    console.log('User responses:', userResponses);
    showQuestion(5); // Show completion screen
});

// Go to dashboard
document.getElementById('go-to-dashboard').addEventListener('click', () => {
    closeOnboardingHandler();
    // In a real app, you would redirect to the dashboard
    alert('Redirecting to your personalized dashboard...');
});

// Initialize option cards
initOptionCards();





// PAYMENT

function makePayment(postUrl, period, btnId) {
    $(btnId).attr('disabled', true);
    let amount = 299;
    if (period == 'monthly') {
        amount = 299;
    }
    else{
        amount = 999;
    }
    fbq('track', 'Purchase', {currency: "INR", value: amount/100});
    $.ajax({
        type: "POST",
        url: postUrl,
        data: {
            period: period,
        },
        dataType: "JSON",
        success: function (response) {
            $(btnId).attr('disabled', false);
            var order_id = response.order_id;
            var amount = response.amount;
            var period = response.period;

            startPayment(order_id, amount, period);
        },
    });
}

function startPayment(order_id, amount, period) {
    let failedHandled = false;
    var options = {
        key: "rzp_live_JmnggPls2cNwnw",
        amount: amount,
        currency: "INR",
        name: "Trade Diary",
        description: "Subscription",
        image: base_url + "assets/images/logo.png",
        order_id: order_id,
        theme: {
            color: "#1a237e",
        },
        handler: function (sres) {
            saveTransaction('success', sres, amount, period);
        },
    };
    var rzp = new Razorpay(options);
    rzp.open();

    rzp.on("payment.failed", function (res) {
        if (failedHandled) return;
        failedHandled = true;

        saveTransaction('failed', {
            razorpay_payment_id: res.error.metadata.payment_id,
            razorpay_order_id: res.error.metadata.order_id,
            error_description: res.error.description
        }, amount, period);
    });
}

function saveTransaction(status, paymentDetails, amount, period) {
    $.ajax({
        url: base_url + "saveTransaction",
        method: "POST",
        data: {
            status: status, // 'success' or 'failed'
            payment_id: paymentDetails.razorpay_payment_id || null,
            order_id: paymentDetails.razorpay_order_id || null,
            signature: paymentDetails.razorpay_signature || null,
            amount: amount / 100,
            period: period,
            error_reason: paymentDetails.error_description || null,
        },
        success: function (response) {
            console.log("Transaction saved:", response);
            if (status == 'success') {
                window.location.replace(base_url + 'Login')
            }
        },
        error: function (xhr, status, error) {
            console.error("Error saving transaction:", error);
        },
    });
}