<h1><img src="https://img.fortawesome.com/349cfdf6/logo-fa-free.svg" alt="Font Awesome 5 Pro" width="50%"></h1>

> Version 5 – the iconic SVG, font, and CSS framework

The internet's most popular icon toolkit has been redesigned and built from
scratch. On top of this, features like icon font ligatures, an SVG framework,
official NPM packages for popular frontend libraries like React, and access to
a new CDN.

Not familiar with Font Awesome 5? [Learn
more](https://www.kickstarter.com/projects/232193852/font-awesome-5) about our
successful Kickstarter and plan. You can also **[order Font Awesome
Pro](https://fontawesome.com/pro)** which includes tons more icons directly
from [fontawesome.com](https://fontawesome.com).

## Documentation

Learn how to get started with Font Awesome and then dive deeper into other and advanced topics:

### Using Font Awesome on the Web

* [With SVG with JavaScript](https://fontawesome.com/how-to-use/on-the-web/setup/getting-started?using=svg-with-js)
* [With web fonts with CSS](https://fontawesome.com/how-to-use/on-the-web/setup/getting-started?using=web-fonts-with-css)
* [Upgrading from version 4](https://fontawesome.com/how-to-use/on-the-web/setup/upgrading-from-version-4)
* [Installing Font Awesome with a package manager](https://fontawesome.com/how-to-use/on-the-web/setup/using-package-managers)
* [Downloading + hosting Font Awesome yourself](https://fontawesome.com/how-to-use/on-the-web/setup/hosting-font-awesome-yourself)
* [Performance and security](https://fontawesome.com/how-to-use/performance-and-security)
* [Accessibility](https://fontawesome.com/how-to-use/on-the-web/other-topics/accessibility)
* [Troubleshooting](https://fontawesome.com/how-to-use/on-the-web/other-topics/troubleshooting)

#### Advanced Options & Techniques

* [Using CSS pseudo-elements](https://fontawesome.com/how-to-use/on-the-web/advanced/css-pseudo-elements)
* [SVG sprites](https://fontawesome.com/how-to-use/svg-sprites)
* [The Font Awesome API](https://fontawesome.com/how-to-use/font-awesome-api)
* [SVG symbols](https://fontawesome.com/how-to-use/on-the-web/advanced/svg-symbols)
* [SVG JavaScript Core](https://fontawesome.com/how-to-use/on-the-web/advanced/svg-javascript-core)
* [Server side rendering](https://fontawesome.com/how-to-use/server-side-rendering)

### Using Font Awesome on the Desktop

* [Getting started](https://fontawesome.com/how-to-use/on-the-desktop/setup/getting-started)
* [Upgrading from version 4](https://fontawesome.com/how-to-use/on-the-desktop/setup/upgrading-from-version-4)
* [Using ligatures](https://fontawesome.com/how-to-use/on-the-desktop/referencing-icons/using-ligatures)
* [Using glyphs](https://fontawesome.com/how-to-use/on-the-desktop/referencing-icons/using-glyphs)
* [Troubleshooting](https://fontawesome.com/how-to-use/on-the-desktop/other-topics/troubleshooting)

### Where did Font Awesome 4 (or 3) go?

Now that Font Awesome 5 has been released we are marking version 4 as
end-of-life. We don't plan on releasing any further versions of the 4.x or 3.x.

Documentation is still available but it's moved to
[https://fontawesome.com/v4.7.0](https://fontawesome.com/v4.7.0) and
[https://fontawesome.com/v3.2.1](https://fontawesome.com/v3.2.1).

The Git repository for
[v4.7.0](https://github.com/FortAwesome/Font-Awesome/releases/tag/v4.7.0) and
[v3.2.1](https://github.com/FortAwesome/Font-Awesome/releases/tag/v3.2.1) can
be found in our GitHub releases.

## Change log

We'll keep track of each release in the [CHANGELOG.md](./CHANGELOG.md)

Looking for older versions of Font Awesome? Check the [releases](https://github.com/FortAwesome/Font-Awesome/releases).

## Upgrading

From time-to-time we'll have special upgrading instructions from one version to the next.

Check out the [UPGRADING.md](./UPGRADING.md) guide when you upgrade your dependencies.

## Code of conduct

We will behave ourselves if you behave yourselves. For more details see our
[CODE_OF_CONDUCT.md](./CODE_OF_CONDUCT.md).

## Contributing

Please read through our [contributing guidelines](./CONTRIBUTING.md).  Included
are directions for opening issues.

## Versioning

Font Awesome will be maintained under the Semantic Versioning guidelines as much as possible. Releases will be numbered
with the following format:

`<major>.<minor>.<patch>`

For more information on SemVer, please visit http://semver.org.

**The major version "5" is part of an umbrella release.  It includes many different types of files and technologies. Therefore
we deviate from normal SemVer in the following ways:**

* Any release may update the design, look-and-feel, or branding of an existing
  icon
* We will never intentionally release a `patch` version update that breaks
  backward compatibility
* A `minor` release **may include backward-incompatible changes** but we will
  write clear upgrading instructions in UPGRADING.md
* A `minor` or `patch` release will never remove icons
* Bug fixes will be addressed as `patch` releases unless they include backward
  incompatibility then they will be `minor` releases

## License

Font Awesome Free is free, open source, and GPL friendly. You can use it for
commercial projects, open source projects, or really almost whatever you want.

- Icons — CC BY 4.0 License
  - In the Font Awesome Free download, the CC BY 4.0 license applies to all icons packaged as .svg and .js files types.
- Fonts — SIL OFL 1.1 License
  - In the Font Awesome Free download, the SIL OLF license applies to all icons packaged as web and desktop font files.
- Code — MIT License
  - In the Font Awesome Free download, the MIT license applies to all non-font and non-icon files.

Attribution is required by MIT, SIL OLF, and CC BY licenses. Downloaded Font
Awesome Free files already contain embedded comments with sufficient
attribution, so you shouldn't need to do anything additional when using these
files normally.

We've kept attribution comments terse, so we ask that you do not actively work
to remove them from files, especially code. They're a great way for folks to
learn about Font Awesome.

## Team
* [Dave Gandy](https://github.com/davegandy)
* [Travis Chase](https://github.com/supercodepoet)
* [Rob Madole](https://github.com/robmadole)
* [Brian Talbot](https://github.com/talbs)
* [Jory Raphael](https://github.com/sensibleworld)
* [Mike Wilkerson](https://github.com/mlwilkerson)
* [Trevor Chase](https://github.com/trevorchase)
* [Jason Lundien](https://github.com/jasonlundien)
* [Jason Otero](https://github.com/deathnfudge)
* [Edward Emanuel](https://github.com/ej2)
* [Geremia Taglialatela](https://github.com/tagliala)
