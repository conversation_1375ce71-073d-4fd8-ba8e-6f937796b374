<?php

namespace App\Controllers;

helper('cookie');

class LandingController extends BaseController
{
    public function __construct()
    {

    }

    public function index()
    {
        $segments = $this->request->getUri()->getSegments();
        $referCode = '';

        if (count($segments) >= 2) {
            $referCode = $segments[1]; // e.g., 'PARI8480' from /refer/PARI8480
        }

        if (!empty($referCode)) {
            $this->response->setCookie([
                'name' => 'refered_by',
                'value' => $referCode,
                'expire' => 60 * 60 * 24 * 5, // 5 days
                'httponly' => true,
                'path' => '/',              // Make sure it's accessible on all paths
                'secure' => false             // Change to true if using HTTPS
            ]);

        }

        $data['main_content'] = 'landing/home';
        return view('landing/includes/template', $data);
    }



    public function ContactUs()
    {
        $data['main_content'] = 'landing/contact';

        return view('landing/includes/template', $data);
    }

    public function PrivacyPolicy()
    {
        $data['main_content'] = 'landing/privacypolicy';

        return view('landing/includes/template', $data);
    }

    public function AboutUs()
    {
        $data['main_content'] = 'landing/aboutus';

        return view('landing/includes/template', $data);
    }

    public function Disclaimer()
    {
        $data['main_content'] = 'landing/disclaimer';

        return view('landing/includes/template', $data);
    }

    public function Refund()
    {
        $data['main_content'] = 'landing/refund';

        return view('landing/includes/template', $data);
    }

    public function Disclosures()
    {
        $data['main_content'] = 'landing/disclosures';

        return view('landing/includes/template', $data);
    }

    public function Terms()
    {
        $data['main_content'] = 'landing/terms';

        return view('landing/includes/template', $data);
    }

    public function Shipping()
    {
        $data['main_content'] = 'landing/shipping';

        return view('landing/includes/template', $data);
    }
}