# Security settings for uploads directory
# Prevent execution of PHP files and other scripts

# Deny access to PHP files
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Deny access to other script files
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Allow only image files
<FilesMatch "\.(jpg|jpeg|png|gif|webp|bmp|ico|svg)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Prevent access to hidden files
<Files ".*">
    Order Deny,Allow
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes

# Set proper MIME types for images
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType image/bmp .bmp
    AddType image/x-icon .ico
    AddType image/svg+xml .svg
</IfModule>

# Set cache headers for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# Compress images if possible
<IfModule mod_deflate.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>
