<?php

namespace App\Libraries;

/**
 * Angel One SmartAPI PHP Client
 * 
 * This class provides integration with Angel One SmartAPI for trading operations
 * Based on Angel One SmartAPI documentation
 */
class AngelOneApi
{
    private $baseUrl = 'https://apiconnect.angelone.in';
    private $apiKey;
    private $clientCode;
    private $mpin;
    private $totp;
    private $accessToken;
    private $refreshToken;
    private $feedToken;
    private $userId;

    public function __construct($apiKey = null, $clientCode = null, $mpin = null, $totp = null)
    {
        $this->apiKey = $apiKey;
        $this->clientCode = $clientCode;
        $this->mpin = $mpin;
        $this->totp = $totp;
    }

    /**
     * Generate session and get access token
     */
    public function generateSession()
    {
        $url = $this->baseUrl . '/rest/auth/angelbroking/user/v1/loginByPassword';

        $data = [
            'clientcode' => $this->clientCode,
            'password' => $this->mpin,
            'totp' => $this->totp
        ];

        $headers = [
            'X-PrivateKey: ' . $this->apiKey,
            'Content-Type: application/json',
            'Accept: application/json',
            'X-UserType: USER',
            'X-SourceID: WEB',
            'User-Agent: TradeDiary/1.0',
            'X-ClientLocalIP: 127.0.0.1',
            'X-ClientPublicIP: 127.0.0.1',
            'X-MACAddress: 00:00:00:00:00:00'
        ];

        $response = $this->makeRequest('POST', $url, $data, $headers);

        if ($response && is_array($response) && isset($response['data']) && is_array($response['data'])) {
            $data = $response['data'];

            if (isset($data['jwtToken']) && isset($data['refreshToken']) && isset($data['feedToken'])) {
                $this->accessToken = $data['jwtToken'];
                $this->refreshToken = $data['refreshToken'];
                $this->feedToken = $data['feedToken'];
                $this->userId = $data['clientcode'] ?? $this->clientCode;

                return [
                    'success' => true,
                    'data' => $response['data']
                ];
            }
        }

        return [
            'success' => false,
            'message' => is_array($response) ? ($response['message'] ?? 'Login failed') : 'Invalid response from server'
        ];
    }

    /**
     * Get user profile
     */
    public function getProfile()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/user/v1/getProfile';
        return $this->makeAuthenticatedRequest('GET', $url);
    }

    /**
     * Get trade book
     */
    public function getTradeBook()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/getTradeBook';
        return $this->makeAuthenticatedRequest('GET', $url);
    }

    /**
     * Get order book
     */
    public function getOrderBook()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/getOrderBook';
        return $this->makeAuthenticatedRequest('GET', $url);
    }

    /**
     * Get holdings
     */
    public function getHoldings()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/portfolio/v1/getHolding';
        return $this->makeAuthenticatedRequest('GET', $url);
    }

    /**
     * Get positions
     */
    public function getPositions()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/getPosition';
        return $this->makeAuthenticatedRequest('GET', $url);
    }

    /**
     * Place order
     */
    public function placeOrder($orderData)
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/placeOrder';
        return $this->makeAuthenticatedRequest('POST', $url, $orderData);
    }

    /**
     * Modify order
     */
    public function modifyOrder($orderData)
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/modifyOrder';
        return $this->makeAuthenticatedRequest('POST', $url, $orderData);
    }

    /**
     * Cancel order
     */
    public function cancelOrder($orderData)
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/order/v1/cancelOrder';
        return $this->makeAuthenticatedRequest('POST', $url, $orderData);
    }

    /**
     * Get historical data
     */
    public function getHistoricalData($params)
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/historical/v1/getCandleData';
        return $this->makeAuthenticatedRequest('POST', $url, $params);
    }

    /**
     * Logout
     */
    public function logout()
    {
        $url = $this->baseUrl . '/rest/secure/angelbroking/user/v1/logout';
        $data = ['clientcode' => $this->clientCode];
        return $this->makeAuthenticatedRequest('POST', $url, $data);
    }

    /**
     * Make authenticated request
     */
    private function makeAuthenticatedRequest($method, $url, $data = null)
    {
        $headers = [
            'Authorization: Bearer ' . $this->accessToken,
            'Content-Type: application/json',
            'Accept: application/json',
            'X-UserType: USER',
            'X-SourceID: WEB',
            'X-ClientLocalIP: ***********',
            'X-ClientPublicIP: ***********',
            'X-MACAddress: 00:00:00:00:00:00',
            'X-PrivateKey: ' . $this->apiKey
        ];

        return $this->makeRequest($method, $url, $data, $headers);
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = null, $headers = [])
    {
        $ch = curl_init();
        
        // Default headers
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'TradeDiary/1.0',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return ['success' => false, 'message' => 'Network error: ' . $error];
        }

        // Log raw response for debugging (only if log_message function exists)
        if (function_exists('log_message')) {
            log_message('debug', 'Angel One API Raw Response: ' . substr($response, 0, 500));
            log_message('debug', 'Angel One API HTTP Code: ' . $httpCode);
            log_message('debug', 'Angel One API URL: ' . $url);
        }

        $decodedResponse = json_decode($response, true);

        // If JSON decode failed, try to extract meaningful error from HTML response
        if (json_last_error() !== JSON_ERROR_NONE) {
            $errorMessage = 'Invalid response from Angel One API';

            // Check if it's an HTML error page
            if (strpos($response, '<html') !== false || strpos($response, '<!DOCTYPE') !== false) {
                if (preg_match('/<title>(.*?)<\/title>/i', $response, $matches)) {
                    $errorMessage = 'Server error: ' . trim($matches[1]);
                } else {
                    $errorMessage = 'Server returned HTML instead of JSON (HTTP ' . $httpCode . ')';
                }
            } else {
                // Try to extract error from plain text response
                $cleanResponse = strip_tags($response);
                if (strlen($cleanResponse) > 0 && strlen($cleanResponse) < 200) {
                    $errorMessage = 'API Error: ' . $cleanResponse;
                } else {
                    $errorMessage = 'Invalid JSON response: ' . json_last_error_msg();
                }
            }

            return [
                'success' => false,
                'message' => $errorMessage,
                'http_code' => $httpCode,
                'raw_response' => substr($response, 0, 300)
            ];
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            return $decodedResponse;
        } else {
            $errorMessage = 'API request failed';

            if (is_array($decodedResponse)) {
                if (isset($decodedResponse['message'])) {
                    $errorMessage = $decodedResponse['message'];
                } elseif (isset($decodedResponse['errorMessage'])) {
                    $errorMessage = $decodedResponse['errorMessage'];
                } elseif (isset($decodedResponse['error'])) {
                    $errorMessage = $decodedResponse['error'];
                }
            }

            return [
                'success' => false,
                'message' => $errorMessage,
                'errorcode' => is_array($decodedResponse) ? ($decodedResponse['errorcode'] ?? null) : null,
                'http_code' => $httpCode
            ];
        }
    }

    // Getters
    public function getAccessToken() { return $this->accessToken; }
    public function getRefreshToken() { return $this->refreshToken; }
    public function getFeedToken() { return $this->feedToken; }
    public function getUserId() { return $this->userId; }

    /**
     * Test basic connectivity to Angel One servers
     */
    private function testConnectivity()
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'TradeDiary/1.0',
            CURLOPT_NOBODY => true  // HEAD request only
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'message' => 'Cannot connect to Angel One servers: ' . $error
            ];
        }

        if ($httpCode >= 200 && $httpCode < 400) {
            return ['success' => true];
        }

        return [
            'success' => false,
            'message' => 'Angel One servers returned HTTP ' . $httpCode
        ];
    }

    // Setters
    public function setAccessToken($token) { $this->accessToken = $token; }
    public function setClientCode($clientCode) { $this->clientCode = $clientCode; }
    public function setApiKey($apiKey) { $this->apiKey = $apiKey; }
}
