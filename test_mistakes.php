<?php
// Simple test to verify Mistakes functionality
echo "<!DOCTYPE html>";
echo "<html><head><title>Mistakes Test</title></head><body>";
echo "<h1>Mistakes System Test</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=diary", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT name, category, severity, impact FROM mistakes WHERE is_active = 1 LIMIT 5");
    $mistakes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Active Mistakes in Database:</h2>";
    echo "<ul>";
    foreach ($mistakes as $mistake) {
        echo "<li><strong>" . htmlspecialchars($mistake["name"]) . "</strong> - ";
        echo htmlspecialchars($mistake["category"]) . " (" . htmlspecialchars($mistake["severity"]) . ", " . htmlspecialchars($mistake["impact"]) . ")</li>";
    }
    echo "</ul>";
    
    echo "<p><a href=\"/mainproject/index.php/Mistakes\">Try accessing Mistakes Dashboard</a></p>";
    echo "<p><a href=\"/mainproject/public/index.php/Mistakes\">Try accessing via public folder</a></p>";
    
} catch (Exception $e) {
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>