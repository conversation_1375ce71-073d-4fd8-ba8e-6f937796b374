$(document).ready(function() {
    // Configuration
    const ADMIN_BASE_URL = window.ADMIN_BASE_URL || (window.location.origin + '/admin');

    // Debug logging
    console.log('Support.js loaded successfully');
    console.log('ADMIN_BASE_URL:', ADMIN_BASE_URL);

    // Debug logging
    console.log('Support.js loaded');
    console.log('ADMIN_BASE_URL:', ADMIN_BASE_URL);
    
    // State variables
    let currentPage = 1;
    let currentFilters = {};
    let isLoading = false;
    let currentModalMessage = null;

    // Initialize page
    loadSupportMessages();

    // Event handlers
    $('#refreshData').on('click', function() {
        currentPage = 1;
        loadSupportMessages();
    });

    $('#markAllRead').on('click', markAllAsRead);
    $('#clearFilters').on('click', clearFilters);
    $('#closeModal').on('click', closeMessageModal);

    // Filter handlers with debouncing
    $('#searchFilter').on('input', debounce(applyFilters, 500));
    $('#statusFilter, #subjectFilter, #dateFromFilter, #dateToFilter').on('change', applyFilters);

    // Modal handlers
    $(document).on('click', function(e) {
        if (e.target.id === 'messageModal') {
            closeMessageModal();
        }
    });

    function loadSupportMessages() {
        if (isLoading) return;
        
        isLoading = true;
        showLoading();

        const params = new URLSearchParams({
            page: currentPage,
            ...currentFilters
        });

        $.ajax({
            url: `${ADMIN_BASE_URL}/get-support-messages?${params}`,
            method: 'GET',
            success: function(response) {
                console.log('AJAX Success:', response);
                hideLoading();
                if (response.success) {
                    renderMessagesTable(response.data);
                    renderPagination(response.pagination);
                    updateRecordCount(response.pagination.total_records);
                    updateStatsCards(response.stats);
                } else {
                    console.error('Response error:', response.message);
                    showError('Failed to load support messages: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                hideLoading();
                console.error('AJAX Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
                showError('Error loading support messages: ' + error);
            },
            complete: function() {
                isLoading = false;
            }
        });
    }

    function renderMessagesTable(messages) {
        const tbody = $('#messagesTableBody');
        tbody.empty();

        if (messages.length === 0) {
            $('#emptyState').removeClass('hidden');
            $('#paginationContainer').addClass('hidden');
            return;
        }

        $('#emptyState').addClass('hidden');
        $('#paginationContainer').removeClass('hidden');

        messages.forEach(message => {
            const statusBadge = getStatusBadge(message.status);
            const subjectBadge = getSubjectBadge(message.subject);
            const messageDate = formatDate(message.created_at);
            const truncatedMessage = truncateText(message.message, 50);

            const row = `
                <tr class="message-row hover:bg-gray-50 transition-all duration-200">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            ${escapeHtml(message.name)}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            ${escapeHtml(message.email)}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${subjectBadge}
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 max-w-xs">
                            ${escapeHtml(truncatedMessage)}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${statusBadge}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            ${messageDate}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-3">
                            <button onclick="viewMessageDetails(${message.id})"
                                    class="inline-flex items-center justify-center w-8 h-8 text-indigo-600 hover:text-white hover:bg-indigo-600 rounded-lg transition-all duration-200 hover:shadow-md"
                                    title="View Details">
                                <i class="fas fa-eye text-sm"></i>
                            </button>
                            ${message.status === 'pending' ? `
                                <button onclick="markAsRead(${message.id})"
                                        class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 rounded-lg transition-all duration-200 hover:shadow-md"
                                        title="Mark as Read">
                                    <i class="fas fa-check text-sm"></i>
                                </button>
                            ` : ''}
                            <button onclick="replyToMessage(${message.id})"
                                    class="inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-white hover:bg-blue-600 rounded-lg transition-all duration-200 hover:shadow-md"
                                    title="Send Reply Email">
                                <i class="fas fa-reply text-sm"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function getStatusBadge(status) {
        const badges = {
            'pending': '<span class="status-badge status-new inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-star badge-icon mr-1"></i>New</span>',
            'read': '<span class="status-badge status-read inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-eye badge-icon mr-1"></i>Read</span>',
            'replied': '<span class="status-badge status-replied inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-reply badge-icon mr-1"></i>Replied</span>',
            'closed': '<span class="status-badge status-closed inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-check-circle badge-icon mr-1"></i>Closed</span>'
        };
        return badges[status] || '<span class="status-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 border border-gray-200"><i class="fas fa-question badge-icon mr-1"></i>Unknown</span>';
    }

    function getSubjectBadge(subject) {
        const badges = {
            'support': '<span class="status-badge subject-support inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-headset badge-icon mr-1"></i>Support</span>',
            'feedback': '<span class="status-badge subject-feedback inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-comment-dots badge-icon mr-1"></i>Feedback</span>',
            'partnership': '<span class="status-badge subject-partnership inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-handshake badge-icon mr-1"></i>Partnership</span>',
            'other': '<span class="status-badge subject-other inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border"><i class="fas fa-question-circle badge-icon mr-1"></i>Other</span>'
        };
        return badges[subject] || '<span class="status-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 border border-gray-200"><i class="fas fa-envelope badge-icon mr-1"></i>General</span>';
    }

    function updateStatsCards(stats) {
        // Update numbers with animation
        animateNumber('#totalMessages', stats.total_messages);
        animateNumber('#newMessages', stats.new_messages);
        animateNumber('#readMessages', stats.read_messages);
        animateNumber('#repliedMessages', stats.replied_messages);

        // Update status filter count
        $('#statusFilter option[value=""]').text(`All Status (${stats.total_messages})`);

        // Add visual indicators for high priority items (removed ring effect)
        // Visual emphasis is now handled by the pulse animation in CSS
    }

    function animateNumber(selector, targetNumber) {
        const element = $(selector);
        const currentNumber = parseInt(element.text().replace(/,/g, '')) || 0;

        if (currentNumber === targetNumber) return;

        const increment = targetNumber > currentNumber ? 1 : -1;
        const duration = Math.min(500, Math.abs(targetNumber - currentNumber) * 50);
        const steps = Math.abs(targetNumber - currentNumber);
        const stepDuration = duration / steps;

        let current = currentNumber;
        const timer = setInterval(() => {
            current += increment;
            element.html(current.toLocaleString());

            if (current === targetNumber) {
                clearInterval(timer);
            }
        }, stepDuration);
    }

    function renderPagination(pagination) {
        const container = $('#paginationButtons');
        container.empty();

        if (pagination.total_pages <= 1) {
            $('#paginationContainer').addClass('hidden');
            return;
        }

        $('#paginationContainer').removeClass('hidden');

        // Previous button
        if (pagination.current_page > 1) {
            container.append(`
                <button onclick="changePage(${pagination.current_page - 1})" 
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Previous
                </button>
            `);
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.current_page;
            container.append(`
                <button onclick="changePage(${i})" 
                        class="px-3 py-2 text-sm font-medium ${isActive ? 'text-indigo-600 bg-indigo-50 border-indigo-500' : 'text-gray-500 bg-white border-gray-300'} border rounded-md hover:bg-gray-50">
                    ${i}
                </button>
            `);
        }

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            container.append(`
                <button onclick="changePage(${pagination.current_page + 1})" 
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Next
                </button>
            `);
        }

        // Update pagination info
        const start = ((pagination.current_page - 1) * pagination.per_page) + 1;
        const end = Math.min(pagination.current_page * pagination.per_page, pagination.total_records);
        $('#paginationInfo').text(`${start}-${end} of ${pagination.total_records}`);
    }

    function updateRecordCount(count) {
        $('#recordCount').text(count.toLocaleString());
    }

    function applyFilters() {
        currentFilters = {
            search: $('#searchFilter').val().trim(),
            status: $('#statusFilter').val(),
            subject: $('#subjectFilter').val(),
            date_from: $('#dateFromFilter').val(),
            date_to: $('#dateToFilter').val()
        };

        // Remove empty filters
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });

        currentPage = 1;
        loadSupportMessages();
    }

    function clearFilters() {
        $('#searchFilter').val('');
        $('#statusFilter').val('');
        $('#subjectFilter').val('');
        $('#dateFromFilter').val('');
        $('#dateToFilter').val('');
        
        currentFilters = {};
        currentPage = 1;
        loadSupportMessages();
    }

    // Global functions for onclick handlers
    window.changePage = function(page) {
        currentPage = page;
        loadSupportMessages();
    };

    window.viewMessageDetails = function(messageId) {
        // Implementation for viewing message details
        showMessageModal(messageId);
    };

    window.markAsRead = function(messageId) {
        updateMessageStatus(messageId, 'read');
    };

    window.replyToMessage = function(messageId) {
        // Get message details first to populate the reply modal
        $.ajax({
            url: `${ADMIN_BASE_URL}/get-message-details/${messageId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const message = response.data;
                    showReplyModal(message);
                } else {
                    showNotification('error', 'Failed to load message details for reply');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading message details for reply:', error);
                showNotification('error', 'Error loading message details for reply');
            }
        });
    };

    function showLoading() {
        $('#loadingState').removeClass('hidden');
        $('#emptyState').addClass('hidden');
    }

    function hideLoading() {
        $('#loadingState').addClass('hidden');
    }

    function showError(message) {
        console.error(message);
        // You can implement a toast notification here
    }

    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    function updateMessageStatus(messageId, status) {
        $.ajax({
            url: `${ADMIN_BASE_URL}/update-message-status`,
            method: 'POST',
            data: {
                message_id: messageId,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    loadSupportMessages(); // Refresh the table
                    showNotification('success', response.message);
                } else {
                    showNotification('error', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating message status:', error);
                showNotification('error', 'Error updating message status');
            }
        });
    }

    function markAllAsRead() {
        if (!confirm('Are you sure you want to mark all messages as read?')) {
            return;
        }

        // This would require a separate endpoint to mark all as read
        console.log('Mark all as read functionality would be implemented here');
    }

    function showMessageModal(messageId) {
        // Load message details and show in modal
        $.ajax({
            url: `${ADMIN_BASE_URL}/get-message-details/${messageId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    renderMessageModal(response.data);
                    $('#messageModal').removeClass('hidden');
                } else {
                    showNotification('error', 'Failed to load message details');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading message details:', error);
                showNotification('error', 'Error loading message details');
            }
        });
    }

    function renderMessageModal(message) {
        // Store current message for modal actions
        currentModalMessage = message;

        const modalContent = $('#modalContent');
        const statusBadge = getStatusBadge(message.status);
        const subjectBadge = getSubjectBadge(message.subject);
        const messageDate = formatDate(message.created_at);

        modalContent.html(`
            <div class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="mt-1 text-sm text-gray-900">${escapeHtml(message.name)}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900">${escapeHtml(message.email)}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Subject</label>
                        <div class="mt-1">${subjectBadge}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <div class="mt-1">${statusBadge}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <p class="mt-1 text-sm text-gray-900">${messageDate}</p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Message</label>
                    <div class="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-900 whitespace-pre-wrap">${escapeHtml(message.message)}</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    ${message.status === 'pending' ? `
                        <button onclick="markAsRead(${message.id}); closeMessageModal();"
                                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                            Mark as Read
                        </button>
                    ` : ''}
                    <button onclick="replyFromModal(${message.id})"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-reply mr-2"></i>Send Reply
                    </button>
                </div>
            </div>
        `);
    }

    function closeMessageModal() {
        $('#messageModal').addClass('hidden');
        currentModalMessage = null;
    }

    // Global function for modal reply button
    window.replyFromModal = function(messageId) {
        if (currentModalMessage && currentModalMessage.id == messageId) {
            showReplyModal(currentModalMessage);
            closeMessageModal();
        } else {
            // Fallback to regular reply function
            replyToMessage(messageId);
        }
    };

    function showReplyModal(message) {
        // Store current message for reply
        currentModalMessage = message;

        // Create reply modal HTML
        const replyModalHtml = `
            <div id="replyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Reply to ${escapeHtml(message.name)}</h3>
                            <button id="closeReplyModal" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Original Message Info -->
                        <div class="bg-gray-50 p-4 rounded-lg mb-6">
                            <h4 class="font-medium text-gray-900 mb-2">Original Message:</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">From:</span>
                                    <span class="text-gray-900">${escapeHtml(message.name)} (${escapeHtml(message.email)})</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Subject:</span>
                                    <span class="text-gray-900">${getSubjectDisplayName(message.subject)}</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <span class="font-medium text-gray-700">Message:</span>
                                <p class="text-gray-900 mt-1">"${escapeHtml(message.message)}"</p>
                            </div>
                        </div>

                        <!-- Reply Form -->
                        <form id="replyForm">
                            <div class="mb-4">
                                <label for="replyMessage" class="block text-sm font-medium text-gray-700 mb-2">Your Reply:</label>
                                <textarea id="replyMessage" name="reply_message" rows="8"
                                         class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                         placeholder="Type your reply here...">${getDefaultReplyTemplate(message)}</textarea>
                            </div>

                            <div class="flex justify-end space-x-3">
                                <button type="button" id="cancelReply"
                                        class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                                    Cancel
                                </button>
                                <button type="submit" id="sendReply"
                                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i class="fas fa-paper-plane mr-2"></i>Send Reply
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        $('body').append(replyModalHtml);

        // Bind events
        $('#closeReplyModal, #cancelReply').on('click', closeReplyModal);
        $('#replyForm').on('submit', handleReplySubmit);

        // Close modal when clicking outside
        $('#replyModal').on('click', function(e) {
            if (e.target.id === 'replyModal') {
                closeReplyModal();
            }
        });
    }

    function getSubjectDisplayName(subject) {
        const subjects = {
            'support': 'Technical Support',
            'feedback': 'Feedback',
            'partnership': 'Partnership Inquiry',
            'other': 'General Inquiry'
        };
        return subjects[subject] || 'General Inquiry';
    }

    function getDefaultReplyTemplate(message) {
        const templates = {
            'support': `Dear ${message.name},

Thank you for contacting Trade Diary support. We have reviewed your technical support request and are pleased to provide you with assistance.

[Please provide your specific response here]

If you need any further assistance, please don't hesitate to contact us.

Best regards,
Trade Diary Support Team`,

            'feedback': `Dear ${message.name},

Thank you for taking the time to share your valuable feedback with Trade Diary. We truly appreciate your input as it helps us improve our platform.

[Please provide your specific response here]

We're committed to making Trade Diary the best trading platform possible, and your feedback is invaluable to us.

Best regards,
Trade Diary Team`,

            'partnership': `Dear ${message.name},

Thank you for your interest in partnering with Trade Diary. We're excited about the potential collaboration opportunities.

[Please provide your specific response here]

We look forward to discussing this partnership opportunity further.

Best regards,
Trade Diary Business Development Team`,

            'other': `Dear ${message.name},

Thank you for contacting Trade Diary. We have received your inquiry and are pleased to respond.

[Please provide your specific response here]

If you have any further questions, please don't hesitate to reach out.

Best regards,
Trade Diary Team`
        };

        return templates[message.subject] || templates['other'];
    }

    function closeReplyModal() {
        $('#replyModal').remove();
        currentModalMessage = null;
    }

    function handleReplySubmit(e) {
        e.preventDefault();

        if (!currentModalMessage) {
            showNotification('error', 'No message selected for reply');
            return;
        }

        const replyMessage = $('#replyMessage').val().trim();

        if (!replyMessage) {
            showNotification('error', 'Please enter a reply message');
            return;
        }

        // Disable form while sending
        $('#sendReply').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');

        // Send reply via AJAX
        $.ajax({
            url: `${ADMIN_BASE_URL}/send-reply`,
            method: 'POST',
            data: {
                message_id: currentModalMessage.id,
                reply_message: replyMessage
            },
            success: function(response) {
                if (response.success) {
                    showNotification('success', response.message);
                    closeReplyModal();
                    loadSupportMessages(); // Refresh the table
                } else {
                    showNotification('error', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error sending reply:', error);
                showNotification('error', 'Error sending reply');
            },
            complete: function() {
                $('#sendReply').prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Send Reply');
            }
        });
    }

    function showNotification(type, message) {
        // Create notification element
        const notification = $(`
            <div class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas ${type === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400'} text-xl"></i>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-gray-900">${message}</p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none" onclick="$(this).closest('.fixed').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // Add to page
        $('body').append(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 5000);
    }
});
