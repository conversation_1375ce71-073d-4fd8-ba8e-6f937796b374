<iframe id="kiteFrame" src="<?= $loginUrl; ?>"
    width="100%" height="100%" style="border: none;"></iframe>


<script>
    // setInterval(() => {
    //     try {
    //         const url = document.getElementById("kiteFrame").contentWindow.location.href;

    //         if (url.includes("request_token=")) {
    //             const token = new URL(url).searchParams.get("request_token");
    //             const state = new URL(url).searchParams.get("state");

    //             // Pass both token and encrypted user ID
    //             window.parent.postMessage({ token, state }, "*");
    //         }
    //     } catch (e) { }
    // }, 1000);
</script>