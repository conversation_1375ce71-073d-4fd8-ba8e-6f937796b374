<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mistake Analysis Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            800: '#1a202c',
                            900: '#121826',
                        },
                        primary: {
                            500: '#6366f1',
                            600: '#4f46e5',
                        },
                        danger: {
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Reset any default browser styling that might cause unwanted borders */
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            border: none;
            outline: none;
        }

        /* Ensure no unwanted borders on main containers */
        .min-h-screen {
            border: none;
            outline: none;
        }

        header, main, div {
            border-left: none !important;
            border-right: none !important;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        }
        .mistake-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .dark .mistake-card:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 30px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #6366f1;
        }
        input:checked + .slider:before {
            transform: translateX(30px);
        }
        .modal {
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .modal-content {
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        .modal.active .modal-content {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
        .chart-container {
            height: 300px;
            width: 100%;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        .dark .glass-effect {
            background: rgba(15, 23, 42, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .metric-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }
        .progress-thin {
            height: 4px;
        }
        .close-btn {
            transition: all 0.2s ease;
        }
        .close-btn:hover {
            transform: rotate(90deg);
        }
        .mistake-item {
            transition: all 0.2s ease;
        }
        .mistake-item:hover {
            background-color: rgba(99, 102, 241, 0.1);
        }
        .dark .mistake-item:hover {
            background-color: rgba(99, 102, 241, 0.2);
        }
        .mistake-enter-active, .mistake-leave-active {
            transition: all 0.3s ease;
        }
        .mistake-enter-from, .mistake-leave-to {
            opacity: 0;
            transform: translateX(30px);
        }
        .edit-btn {
            transition: all 0.2s ease;
        }
        .edit-btn:hover {
            transform: scale(1.05);
        }
        .mistake-tag {
            transition: all 0.2s ease;
        }
        .mistake-tag:hover {
            transform: scale(1.05);
        }
        .heatmap-day {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            margin: 0;
            transition: all 0.2s ease;
        }
        .heatmap-day:hover {
            transform: scale(1.3);
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-5px); }
            40%, 80% { transform: translateX(5px); }
        }
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        .bar-chart {
            display: flex;
            align-items: flex-end;
            height: 250px;
            padding-top: 20px;
        }
        .bar-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }
        .bar {
            width: 80%;
            border-radius: 4px 4px 0 0;
            transition: height 0.5s ease;
            position: relative;
        }
        .bar-label {
            margin-top: 8px;
            font-size: 0.75rem;
            text-align: center;
            color: #6b7280;
        }
        .dark .bar-label {
            color: #9ca3af;
        }
        .y-axis {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 30px;
            border-right: 1px solid #e5e7eb;
        }
        .dark .y-axis {
            border-right-color: #4b5563;
        }
        .y-tick {
            position: absolute;
            width: 100%;
            text-align: right;
            padding-right: 5px;
            font-size: 0.7rem;
            color: #6b7280;
            transform: translateY(50%);
        }
        .dark .y-tick {
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-dark-900 transition-colors duration-200">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="container mx-auto px-4 py-6">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-bug text-2xl"></i>
                        <h1 class="text-2xl font-bold">Mistake Analysis Dashboard</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="hidden md:inline">Dark Mode</span>
                        <label class="switch">
                            <input type="checkbox" id="darkModeToggle" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                <p class="mt-2 opacity-90">Track, analyze and learn from your mistakes</p>
            </div>
        </header>

        <main class="container mx-auto px-4 py-8">
            <!-- Overview Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Total Mistakes -->
                <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Total Mistakes</p>
                            <h3 class="text-3xl font-bold text-gray-800 dark:text-white mt-1">247</h3>
                        </div>
                        <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-300">
                            <i class="fas fa-times-circle text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">This week</span>
                            <span class="font-medium text-red-500">+12%</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                            <div class="bg-red-500 h-2 rounded-full" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <!-- Most Common Mistake -->
                <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Most Common</p>
                            <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">Overconfidence</h3>
                        </div>
                        <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-300">
                            <i class="fas fa-exclamation-triangle text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">Occurrences</span>
                            <span class="font-medium text-gray-800 dark:text-gray-200">43</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                </div>

                <!-- Improvement Rate -->
                <div class="metric-card bg-white dark:bg-dark-800 rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Improvement Rate</p>
                            <h3 class="text-3xl font-bold text-gray-800 dark:text-white mt-1">28%</h3>
                        </div>
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-300">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">Last month</span>
                            <span class="font-medium text-green-500">+8%</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 28%"></div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Mistake Categories -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Mistake Distribution -->
                <div class="bg-white dark:bg-dark-800 rounded-lg shadow p-6 lg:col-span-2">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Mistake Distribution</h2>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs bg-primary-500 text-white rounded-full">This Month</button>
                            <button class="px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">All Time</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="bar-chart">
                            <div class="y-axis">
                            </div>
                            <div class="flex-1 flex items-end pl-8">
                                <div class="bar-container">
                                    <div class="bar bg-red-400 dark:bg-red-500" style="height: 80%;" title="80 mistakes"></div>
                                    <div class="bar-label">Overconfidence</div>
                                </div>
                                <div class="bar-container">
                                    <div class="bar bg-yellow-400 dark:bg-yellow-500" style="height: 65%;" title="65 mistakes"></div>
                                    <div class="bar-label">Rushing</div>
                                </div>
                                <div class="bar-container">
                                    <div class="bar bg-blue-400 dark:bg-blue-500" style="height: 50%;" title="50 mistakes"></div>
                                    <div class="bar-label">Distraction</div>
                                </div>
                                <div class="bar-container">
                                    <div class="bar bg-purple-400 dark:bg-purple-500" style="height: 40%;" title="40 mistakes"></div>
                                    <div class="bar-label">Fatigue</div>
                                </div>
                                <div class="bar-container">
                                    <div class="bar bg-green-400 dark:bg-green-500" style="height: 30%;" title="30 mistakes"></div>
                                    <div class="bar-label">Knowledge Gap</div>
                                </div>
                                <div class="bar-container">
                                    <div class="bar bg-indigo-400 dark:bg-indigo-500" style="height: 25%;" title="25 mistakes"></div>
                                    <div class="bar-label">Others</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mistake Heatmap -->
                <div class="bg-white dark:bg-dark-800 rounded-lg shadow p-4">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">Mistake Frequency Heatmap</h2>
                    <div class="flex flex-col items-center space-y-4">
                        <div class="flex justify-center items-center gap-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Less</span>
                            <div class="heatmap-day bg-gray-200 dark:bg-gray-700"></div>
                            <div class="heatmap-day bg-blue-200 dark:bg-blue-900"></div>
                            <div class="heatmap-day bg-blue-400 dark:bg-blue-700"></div>
                            <div class="heatmap-day bg-blue-600 dark:bg-blue-500"></div>
                            <div class="heatmap-day bg-blue-800 dark:bg-blue-400"></div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">More</span>
                        </div>
                        <div class="w-full flex justify-center">
                            <div class="grid grid-cols-7 gap-2">
                                <!-- Weekday labels -->
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">M</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">T</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">W</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">T</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">F</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">S</div>
                                <div class="text-center text-xs text-gray-500 dark:text-gray-400 h-6 flex items-center justify-center font-medium">S</div>
                                
                                <!-- Heatmap cells (sample data) -->
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Monday week 1"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Tuesday week 1"></div>
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Wednesday week 1"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Thursday week 1"></div>
                                <div class="heatmap-day bg-blue-800 dark:bg-blue-400" title="4 mistakes on Friday week 1"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Saturday week 1"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Sunday week 1"></div>
                                
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Monday week 2"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Tuesday week 2"></div>
                                <div class="heatmap-day bg-blue-800 dark:bg-blue-400" title="4 mistakes on Wednesday week 2"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Thursday week 2"></div>
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Friday week 2"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Saturday week 2"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Sunday week 2"></div>
                                
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Monday week 3"></div>
                                <div class="heatmap-day bg-blue-800 dark:bg-blue-400" title="4 mistakes on Tuesday week 3"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Wednesday week 3"></div>
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Thursday week 3"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Friday week 3"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Saturday week 3"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Sunday week 3"></div>
                                
                                <div class="heatmap-day bg-blue-800 dark:bg-blue-400" title="4 mistakes on Monday week 4"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Tuesday week 4"></div>
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Wednesday week 4"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Thursday week 4"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Friday week 4"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Saturday week 4"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Sunday week 4"></div>
                                
                                <div class="heatmap-day bg-blue-400 dark:bg-blue-700" title="2 mistakes on Monday week 5"></div>
                                <div class="heatmap-day bg-blue-600 dark:bg-blue-500" title="3 mistakes on Tuesday week 5"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Wednesday week 5"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Thursday week 5"></div>
                                <div class="heatmap-day bg-blue-200 dark:bg-blue-900" title="1 mistake on Friday week 5"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Saturday week 5"></div>
                                <div class="heatmap-day bg-gray-200 dark:bg-gray-700" title="0 mistakes on Sunday week 5"></div>
                            </div>
                        </div>
                        <div class="flex text-xs text-gray-500 dark:text-gray-400 justify-center space-x-6 font-medium">
                            <span>Week 1</span>
                            <span>Week 2</span>
                            <span>Week 3</span>
                            <span>Week 4</span>
                            <span>Week 5</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Mistakes -->
            <div class="bg-white dark:bg-dark-800 rounded-lg shadow overflow-hidden mb-8">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Recent Mistakes</h2>
                    <button class="px-4 py-2 bg-primary-500 text-white text-sm rounded-md hover:bg-primary-600 transition">
                        <i class="fas fa-plus mr-2"></i> Add Mistake
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Mistake Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Severity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Impact</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Count</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <tr class="mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer" data-mistake-id="overconfidence">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
                                            <i class="fas fa-brain text-red-600 dark:text-red-300"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Overconfidence</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-gray-100">Cognitive</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">High</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200">Critical</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">43</div>
                                </td>
                            </tr>
                            <tr class="mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer" data-mistake-id="rushing">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-yellow-100 dark:bg-yellow-900/50 flex items-center justify-center">
                                            <i class="fas fa-running text-yellow-600 dark:text-yellow-300"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Rushing</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-gray-100">Behavioral</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">Medium</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">Moderate</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">32</div>
                                </td>
                            </tr>
                            <tr class="mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer" data-mistake-id="distraction">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                                            <i class="fas fa-mobile-alt text-blue-600 dark:text-blue-300"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Distraction</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-gray-100">Psychological</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">Low</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">Minor</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">28</div>
                                </td>
                            </tr>
                            <tr class="mistake-item hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
                                            <i class="fas fa-tired text-purple-600 dark:text-purple-300"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Fatigue</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-gray-100">Psychological</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">Medium</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">Moderate</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">19</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                            Previous
                        </button>
                        <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                            Next
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">4</span> of <span class="font-medium">24</span> mistakes
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-dark-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button aria-current="page" class="z-10 bg-primary-50 dark:bg-primary-900/50 border-primary-500 dark:border-primary-600 text-primary-600 dark:text-primary-300 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </button>
                                <button class="bg-white dark:bg-dark-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </button>
                                <button class="bg-white dark:bg-dark-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </button>
                                <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-dark-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mistake Analysis Modal -->
    <div id="mistakeModal" class="modal fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="modal-content inline-block align-bottom bg-white dark:bg-dark-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-dark-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div id="mistakeIcon" class="flex-shrink-0 h-12 w-12 rounded-lg bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
                            <i class="fas fa-brain text-red-600 dark:text-red-300 text-xl"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="mistakeType">Overconfidence</h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400" id="mistakeCategory">Cognitive Mistake</p>
                                <div class="mt-4">
                                    <p class="text-sm text-gray-700 dark:text-gray-300" id="mistakeDescription">
                                        Ignored warning signs due to overconfidence in initial analysis.
                                    </p>
                                </div>
                                <div class="mt-4 grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Severity</p>
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeSeverity">High</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Frequency</p>
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeFrequency">43 times</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Time</p>
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeTime">Today, 3:42 PM</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Context</p>
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" id="mistakeContext">Time Pressure</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700/30 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="close-mistake-modal w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-500 text-base font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Analyze Pattern
                    </button>
                    <button type="button" class="close-mistake-modal mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-dark-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Check for saved user preference or use system preference
        if (localStorage.getItem('darkMode') === 'true' || 
            (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
            darkModeToggle.checked = true;
        } else {
            html.classList.remove('dark');
            darkModeToggle.checked = false;
        }

        // Toggle dark mode
        darkModeToggle.addEventListener('change', function() {
            if (this.checked) {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
            } else {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
            }
        });

        // Mistake data
        const mistakes = {
            'overconfidence': {
                type: 'Overconfidence',
                category: 'Cognitive Mistake',
                description: 'Ignored multiple warning signs in the data due to overconfidence in initial analysis. Failed to properly consider alternative interpretations despite contradictory evidence. This led to a significant error in the final decision.',
                severity: 'High',
                frequency: '43 times',
                time: 'Today, 3:42 PM',
                context: 'Time Pressure',
                icon: 'fas fa-brain',
                iconBg: 'bg-red-100 dark:bg-red-900/50',
                iconColor: 'text-red-600 dark:text-red-300'
            },
            'rushing': {
                type: 'Rushing',
                category: 'Behavioral Mistake',
                description: 'Made decision too quickly without proper verification of all relevant factors. Skipped important steps in the analysis process due to perceived time constraints, leading to an incomplete assessment.',
                severity: 'Medium',
                frequency: '32 times',
                time: 'Yesterday, 2:15 PM',
                context: 'Deadline Pressure',
                icon: 'fas fa-running',
                iconBg: 'bg-yellow-100 dark:bg-yellow-900/50',
                iconColor: 'text-yellow-600 dark:text-yellow-300'
            },
            'distraction': {
                type: 'Distraction',
                category: 'Environmental Mistake',
                description: 'Phone notification interrupted focus during critical moment, causing loss of concentration and subsequent error in data entry. Failed to maintain focused environment for important work.',
                severity: 'Low',
                frequency: '28 times',
                time: 'Yesterday, 10:30 AM',
                context: 'Multitasking',
                icon: 'fas fa-mobile-alt',
                iconBg: 'bg-blue-100 dark:bg-blue-900/50',
                iconColor: 'text-blue-600 dark:text-blue-300'
            }
        };

        // Mistake Analysis Modal
        const mistakeModal = document.getElementById('mistakeModal');
        const closeMistakeButtons = document.querySelectorAll('.close-mistake-modal');

        // Function to open modal with mistake details
        function openMistakeModal(mistakeId = 'overconfidence') {
            const mistake = mistakes[mistakeId];
            if (!mistake) {
                console.error('Mistake data not found for ID:', mistakeId);
                return;
            }

            try {
                // Update modal content with error checking
                const elements = {
                    mistakeType: document.getElementById('mistakeType'),
                    mistakeCategory: document.getElementById('mistakeCategory'),
                    mistakeDescription: document.getElementById('mistakeDescription'),
                    mistakeSeverity: document.getElementById('mistakeSeverity'),
                    mistakeFrequency: document.getElementById('mistakeFrequency'),
                    mistakeTime: document.getElementById('mistakeTime'),
                    mistakeContext: document.getElementById('mistakeContext'),
                    mistakeIcon: document.getElementById('mistakeIcon')
                };

                // Check if all elements exist
                for (const [key, element] of Object.entries(elements)) {
                    if (!element) {
                        console.error(`Element not found: ${key}`);
                        return;
                    }
                }

                // Update text content
                elements.mistakeType.textContent = mistake.type;
                elements.mistakeCategory.textContent = mistake.category;
                elements.mistakeDescription.textContent = mistake.description;
                elements.mistakeSeverity.textContent = mistake.severity;
                elements.mistakeFrequency.textContent = mistake.frequency;
                elements.mistakeTime.textContent = mistake.time;
                elements.mistakeContext.textContent = mistake.context;

                // Update icon
                elements.mistakeIcon.className = `flex-shrink-0 h-12 w-12 rounded-lg ${mistake.iconBg} flex items-center justify-center`;
                const icon = document.createElement('i');
                icon.className = `${mistake.icon} ${mistake.iconColor} text-xl`;
                elements.mistakeIcon.innerHTML = '';
                elements.mistakeIcon.appendChild(icon);

                // Show modal
                mistakeModal.classList.remove('hidden');
                setTimeout(() => {
                    mistakeModal.classList.add('active');
                }, 10);
            } catch (error) {
                console.error('Error opening mistake modal:', error);
            }
        }

        // Add event listeners to analyze buttons
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('Analyze')) {
                button.addEventListener('click', function() {
                    // Get mistake ID from data attribute or use default
                    const mistakeId = this.getAttribute('data-mistake-id') || 'overconfidence';
                    openMistakeModal(mistakeId);
                });
            }
        });

        // Add event listeners to table rows for mistake details
        document.querySelectorAll('tr[data-mistake-id]').forEach(row => {
            row.addEventListener('click', function() {
                const mistakeId = this.getAttribute('data-mistake-id');
                if (mistakeId) {
                    openMistakeModal(mistakeId);
                }
            });
        });

        // Close modal
        closeMistakeButtons.forEach(button => {
            button.addEventListener('click', function() {
                mistakeModal.classList.remove('active');
                setTimeout(() => {
                    mistakeModal.classList.add('hidden');
                }, 300);
            });
        });

        // Close modal when clicking outside
        mistakeModal.addEventListener('click', function(e) {
            if (e.target === mistakeModal) {
                mistakeModal.classList.remove('active');
                setTimeout(() => {
                    mistakeModal.classList.add('hidden');
                }, 300);
            }
        });

        // Add shake animation to high severity mistakes
        document.querySelectorAll('[id="mistakeSeverity"]').forEach(el => {
            if (el.textContent === 'High') {
                el.classList.add('shake');
            }
        });

        // Animate bars in the distribution chart
        document.addEventListener('DOMContentLoaded', function() {
            const bars = document.querySelectorAll('.bar');
            bars.forEach(bar => {
                const height = bar.style.height;
                bar.style.height = '0%';
                setTimeout(() => {
                    bar.style.height = height;
                }, 100);
            });
        });
    </script>
</body>
</html>