"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Bo<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

const loadingSteps = [
  {
    title: "Analyzing Your Trades",
    subtitle: "Our AI is reviewing your trading patterns and performance metrics.",
    progress: 33,
  },
  {
    title: "Processing Data",
    subtitle: "Almost there! We're compiling insights and recommendations.",
    progress: 66,
  },
  {
    title: "Finalizing Report",
    subtitle: "Your personalized trading summary is almost ready.",
    progress: 100,
  },
]

export function LoadingSection() {
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < loadingSteps.length - 1) {
          const nextStep = prev + 1
          setProgress(loadingSteps[nextStep].progress)
          return nextStep
        }
        return prev
      })
    }, 2000)

    // Initial progress
    setProgress(loadingSteps[0].progress)

    return () => clearInterval(timer)
  }, [])

  const currentStepData = loadingSteps[currentStep]

  return (
    <Card className="bg-slate-800 border-slate-700 shadow-xl">
      <CardContent className="p-12">
        <div className="max-w-lg mx-auto text-center">
          {/* AI Icon */}
          <div className="w-24 h-24 mx-auto mb-8 rounded-2xl bg-blue-600/20 flex items-center justify-center border border-blue-500/30 animate-pulse">
            <div className="w-16 h-16 rounded-xl bg-blue-600 flex items-center justify-center shadow-lg animate-bounce">
              <Bot className="w-8 h-8 text-white" />
            </div>
          </div>

          {/* Title and Subtitle */}
          <h2 className="text-3xl font-bold mb-3 text-white">{currentStepData.title}</h2>
          <p className="text-slate-400 mb-8 text-lg">{currentStepData.subtitle}</p>

          {/* Progress Bar */}
          <div className="mb-8">
            <Progress value={progress} className="h-3 bg-slate-700" />
          </div>

          {/* Step Indicators */}
          <div className="grid grid-cols-3 gap-6">
            {loadingSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div
                  className={cn(
                    "w-10 h-10 mx-auto mb-3 rounded-full flex items-center justify-center border-2 transition-all duration-500",
                    index <= currentStep
                      ? "bg-blue-600 border-blue-500 text-white shadow-lg"
                      : "bg-slate-700 border-slate-600 text-slate-400",
                  )}
                >
                  {index + 1}
                </div>
                <span
                  className={cn(
                    "text-sm font-medium transition-colors duration-500",
                    index <= currentStep ? "text-white" : "text-slate-500",
                  )}
                >
                  {index === 0 ? "Analyzing" : index === 1 ? "Processing" : "Finalizing"}
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
