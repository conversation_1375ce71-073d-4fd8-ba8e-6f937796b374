<?php
/**
 * Quick setup script to populate mistakes table
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'diary';

echo "<h2>Mistake Database Setup</h2>";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if mistakes table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) FROM mistakes WHERE is_active = 1");
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<p style='color: blue;'>ℹ️ Mistakes table already has $count records</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Mistakes table is empty. Inserting default mistakes...</p>";
        
        // Insert default mistakes
        $mistakes = [
            ['FOMO Entry', 'Psychological', 'Fear of missing out led to poor entry timing', 'Medium', 'Moderate', 'fas fa-brain', 'purple'],
            ['Exited Too Late', 'Cognitive', 'Failed to exit at optimal time due to greed', 'Medium', 'Moderate', 'fas fa-clock', 'green'],
            ['Overtrading', 'Behavioral', 'Took too many trades in a short period', 'High', 'Critical', 'fas fa-sync-alt', 'blue'],
            ['Risked Too Much', 'Behavioral', 'Position size was too large for account', 'High', 'Critical', 'fas fa-exclamation-triangle', 'blue'],
            ['No Stop Loss', 'Technical', 'Failed to set proper stop loss', 'High', 'Critical', 'fas fa-shield-alt', 'red'],
            ['Ignored Plan', 'Behavioral', 'Deviated from predetermined trading plan', 'High', 'Critical', 'fas fa-route', 'orange'],
            ['Emotional Trading', 'Psychological', 'Made decisions based on emotions rather than analysis', 'Medium', 'Moderate', 'fas fa-heart', 'red'],
            ['Poor Timing', 'Technical', 'Entry or exit timing was suboptimal', 'Medium', 'Moderate', 'fas fa-stopwatch', 'yellow'],
            ['Lack of Research', 'Technical', 'Insufficient analysis before entering trade', 'Medium', 'Moderate', 'fas fa-search', 'indigo'],
            ['Revenge Trading', 'Psychological', 'Attempted to recover losses with impulsive trades', 'High', 'Critical', 'fas fa-angry', 'red']
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO mistakes (name, category, description, severity, impact, icon, color_class, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 1)
        ");
        
        $inserted = 0;
        foreach ($mistakes as $mistake) {
            try {
                $insertStmt->execute($mistake);
                $inserted++;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error inserting {$mistake[0]}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p style='color: green;'>✓ Inserted $inserted default mistakes</p>";
    }
    
    // Show current mistakes
    echo "<h3>Current Mistakes in Database:</h3>";
    $stmt = $pdo->query("SELECT id, name, category, severity, impact, icon, color_class FROM mistakes WHERE is_active = 1 ORDER BY id");
    $mistakes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($mistakes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background-color: #f2f2f2;'><th>ID</th><th>Name</th><th>Category</th><th>Severity</th><th>Impact</th><th>Icon</th><th>Color</th></tr>";
        foreach ($mistakes as $mistake) {
            echo "<tr>";
            echo "<td>{$mistake['id']}</td>";
            echo "<td>{$mistake['name']}</td>";
            echo "<td>{$mistake['category']}</td>";
            echo "<td>{$mistake['severity']}</td>";
            echo "<td>{$mistake['impact']}</td>";
            echo "<td>{$mistake['icon']}</td>";
            echo "<td>{$mistake['color_class']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p style='color: green;'>✓ Setup complete! You can now use the mistake analysis feature.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Go to your trading application</li>";
    echo "<li>Add some trades and associate them with mistakes</li>";
    echo "<li>Visit the Mistakes page to see analytics</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
</style>
