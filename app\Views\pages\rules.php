<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
        color: #1e293b;
    }

    .dark body {
        background-color: #111827;
        color: #e2e8f0;
    }

    .rule-card {
        transition: all 0.3s ease;
        transform: translateY(0);
    }

    .rule-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .glow-effect {
        box-shadow: 0 0 15px rgba(58, 130, 246, 0.3);
    }

    .glow-effect:hover {
        box-shadow: 0 0 25px rgba(58, 130, 246, 0.5);
    }

    .progress-ring {
        transform: rotate(-90deg);
    }

    .progress-ring__circle {
        transition: stroke-dashoffset 0.5s ease;
        transform-origin: 50% 50%;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .rule-card {
        min-height: 180px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .rule-card:hover {
        transform: translateY(-6px) scale(1.02);
    }

    /* Enhanced shadow effects for different themes */
    .rule-card.shadow-blue:hover {
        box-shadow: 0 20px 40px -12px rgba(59, 130, 246, 0.4), 0 8px 16px -8px rgba(59, 130, 246, 0.3);
    }

    .rule-card.shadow-green:hover {
        box-shadow: 0 20px 40px -12px rgba(34, 197, 94, 0.4), 0 8px 16px -8px rgba(34, 197, 94, 0.3);
    }

    .rule-card.shadow-red:hover {
        box-shadow: 0 20px 40px -12px rgba(239, 68, 68, 0.4), 0 8px 16px -8px rgba(239, 68, 68, 0.3);
    }

    .rule-card.shadow-purple:hover {
        box-shadow: 0 20px 40px -12px rgba(147, 51, 234, 0.4), 0 8px 16px -8px rgba(147, 51, 234, 0.3);
    }

    .rule-card.shadow-indigo:hover {
        box-shadow: 0 20px 40px -12px rgba(99, 102, 241, 0.4), 0 8px 16px -8px rgba(99, 102, 241, 0.3);
    }

    .rule-card.shadow-orange:hover {
        box-shadow: 0 20px 40px -12px rgba(249, 115, 22, 0.4), 0 8px 16px -8px rgba(249, 115, 22, 0.3);
    }

    .rule-card.shadow-gray:hover {
        box-shadow: 0 20px 40px -12px rgba(107, 114, 128, 0.4), 0 8px 16px -8px rgba(107, 114, 128, 0.3);
    }

    /* Dark theme shadow adjustments */
    .dark .rule-card.shadow-blue:hover {
        box-shadow: 0 20px 40px -12px rgba(96, 165, 250, 0.3), 0 8px 16px -8px rgba(96, 165, 250, 0.2);
    }

    .dark .rule-card.shadow-green:hover {
        box-shadow: 0 20px 40px -12px rgba(74, 222, 128, 0.3), 0 8px 16px -8px rgba(74, 222, 128, 0.2);
    }

    .dark .rule-card.shadow-red:hover {
        box-shadow: 0 20px 40px -12px rgba(248, 113, 113, 0.3), 0 8px 16px -8px rgba(248, 113, 113, 0.2);
    }

    .dark .rule-card.shadow-purple:hover {
        box-shadow: 0 20px 40px -12px rgba(168, 85, 247, 0.3), 0 8px 16px -8px rgba(168, 85, 247, 0.2);
    }

    .dark .rule-card.shadow-indigo:hover {
        box-shadow: 0 20px 40px -12px rgba(129, 140, 248, 0.3), 0 8px 16px -8px rgba(129, 140, 248, 0.2);
    }

    .dark .rule-card.shadow-orange:hover {
        box-shadow: 0 20px 40px -12px rgba(251, 146, 60, 0.3), 0 8px 16px -8px rgba(251, 146, 60, 0.2);
    }

    .dark .rule-card.shadow-gray:hover {
        box-shadow: 0 20px 40px -12px rgba(156, 163, 175, 0.3), 0 8px 16px -8px rgba(156, 163, 175, 0.2);
    }

    /* Progress Ring Styles */
    .progress-ring__circle {
        transition: stroke-dashoffset 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }
</style>

<div class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6">
    <!-- Page Header with Glass Morphism Effect -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Trading Rules
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2 text-sm sm:text-base max-w-lg">
                Define and track your trading rules to improve consistency and performance.
                <span class="text-blue-500 dark:text-blue-400 font-medium">Your discipline creates profits.</span>
            </p>
        </div>
        <button id="createRuleBtn" class="group relative w-full sm:w-auto flex items-center justify-center px-5 py-3 rounded-xl bg-blue-600 text-white font-medium hover:bg-blue-700 transition-all duration-300 text-sm sm:text-base shadow-lg hover:shadow-xl">
            <span class="relative z-10 flex items-center">
                <i class="fas fa-plus w-4 h-4 sm:w-5 sm:h-5 mr-2"></i>
                Create New Rule
            </span>
        </button>
    </div>

    <!-- Rule Selector -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
            <h2 class="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-white mb-3 sm:mb-0 flex items-center">
                <span class="bg-blue-500 text-white p-2 rounded-lg mr-3">
                    <i class="fas fa-clipboard-list w-5 h-5"></i>
                </span>
                Your Rule Collection
            </h2>
            <div class="relative w-full sm:w-72">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" id="searchRules" placeholder="Search rules..." class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl py-2.5 pl-10 pr-4 text-sm sm:text-base text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
            </div>
        </div>

        <!-- Filter Chips with Hover Effects -->
        <div class="flex flex-wrap gap-3 mb-6">
            <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-gray-100 dark:bg-gray-700 text-sm text-gray-800 dark:text-white border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 cursor-pointer shadow-lg active overflow-hidden" data-filter="all">
                <span class="relative z-10 flex items-center">
                    All Rules <span id="totalRulesCount" class="ml-2 text-gray-500 dark:text-gray-300">(0)</span>
                </span>
            </button>
            <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-blue-100 dark:bg-blue-900/20 text-sm text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="most-used">
                <span class="relative z-10 flex items-center">
                    Most Used <i class="fas fa-trending-up w-4 h-4 ml-2"></i>
                </span>
            </button>
            <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-blue-100 dark:bg-blue-900/20 text-sm text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="high-impact">
                <span class="relative z-10 flex items-center">
                    High Impact <i class="fas fa-bolt w-4 h-4 ml-2"></i>
                </span>
            </button>
            <button class="filter-btn group relative inline-flex items-center px-4 py-2.5 rounded-xl bg-green-100 dark:bg-green-900/20 text-sm text-green-600 dark:text-green-400 border border-green-200 dark:border-green-800 hover:bg-green-200 dark:hover:bg-green-900/30 transition-all duration-300 cursor-pointer shadow-lg overflow-hidden" data-filter="new">
                <span class="relative z-10 flex items-center">
                    New <i class="fas fa-sparkles w-4 h-4 ml-2"></i>
                </span>
            </button>
        </div>

        <!-- Rules Grid with Animated Cards -->
        <div id="rulesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            <!-- Rules will be loaded here via AJAX -->
        </div>

        <!-- Add New Rule Card -->
        <div class="flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-500 transition-all duration-300 cursor-pointer mt-6" id="addRuleCard">
            <div class="text-center group">
                <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-blue-500 flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-plus text-white text-xl group-hover:scale-110 transition-transform duration-300"></i>
                </div>
                <p class="text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors duration-300">Add new trading rule</p>
            </div>
        </div>
    </div>

    <!-- Rules Analysis Section -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
            <div class="flex items-center space-x-3 mb-4 sm:mb-0">
                <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-blue-500 flex items-center justify-center shadow-lg">
                    <i class="fas fa-chart-line text-white text-lg sm:text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white">Rules Performance Analysis</h2>
                    <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">Track your trading discipline and rule adherence</p>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                <select id="analyticsTimeframe" class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                    <option value="7">Last 7 days</option>
                    <option value="30" selected>Last 30 days</option>
                    <option value="90">Last 90 days</option>
                    <option value="365">Last year</option>
                </select>
                <button id="refreshAnalytics" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-sync-alt w-3 h-3 sm:w-4 sm:h-4 mr-2"></i>
                        Refresh
                    </span>
                </button>
            </div>
        </div>
        


        <!-- Top Rules Chart with Progress Rings -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-5 sm:p-6 lg:p-7 border border-gray-200 dark:border-gray-600 shadow-md hover:shadow-lg transition-all duration-300">
                <div class="flex items-center space-x-3 mb-5 sm:mb-6 lg:mb-7">
                    <div class="w-8 h-8 sm:w-9 sm:h-9 rounded-lg bg-blue-500 flex items-center justify-center shadow-sm">
                        <i class="fas fa-trophy text-white text-sm"></i>
                    </div>
                    <h3 class="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100">Top 5 Most Followed Rules</h3>
                </div>
                <div id="topRulesList" class="space-y-5">
                    <!-- Top rules will be loaded here -->
                </div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-5 sm:p-6 lg:p-7 border border-gray-200 dark:border-gray-600 shadow-md hover:shadow-lg transition-all duration-300">
                <div class="flex items-center space-x-3 mb-5 sm:mb-6 lg:mb-7">
                    <div class="w-8 h-8 sm:w-9 sm:h-9 rounded-lg bg-red-500 flex items-center justify-center shadow-sm">
                        <i class="fas fa-exclamation-circle text-white text-sm"></i>
                    </div>
                    <h3 class="text-lg sm:text-xl font-bold text-gray-800 dark:text-gray-100">Least Used Rules</h3>
                </div>
                <div class="mt-6" id="leastUsedRulesContainer">
                    <!-- Least used rules will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Rule Modal with Glass Morphism -->
<div id="createRuleModal" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-[1000] hidden">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 transform transition-all shadow-xl border border-gray-200/50 dark:border-gray-700/50">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                    <i class="fas fa-plus text-white text-sm"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Create New Rule</h2>
            </div>
            <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                <i class="fas fa-times w-5 h-5"></i>
            </button>
        </div>

        <form id="ruleForm">
            <div class="mb-4">
                <label for="ruleName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rule Name</label>
                <input type="text" id="ruleName" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" placeholder="e.g., Wait for confirmation" required>
            </div>

            <div class="mb-4">
                <label for="ruleCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                <select id="ruleCategory" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                    <option value="entry">Entry</option>
                    <option value="exit">Exit</option>
                    <option value="risk_management">Risk Management</option>
                    <option value="psychology">Psychology</option>
                    <option value="analysis">Analysis</option>
                    <option value="custom">Custom</option>
                </select>
            </div>

            <div class="mb-6">
                <label for="ruleDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea id="ruleDescription" rows="4" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none" placeholder="Describe the rule in detail..."></textarea>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelRuleBtn" class="px-4 py-2.5 rounded-lg bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2.5 rounded-lg bg-blue-600 text-white font-medium hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                    Save Rule
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Rule Modal with Glass Morphism -->
<div id="editRuleModal" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-[1000] hidden">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 transform transition-all shadow-xl border border-gray-200/50 dark:border-gray-700/50">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                    <i class="fas fa-edit text-white text-sm"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Edit Rule</h2>
            </div>
            <button id="closeEditModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                <i class="fas fa-times w-5 h-5"></i>
            </button>
        </div>

        <form id="editRuleForm">
            <input type="hidden" id="editRuleId">
            <div class="mb-4">
                <label for="editRuleName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rule Name</label>
                <input type="text" id="editRuleName" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" placeholder="e.g., Wait for confirmation" required>
            </div>

            <div class="mb-4">
                <label for="editRuleCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                <select id="editRuleCategory" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                    <option value="entry">Entry</option>
                    <option value="exit">Exit</option>
                    <option value="risk_management">Risk Management</option>
                    <option value="psychology">Psychology</option>
                    <option value="analysis">Analysis</option>
                    <option value="custom">Custom</option>
                </select>
            </div>

            <div class="mb-6">
                <label for="editRuleDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea id="editRuleDescription" rows="4" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2.5 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none" placeholder="Describe the rule in detail..."></textarea>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelEditRuleBtn" class="px-4 py-2.5 rounded-lg bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2.5 rounded-lg bg-blue-600 text-white font-medium hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                    Update Rule
                </button>
            </div>
        </form>
    </div>
</div>
