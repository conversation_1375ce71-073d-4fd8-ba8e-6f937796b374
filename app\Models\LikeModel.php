<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\PostModel;
use App\Models\CommentModel;

class LikeModel extends Model
{
    protected $table = 'community_likes';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'user_id',
        'likeable_type',
        'likeable_id'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = false;
    protected $deletedField = false;

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'likeable_type' => 'required|in_list[post,comment]',
        'likeable_id' => 'required|integer'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'likeable_type' => [
            'required' => 'Likeable type is required',
            'in_list' => 'Likeable type must be either post or comment'
        ],
        'likeable_id' => [
            'required' => 'Likeable ID is required',
            'integer' => 'Likeable ID must be an integer'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Toggle like for a post or comment
     */
    public function toggleLike($userId, $likeableType, $likeableId)
    {
        try {
            log_message('info', "LikeModel::toggleLike - User: $userId, Type: $likeableType, ID: $likeableId");

            // Use direct database query to avoid model validation issues
            $db = \Config\Database::connect();
            $builder = $db->table($this->table);

            $existingLike = $builder->where([
                'user_id' => $userId,
                'likeable_type' => $likeableType,
                'likeable_id' => $likeableId
            ])->get()->getRowArray();

            log_message('info', 'Existing like: ' . json_encode($existingLike));

            if ($existingLike) {
                // Unlike - remove the like
                log_message('info', 'Removing like with ID: ' . $existingLike['id']);

                $builder = $db->table($this->table);
                $deleteResult = $builder->where('id', $existingLike['id'])->delete();

                if ($deleteResult) {
                    $this->decrementLikeCount($likeableType, $likeableId);
                    return ['action' => 'unliked', 'liked' => false];
                } else {
                    log_message('error', 'Failed to delete like');
                    return ['action' => 'error', 'liked' => false];
                }
            } else {
                // Like - add the like
                $data = [
                    'user_id' => $userId,
                    'likeable_type' => $likeableType,
                    'likeable_id' => $likeableId,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                log_message('info', 'Inserting like with data: ' . json_encode($data));

                $builder = $db->table($this->table);
                $insertResult = $builder->insert($data);

                if ($insertResult) {
                    log_message('info', 'Like inserted successfully');
                    $this->incrementLikeCount($likeableType, $likeableId);
                    return ['action' => 'liked', 'liked' => true];
                } else {
                    log_message('error', 'Failed to insert like. DB Error: ' . $db->error()['message']);
                    return ['action' => 'error', 'liked' => false];
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Exception in LikeModel::toggleLike: ' . $e->getMessage());
            return ['action' => 'error', 'liked' => false];
        }
    }

    /**
     * Check if user has liked an item
     */
    public function hasUserLiked($userId, $likeableType, $likeableId)
    {
        return $this->where([
            'user_id' => $userId,
            'likeable_type' => $likeableType,
            'likeable_id' => $likeableId
        ])->first() !== null;
    }

    /**
     * Get like count for an item
     */
    public function getLikeCount($likeableType, $likeableId)
    {
        try {
            $db = \Config\Database::connect();
            $builder = $db->table($this->table);

            return $builder->where([
                'likeable_type' => $likeableType,
                'likeable_id' => $likeableId
            ])->countAllResults();
        } catch (\Exception $e) {
            log_message('error', 'Error getting like count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get users who liked an item
     */
    public function getLikers($likeableType, $likeableId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' l');
        $builder->select('u.id, u.full_name, u.profile, u.badge');
        $builder->join('users u', 'u.id = l.user_id');
        $builder->where('l.likeable_type', $likeableType);
        $builder->where('l.likeable_id', $likeableId);
        $builder->orderBy('l.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get user's liked posts
     */
    public function getUserLikedPosts($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' l');
        $builder->select('p.*, u.full_name, u.profile, u.badge, l.created_at as liked_at');
        $builder->join('community_posts p', 'p.id = l.likeable_id');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('l.user_id', $userId);
        $builder->where('l.likeable_type', 'post');
        $builder->where('p.deleted_at', null);
        $builder->orderBy('l.created_at', 'DESC');
        $builder->limit($limit, $offset);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get like status for multiple items
     */
    public function getLikeStatusForItems($userId, $items)
    {
        if (empty($items)) {
            return [];
        }

        $builder = $this->db->table($this->table);
        $builder->select('likeable_type, likeable_id');
        $builder->where('user_id', $userId);
        
        $builder->groupStart();
        foreach ($items as $item) {
            $builder->orWhere([
                'likeable_type' => $item['type'],
                'likeable_id' => $item['id']
            ]);
        }
        $builder->groupEnd();
        
        $likes = $builder->get()->getResultArray();
        
        $likedItems = [];
        foreach ($likes as $like) {
            $likedItems[$like['likeable_type'] . '_' . $like['likeable_id']] = true;
        }
        
        return $likedItems;
    }

    /**
     * Increment like count in the respective table
     */
    private function incrementLikeCount($likeableType, $likeableId)
    {
        try {
            $db = \Config\Database::connect();

            if ($likeableType === 'post') {
                $builder = $db->table('community_posts');
                $result = $builder->where('id', $likeableId)
                                 ->set('likes_count', 'likes_count + 1', false)
                                 ->update();
                log_message('info', "Incremented likes for post $likeableId: " . ($result ? 'success' : 'failed'));
            } elseif ($likeableType === 'comment') {
                $builder = $db->table('community_comments');
                $result = $builder->where('id', $likeableId)
                                 ->set('likes_count', 'likes_count + 1', false)
                                 ->update();
                log_message('info', "Incremented likes for comment $likeableId: " . ($result ? 'success' : 'failed'));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error incrementing like count: ' . $e->getMessage());
        }
    }

    /**
     * Decrement like count in the respective table
     */
    private function decrementLikeCount($likeableType, $likeableId)
    {
        try {
            $db = \Config\Database::connect();

            if ($likeableType === 'post') {
                $builder = $db->table('community_posts');
                $result = $builder->where('id', $likeableId)
                                 ->where('likes_count >', 0)
                                 ->set('likes_count', 'likes_count - 1', false)
                                 ->update();
                log_message('info', "Decremented likes for post $likeableId: " . ($result ? 'success' : 'failed'));
            } elseif ($likeableType === 'comment') {
                $builder = $db->table('community_comments');
                $result = $builder->where('id', $likeableId)
                                 ->where('likes_count >', 0)
                                 ->set('likes_count', 'likes_count - 1', false)
                                 ->update();
                log_message('info', "Decremented likes for comment $likeableId: " . ($result ? 'success' : 'failed'));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error decrementing like count: ' . $e->getMessage());
        }
    }

    /**
     * Remove all likes for a user (for cleanup)
     */
    public function removeUserLikes($userId)
    {
        return $this->where('user_id', $userId)->delete();
    }

    /**
     * Remove all likes for an item (for cleanup)
     */
    public function removeItemLikes($likeableType, $likeableId)
    {
        return $this->where([
            'likeable_type' => $likeableType,
            'likeable_id' => $likeableId
        ])->delete();
    }
}
