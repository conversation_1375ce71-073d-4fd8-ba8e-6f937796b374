<link rel="stylesheet" href="<?= base_url() ?>assets/css/pages/calender.css?v=<?= rand() ?>">

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <header class="hidden flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
            <div class="flex items-center justify-between">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white tracking-tight">Trading Journal</h1>
                <div class="relative inline-block w-12 ml-4 align-middle select-none">
                    <input type="checkbox" name="darkModeToggle" id="darkModeToggle"
                        class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
                    <label for="darkModeToggle"
                        class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer flex items-center justify-between px-1">
                        <span class="text-xs text-gray-700"><i class="fas fa-sun"></i></span>
                        <span class="text-xs text-gray-700"><i class="fas fa-moon"></i></span>
                    </label>
                </div>
            </div>
            <p class="text-gray-500 dark:text-gray-400 mt-1.5">Track your daily trading performance</p>
        </div>
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full md:w-auto">
            <button
                class="btn-primary px-4 py-2.5 text-white rounded-lg transition-all flex items-center justify-center w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Add Trade
            </button>
            <div class="relative w-full sm:w-auto select-container rounded-lg">
                <select
                    class="appearance-none px-4 py-2.5 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg w-full text-gray-700 dark:text-gray-200">
                    <option>January 2023</option>
                    <option>February 2023</option>
                    <option selected>March 2023</option>
                    <option>April 2023</option>
                </select>
                <div
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-chevron-down text-sm"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="flex justify-between items-center mb-3">
        <div>
            <!-- <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Dashboard Overview</h1>
            <p class="text-gray-500 dark:text-gray-400">Welcome back, <?= $userDetails['full_name'] ?>! Here's your
                trading performance
                summary.</p> -->
        </div>
        <div class="flex space-x-3">
            <div class="relative">
                <select id="marketTypeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <!-- <option value="0">All markets</option> -->
                    <option value="1">Indian</option>
                    <option value="2">Forex</option>
                    <option value="3">Crypto</option>
                </select>

                <select id="calenderMonth"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">January</option>
                    <option value="2">February</option>
                    <option value="3">March</option>
                    <option value="4">April</option>
                    <option value="5">May</option>
                    <option value="6">June</option>
                    <option value="7">July</option>
                    <option value="8">August</option>
                    <option value="9">September</option>
                    <option value="10">October</option>
                    <option value="11">November</option>
                    <option value="12">December</option>
                </select>

                <select id="calenderYear"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <?php
                    $currentYear = date('Y');
                    $minYear = 2025;
                    for ($year = $currentYear; $year >= $minYear; $year--) {
                        echo "<option value=\"$year\">$year</option>";
                    }
                    ?>
                </select>

            </div>
        </div>
    </div>



    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
        <!-- Total Profit/Loss Card -->
        <div class="stat-card bg-white dark:bg-gray-800 p-4 rounded-xl shadow">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 font-semibold uppercase tracking-wider">Total P&L
                    </p>
                    <h3 id="totalPnl" class="text-2xl font-bold text-green-600 dark:text-green-400 mt-2 mb-1">₹0</h3>
                    <div id="totalPnlChange"
                        class="mt-1 text-xs font-medium inline-flex items-center py-1 rounded-full"></div>
                </div>
                <div class="icon-container text-green-500 dark:text-green-400 text-xl">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>

        <!-- Win Rate Card -->
        <div class="stat-card bg-white dark:bg-gray-800 p-4 rounded-xl shadow">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 font-semibold uppercase tracking-wider">Win Rate
                    </p>
                    <h3 id="winRate" class="text-2xl font-bold text-green-600 dark:text-green-400 mt-2 mb-1">0%</h3>
                    <div id="winRateChange"
                        class="mt-1 text-xs font-medium inline-flex items-center py-1 rounded-full"></div>
                </div>
                <div class="icon-container text-green-500 dark:text-green-400 text-xl">
                    <i class="fas fa-trophy"></i>
                </div>
            </div>
        </div>

        <!-- Total Trades Card -->
        <div class="stat-card bg-white dark:bg-gray-800 p-4 rounded-xl shadow">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 font-semibold uppercase tracking-wider">Total
                        Trades</p>
                    <h3 id="totalTrades" class="text-2xl font-bold text-gray-700 dark:text-gray-300 mt-2 mb-1">0</h3>
                    <div id="totalTradesChange"
                        class="mt-1 text-xs font-medium inline-flex items-center py-1 rounded-full"></div>
                </div>
                <div class="icon-container text-gray-500 dark:text-gray-400 text-xl">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>

        <!-- Avg. R:R Card -->
        <div class="stat-card bg-white dark:bg-gray-800 p-4 rounded-xl shadow">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 font-semibold uppercase tracking-wider">Avg. R:R
                    </p>
                    <h3 id="avgRiskReward" class="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-2 mb-1">1:0
                    </h3>
                    <div id="avgRiskRewardChange"
                        class="mt-1 text-xs font-medium inline-flex items-center py-1 rounded-full"></div>
                </div>
                <div class="icon-container text-blue-500 dark:text-blue-400 text-xl">
                    <i class="fas fa-balance-scale-right"></i>
                </div>
            </div>
        </div>
    </div>


    <div
        class="bg-white dark:bg-slate-800 rounded-xl shadow-xl overflow-hidden border border-gray-100 dark:border-slate-700">
        <div class="grid grid-cols-7 bg-gray-100 dark:bg-slate-700 p-1 calendar-header gap-0">
            <div
                class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent rounded-l">
                Sun</div>
            <div class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent">
                Mon</div>
            <div class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent">
                Tue</div>
            <div class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent">
                Wed</div>
            <div class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent">
                Thu</div>
            <div class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent">
                Fri</div>
            <div
                class="text-center font-normal sm:font-semibold text-gray-600 dark:text-gray-300 py-3.5 bg-white dark:bg-transparent rounded-r">
                Sat</div>
        </div>

        <div class="grid grid-cols-7 gap-1 bg-gray-100 dark:bg-slate-700 p-1 calendar-grid"></div>
    </div>

    <div class="mt-3 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="flex flex-wrap items-center gap-3">
            <div class="flex items-center legend-item px-3 py-1.5 rounded-lg">
                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300">Profitable Day</span>
            </div>
            <div class="flex items-center legend-item px-3 py-1.5 rounded-lg">
                <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300">Losing Day</span>
            </div>
            <div class="flex items-center legend-item px-3 py-1.5 rounded-lg">
                <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300">Today</span>
            </div>
            <div class="flex items-center legend-item px-3 py-1.5 rounded-lg">
                <div class="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                <span class="text-sm text-gray-700 dark:text-gray-300">Selected</span>
            </div>
        </div>
        <button
            class="hidden btn-secondary px-4 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg transition-all flex items-center justify-center w-full sm:w-auto">
            <i class="fas fa-chart-line mr-2"></i>View Statistics
        </button>
    </div>
</div>

<!-- Modal -->
<div class="modal fixed inset-0 w-full h-full z-50 overflow-y-auto flex items-center justify-center p-4 hidden"
    id="dayDetailsModal">
    <div class="modal-overlay absolute inset-0 bg-black opacity-50"></div>
    <div
        class="modal-container bg-white dark:bg-slate-800 rounded-xl shadow-2xl relative max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="modal-content p-6 w-full">
            <div class="flex justify-between items-center mb-6 pb-4 modal-header">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Trading Details</h2>
                    <p id="tradeDate" class="text-gray-500 dark:text-gray-400">...</p>
                </div>
                <button class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    id="closeModal">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div class="stat-card p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Total P&L</p>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-500" id="totalPnlModal">+$720</p>
                        </div>
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg stat-icon">
                            <i class="fas fa-chart-line text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Average R:R</p>
                            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="avgRiskRewardModal">1:2.5</p>
                        </div>
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg stat-icon">
                            <i class="fas fa-balance-scale text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Total Trades</p>
                            <p class="text-2xl font-bold text-gray-700 dark:text-gray-300" id="totalTradesModal">4</p>
                        </div>
                        <div class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
                            <i class="fas fa-exchange-alt text-gray-600 dark:text-gray-400 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Win Rate</p>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-400" id="winRateModal">75%</p>
                        </div>
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg stat-icon">
                            <i class="fas fa-trophy text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trades Table -->
            <div>

                <div class="overflow-x-auto">
                    <table class="trade-table min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Symbol</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Side</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Size</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Entry</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Exit</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    P&L</th>
                            </tr>
                        </thead>
                        <tbody id="tradesTableBody"
                            class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Trade rows will be inserted here by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>