@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #f0f4ff 0%, #f8fafc 100%);
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

.dark body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.auth-card {
    backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark .auth-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-input {
    transition: all 0.3s ease;
    padding: 12px;
    height: 48px;
    width: 100%;
    border-radius: 8px;
    background: white;
    border: 1px solid #e5e7eb;
    font-size: 14px;
    line-height: 1.5;
}

.dark .form-input {
    background: #1e293b;
    border-color: #334155;
    color: white;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.3);
}

.dark .form-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.3);
}

.toggle-password {
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-password:hover {
    transform: scale(1.1);
}

.social-btn {
    transition: all 0.3s ease;
}

.social-btn:hover {
    transform: translateY(-2px);
}

.tab-indicator {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Animation for floating background elements */
@keyframes float {

    0%,
    100% {
        transform: translateY(0) translateX(0);
    }

    50% {
        transform: translateY(-20px) translateX(20px);
    }
}

.animate-float {
    animation: float 8s ease-in-out infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

/* Prevent horizontal scrolling */
html,
body {
    overflow-x: hidden;
}

body {
    position: relative;
}

/* Input group styling */
.input-group {
    margin-top: 16px;
    position: relative;
}

/* Checkbox styling */
.checkbox {
    height: 16px;
    width: 16px;
    border-radius: 4px;
    border: 1px solid #d1d5db;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 12px 12px;
}

.dark .checkbox {
    border-color: #4b5563;
    background-color: #1f2937;
}

.dark .checkbox:checked {
    background-color: #60a5fa;
    border-color: #60a5fa;
}

/* Label styling */
.input-label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #4b5563;
    transition: all 0.2s ease;
}

.dark .input-label {
    color: #9ca3af;
}

.input-container {
    position: relative;
}

/* Toast Notification System - Updated to slide from top */
.toast-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    pointer-events: none;
    padding-top: 1rem;
}

.toast {
    position: relative;
    margin-bottom: 1rem;
    padding: 1rem 1.25rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    pointer-events: auto;
    transform: translateY(-100px);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    opacity: 0;
    width: 90%;
    max-width: 400px;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast.hide {
    transform: translateY(-100px);
    opacity: 0;
}

.toast.success {
    background-color: #10b981;
    color: white;
}

.toast.error {
    background-color: #ef4444;
    color: white;
}

.toast.warning {
    background-color: #f59e0b;
    color: white;
}

.toast.info {
    background-color: #3b82f6;
    color: white;
}

.toast-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.toast-content {
    flex-grow: 1;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toast-message {
    opacity: 0.9;
}

.toast-close {
    margin-left: 0.75rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    flex-shrink: 0;
    background: none;
    border: none;
    color: inherit;
    padding: 0.25rem;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 0.5rem 0.5rem;
    overflow: hidden;
}

.toast-progress-bar {
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    width: 100%;
    animation: progress linear;
    transform-origin: left;
}

@keyframes progress {
    from {
        transform: scaleX(1);
    }

    to {
        transform: scaleX(0);
    }
}

/* Input validation styles */
.input-error {
    border-color: #ef4444 !important;
}

.dark .input-error {
    border-color: #f87171 !important;
}

.error-message {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: none;
}

.dark .error-message {
    color: #f87171;
}