<?php namespace App\Libraries;

// No need for CodeIgniter's CURLRequest service anymore if using native cURL
// use CodeIgniter\HTTP\CURLRequest;
// use CodeIgniter\Config\Services;

class DhanApi
{
    // Ensure this is the correct base URL based on <PERSON><PERSON>'s v2 API documentation
    protected $baseUrl = 'https://api.dhan.co/v2/';
    protected $accessToken;
    protected $dhanClientId; // Still good to pass if the API ever needs it in a header/param

    public function __construct(string $accessToken, string $dhanClientId)
    {
        $this->accessToken = $accessToken;
        $this->dhanClientId = $dhanClientId; // Store it, even if not directly used in this specific cURL example
    }

    public function getTrades()
    {
        $url = $this->baseUrl . 'trades'; // Construct the full URL

        // Initialize cURL session
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return the response as a string
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'access-token: ' . $this->accessToken // Use the provided access token
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // Verify SSL certificate
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Set a timeout (10 seconds)

        // Execute the cURL request
        $response = curl_exec($ch);

        // Get HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            log_message('error', 'Dhan API cURL Error (Trades): ' . $error_msg);
            curl_close($ch);
            return null; // Or throw an exception
        }

        // Close cURL session
        curl_close($ch);

        // Process the response
        if ($httpCode === 200) {
            return json_decode($response, true);
        } else {
            // Handle API error
            log_message('error', 'Dhan API Error (Trades): HTTP ' . $httpCode . ' - ' . $response);
            return null; // Or throw an exception
        }
    }

    // You might add other methods here for other Dhan API endpoints
    // public function getOrders() { ... }
    // public function getHoldings() { ... }
}