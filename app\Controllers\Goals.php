<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;
use \App\Models\PnlGoalsModel;

helper('cookie');

class Goals extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
        $this->pnlgoalsmodel = new PnlGoalsModel();
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Goals';
        $data['active'] = 'goals';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'goals';
        $data['main_content'] = 'pages/goals';

        return view('includes/template', $data);
    }

    public function submitPnlGoalForm()
    {
        $response = ['status' => false, 'message' => 'Something went wrong.'];

        try {
            $user_id = $this->decrypt_cookie_value(get_cookie('user_session'));

            if (!$user_id) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'User not authenticated.'
                ]);
            }

            $goal_name = $this->request->getVar('goal_name');
            $timeframe = $this->request->getVar('timeframe');
            $goal_type = $this->request->getVar('goal_type');
            $target_value = $this->request->getVar('target_value');
            $custom_number = $this->request->getVar('custom_number');
            $custom_period = $this->request->getVar('custom_period');

            // Basic validation
            if (!$goal_name || !$timeframe || !$goal_type || !$target_value) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Required fields are missing.'
                ]);
            }

            // Prepare data
            $data = [
                'user_id' => $user_id,
                'goal_name' => $goal_name,
                'timeframe' => $timeframe,
                'goal_type' => $goal_type,
                'target_value' => $target_value
            ];

            // Add custom fields if needed
            if ($timeframe === 'custom') {
                if (!$custom_number || !$custom_period) {
                    return $this->response->setJSON([
                        'status' => false,
                        'message' => 'Custom timeframe values are missing.'
                    ]);
                }
                $data['custom_number'] = $custom_number;
                $data['custom_period'] = $custom_period;
            }

            // Save to DB
            if ($this->pnlgoalsmodel->insert($data)) {
                return $this->response->setJSON([
                    'status' => true,
                    'message' => 'P&L Goal saved successfully.'
                ]);
            } else {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Failed to save data.'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ]);
        }
    }

    private function getEndDateForTimeframe($goal)
    {
        $start = strtotime($goal->created_at);

        return match (strtolower($goal->timeframe)) {
            'weekly' => date('Y-m-d H:i:s', strtotime('+7 days', $start)),
            'monthly' => date('Y-m-d H:i:s', strtotime('+1 month', $start)),
            'quarterly' => date('Y-m-d H:i:s', strtotime('+3 months', $start)),
            'halfyear' => date('Y-m-d H:i:s', strtotime('+6 months', $start)),
            'yearly' => date('Y-m-d H:i:s', strtotime('+1 year', $start)),
            'custom' => match (strtolower($goal->custom_period)) {
                    'days' => date('Y-m-d H:i:s', strtotime("+{$goal->custom_number} days", $start)),
                    'weeks' => date('Y-m-d H:i:s', strtotime("+{$goal->custom_number} weeks", $start)),
                    'months' => date('Y-m-d H:i:s', strtotime("+{$goal->custom_number} months", $start)),
                    default => date('Y-m-d H:i:s', $start)
                },
            default => date('Y-m-d H:i:s', strtotime('+1 month', $start))
        };
    }

    public function updateGoalStatuses()
    {
        $db = \Config\Database::connect();
        $user_id = $this->decrypt_cookie_value(get_cookie('user_session'));
        $now = date('Y-m-d H:i:s');

        $goals = $db->table('pnl_goals')
            ->where('user_id', $user_id)
            ->where('status', 1)
            ->where('deleted_at IS NULL')
            ->get()->getResult();

        foreach ($goals as $goal) {
            $startDate = $goal->created_at;
            $endDate = $this->getEndDateForTimeframe($goal);
            $fromDate = date('Y-m-d', strtotime($startDate));
            $toDate = date('Y-m-d', strtotime($endDate));

            // Duration in days and 30% threshold in seconds
            $durationInSeconds = strtotime($toDate) - strtotime($fromDate);
            $thresholdSeconds = $durationInSeconds * 0.3;

            // Check if 30% of timeframe has passed
            $isThresholdCrossed = (strtotime($now) - strtotime($fromDate)) >= $thresholdSeconds;

            $actual = 0;
            $goalAchieved = false;

            $trades = $db->table('trades')
                ->where('user_id', $user_id)
                ->where('deleted_at IS NULL')
                ->where('datetime >=', $fromDate)
                ->where('datetime <=', $toDate)
                ->get()->getResult();

            $pnlValues = array_map(fn($t) => (float) $t->pnl_amount, $trades);
            $totalTrades = count($pnlValues);
            $wins = count(array_filter($pnlValues, fn($p) => $p > 0));

            switch ($goal->goal_type) {
                case 'exact':
                    $actual = array_sum($pnlValues);
                    break;

                case 'highest':
                    $actual = count($pnlValues) > 0 ? max($pnlValues) : 0;
                    break;

                case 'average':
                    if ($isThresholdCrossed && $totalTrades > 0) {
                        $days = max(1, (strtotime($now) - strtotime($fromDate)) / 86400);
                        $actual = round(array_sum($pnlValues) / $days, 2);
                    }
                    break;

                case 'winrate':
                    if ($isThresholdCrossed && $totalTrades > 0) {
                        $actual = round(($wins / $totalTrades) * 100, 2);
                    }
                    break;
            }

            if ($actual >= (float) $goal->target_value && $actual > 0) {
                $goalAchieved = true;
            }

            $previousStatus = (int) $goal->status;
            $updateData = ['actual_value' => $actual];

            if ($now <= $endDate && !$goalAchieved) {
                $status = 1; // active
            } elseif ($goalAchieved) {
                $status = 2; // completed
                if ($previousStatus !== 2) {
                    $updateData['completed_at'] = $now;
                }
            } else {
                $status = 3; // failed
            }

            if ($previousStatus !== $status) {
                $updateData['status'] = $status;
            }

            if (!empty($updateData)) {
                $db->table('pnl_goals')
                    ->where('id', $goal->id)
                    ->update($updateData);
            }
        }

        return $this->response->setJSON(['status' => 'success']);
    }

    public function getPnlGoalDashboardStats()
    {
        $user_id = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Active Goals
        $activeGoals = $this->pnlgoalsmodel->where('user_id', $user_id)
            ->where('status', 1)
            ->where('deleted_at', null)
            ->findAll();
        $activeCount = count($activeGoals);

        // New this week
        $startOfWeek = date('Y-m-d', strtotime('monday this week'));
        $newThisWeek = $this->pnlgoalsmodel->where('user_id', $user_id)
            ->where('created_at >=', $startOfWeek)
            ->where('deleted_at', null)
            ->countAllResults();

        // Completion Rate
        $totalGoals = $this->pnlgoalsmodel->where('user_id', $user_id)
            ->where('deleted_at', null)
            ->countAllResults();
        $completedGoals = $this->pnlgoalsmodel->where('user_id', $user_id)
            ->where('status', 2)
            ->where('deleted_at', null)
            ->countAllResults();
        $completionRate = $totalGoals > 0 ? round(($completedGoals / $totalGoals) * 100) : 0;

        // Avg Daily PnL (last 30 days)
        $from = date('Y-m-d', strtotime('-30 days'));
        $to = date('Y-m-d');
        $trades = $this->trademodel->where('user_id', $user_id)
            ->where('deleted_at', null)
            ->where('datetime >=', $from)
            ->where('datetime <=', $to)
            ->findAll();

        $dailyPnl = [];
        foreach ($trades as $trade) {
            $date = date('Y-m-d', strtotime($trade['datetime']));
            if (!isset($dailyPnl[$date])) {
                $dailyPnl[$date] = 0;
            }
            $dailyPnl[$date] += $trade['pnl_amount'];
        }

        $avgDailyPnl = count($dailyPnl) ? round(array_sum($dailyPnl) / count($dailyPnl)) : 0;

        // Streak
        $streak = $this->calculatePnlStreak($user_id);

        return $this->response->setJSON([
            'active_goals' => $activeCount,
            'new_this_week' => $newThisWeek,
            'completion_rate' => $completionRate,
            'avg_daily_pnl' => $avgDailyPnl,
            'streak_days' => $streak['current_streak'],
            'to_best_streak' => $streak['to_best'],
        ]);
    }

    private function calculatePnlStreak($user_id)
    {
        $trades = $this->trademodel->where('user_id', $user_id)
            ->where('deleted_at', null)
            ->orderBy('datetime', 'DESC')
            ->findAll();

        $today = date('Y-m-d');
        $streak = 0;
        $best = 0;
        $prevDate = $today;

        foreach ($trades as $trade) {
            $tradeDate = date('Y-m-d', strtotime($trade['datetime']));
            if ($trade['pnl_amount'] > 0) {
                if ($tradeDate == $prevDate || date('Y-m-d', strtotime($prevDate . ' -1 day')) == $tradeDate) {
                    $streak++;
                    $prevDate = $tradeDate;
                    $best = max($best, $streak);
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        return [
            'current_streak' => $streak,
            'to_best' => max(0, $best - $streak)
        ];
    }

    public function getActiveGoals()
    {
        $user_id = $this->decrypt_cookie_value(get_cookie('user_session'));

        $goals = $this->pnlgoalsmodel->where('user_id', $user_id)
            ->where('status', 1) // active only
            ->where('deleted_at', null)
            ->findAll();

        $response = [];
        $now = time();

        foreach ($goals as $goal) {
            $target = (float) $goal['target_value'];
            $dateRange = $this->calculateDateRange($goal);
            $from = $dateRange['from'];
            $to = $dateRange['to'];

            // ⏳ Filter expired goals even if status is 1
            if (strtotime($to) < $now) {
                continue;
            }

            // ⏱️ 30% threshold logic
            $duration = strtotime($to) - strtotime($from);
            $thresholdCrossed = ($now - strtotime($from)) >= ($duration * 0.3);

            $daysLeft = max(0, round(($duration - ($now - strtotime($from))) / (60 * 60 * 24)));

            $trades = $this->trademodel->where('user_id', $user_id)
                ->where('deleted_at', null)
                ->where('datetime >=', $from)
                ->where('datetime <=', $to)
                ->findAll();

            $pnlValues = array_column($trades, 'pnl_amount');
            $actual = 0;

            switch ($goal['goal_type']) {
                case 'exact':
                    $actual = array_sum($pnlValues);
                    break;

                case 'highest':
                    $actual = count($pnlValues) > 0 ? max($pnlValues) : 0;
                    break;

                case 'average':
                    if ($thresholdCrossed && count($pnlValues) > 0) {
                        $daysPassed = max(1, round(($now - strtotime($from)) / (60 * 60 * 24)));
                        $actual = round(array_sum($pnlValues) / $daysPassed, 2);
                    }
                    break;

                case 'winrate':
                    if ($thresholdCrossed && count($pnlValues) > 0) {
                        $wins = count(array_filter($pnlValues, fn($val) => $val > 0));
                        $actual = round(($wins / count($pnlValues)) * 100, 2);
                    }
                    break;
            }

            $progress = $target > 0 ? min(round(($actual / $target) * 100), 100) : 0;
            $statusLabel = $progress >= 100 ? 'Completed' : ($progress >= 70 ? 'On track' : 'Needs work');
            $statusClass = $progress >= 100 ? 'status-complete' : ($progress >= 70 ? 'status-on-track' : 'status-pending');

            $response[] = [
                'goal_name' => $goal['goal_name'],
                'timeframe' => ucfirst($goal['timeframe']),
                'goal_type' => ucfirst($goal['goal_type']),
                'target' => round($target),
                'actual' => round($actual),
                'progress' => $progress,
                'days_left' => (int) $daysLeft,
                'status_label' => $statusLabel,
                'status_class' => $statusClass,
            ];
        }

        return $this->response->setJSON(['goals' => $response]);
    }


    private function calculateDateRange($goal)
    {
        $from = date('Y-m-d', strtotime($goal['created_at']));
        $period = $goal['custom_period'] ?? '';
        $number = (int) $goal['custom_number'];

        switch ($goal['timeframe']) {
            case 'weekly':
                $to = date('Y-m-d', strtotime($from . ' +7 days'));
                break;
            case 'monthly':
                $to = date('Y-m-d', strtotime($from . ' +1 month'));
                break;
            case 'quarterly':
                $to = date('Y-m-d', strtotime($from . ' +3 months'));
                break;
            case 'halfyear':
                $to = date('Y-m-d', strtotime($from . ' +6 months'));
                break;
            case 'yearly':
                $to = date('Y-m-d', strtotime($from . ' +1 year'));
                break;
            case 'custom':
                if (in_array($period, ['days', 'weeks', 'months']) && $number > 0) {
                    $to = date('Y-m-d', strtotime("$from +$number $period"));
                } else {
                    $to = $from;
                }
                break;
            default:
                $to = $from;
        }

        return ['from' => $from, 'to' => $to];
    }

}