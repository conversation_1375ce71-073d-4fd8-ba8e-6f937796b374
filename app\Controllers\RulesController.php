<?php

namespace App\Controllers;

use App\Models\RulesModel;
use App\Models\UserModel;

class RulesController extends BaseController
{
    protected $rulesModel;
    protected $userModel;

    public function __construct()
    {
        $this->rulesModel = new RulesModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display the rules page
     */
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        $data['title'] = 'Trading Rules';
        $data['active'] = 'rules';
        $data['userDetails'] = $this->userModel->find($userId);
        $data['customScript'] = 'rules';
        $data['main_content'] = 'pages/rules';

        return view('includes/template', $data);
    }

    /**
     * Get all rules for the current user
     */
    public function getRules()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            $rules = $this->rulesModel->getRulesForUser($userId);

            return $this->response->setJSON([
                'success' => true,
                'rules' => $rules
            ]);
        } catch (\Exception $e) {
            log_message('error', 'getRules error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Simple test endpoint
     */
    public function testRules()
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Rules endpoint is working',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $userId ? $userId : 'Not authenticated',
            'cookie_exists' => $this->request->getCookie('user_session') ? 'Yes' : 'No'
        ]);
    }

    /**
     * Create a new rule
     */
    public function createRule()
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $name = $this->request->getPost('name');
        $description = $this->request->getPost('description');
        $category = $this->request->getPost('category');

        if (empty($name)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rule name is required'
            ]);
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|max_length[255]',
            'description' => 'permit_empty|max_length[1000]',
            'category' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        try {
            // Check database structure
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');

            if (in_array('name', $fields) && in_array('description', $fields) && in_array('category', $fields)) {
                // New structure
                $data = [
                    'user_id' => $userId,
                    'name' => $name,
                    'description' => $description,
                    'category' => $category ?: 'general',
                    'is_active' => 1
                ];

                $ruleId = $this->rulesModel->insert($data);
            } else {
                // Old structure - combine name and description into rule field
                // Include category information in a detectable format
                $ruleText = $name;
                if (!empty($description)) {
                    $ruleText .= ' - ' . $description;
                }

                // Add category marker for better detection
                if (!empty($category) && $category !== 'general') {
                    $ruleText .= ' [' . $category . ']';
                }

                // Get the next available ID for old structure
                $maxIdQuery = $db->query("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM rules");
                $maxIdResult = $maxIdQuery->getRow();
                $nextId = $maxIdResult->next_id;

                // For old structure, keep it simple - just id and rule
                $data = [
                    'id' => $nextId,
                    'rule' => $ruleText
                ];

                try {
                    $result = $db->table('rules')->insert($data);
                    $ruleId = $result ? $nextId : false;

                    if (!$result) {
                        log_message('error', 'Failed to insert rule. Data: ' . json_encode($data));
                        log_message('error', 'Database error: ' . $db->error()['message']);
                    }
                } catch (\Exception $insertException) {
                    log_message('error', 'Insert exception: ' . $insertException->getMessage());
                    log_message('error', 'Insert data: ' . json_encode($data));
                    $ruleId = false;
                }
            }

            if ($ruleId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Rule created successfully',
                    'rule_id' => $ruleId
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create rule',
                    'errors' => $this->rulesModel->errors()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'createRule error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get a single rule for editing
     */
    public function getRule($id)
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        try {
            // Check database structure
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');
            $hasNewStructure = in_array('name', $fields) && in_array('category', $fields);

            if ($hasNewStructure) {
                // Use model for new structure
                $rule = $this->rulesModel->getRuleById($id, $userId);
            } else {
                // Direct query for old structure (no deleted_at column)
                $ruleQuery = $db->query("SELECT * FROM rules WHERE id = ?", [$id]);
                $rule = $ruleQuery->getRowArray();

                if ($rule) {
                    // Parse the rule text to extract components
                    $parsedRule = $this->parseRuleText($rule['rule']);

                    // Transform to expected format
                    $rule = [
                        'id' => $rule['id'],
                        'name' => $parsedRule['name'],
                        'description' => $parsedRule['description'],
                        'category' => $parsedRule['category'],
                        'created_at' => $rule['created_at'],
                        'updated_at' => $rule['updated_at']
                    ];
                }
            }

            if (!$rule) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Rule not found'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'rule' => $rule
            ]);
        } catch (\Exception $e) {
            log_message('error', 'getRule error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update an existing rule
     */
    public function updateRule($id)
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));
        
        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        try {
            // Check database structure
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');

            $validation = \Config\Services::validation();
            $validation->setRules([
                'name' => 'required|max_length[255]',
                'description' => 'permit_empty|max_length[1000]',
                'category' => 'permit_empty|max_length[100]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validation->getErrors()
                ]);
            }

            $name = $this->request->getPost('name');
            $description = $this->request->getPost('description');
            $category = $this->request->getPost('category');

            if (in_array('name', $fields) && in_array('description', $fields) && in_array('category', $fields)) {
                // New structure
                $rule = $this->rulesModel->find($id);
                if (!$rule) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Rule not found'
                    ]);
                }

                $data = [
                    'name' => $name,
                    'description' => $description,
                    'category' => $category ?: 'general'
                ];

                $success = $this->rulesModel->update($id, $data);
            } else {
                // Old structure - combine name and description into rule field
                $ruleText = $name;
                if (!empty($description)) {
                    $ruleText .= ' - ' . $description;
                }

                $data = [
                    'rule' => $ruleText,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $success = $db->table('rules')->where('id', $id)->update($data);
            }

            if ($success) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Rule updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update rule'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'updateRule error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a rule
     */
    public function deleteRule($id)
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            if ($id === null || $id === '' || !is_numeric($id)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid rule ID'
                ]);
            }

            // Check database structure and handle deletion accordingly
            $db = \Config\Database::connect();
            $fields = $db->getFieldNames('rules');

            // Check if rule exists first
            $existingRule = $db->query("SELECT * FROM rules WHERE id = ?", [$id])->getRowArray();

            if (!$existingRule) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Rule not found'
                ]);
            }

            // Always use hard delete for simplicity (works with both old and new structures)
            $success = $db->table('rules')->where('id', $id)->delete();

            if ($success) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Rule deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete rule'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get rules performance analytics
     */
    public function getRulesAnalytics()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            // Get real analytics data from trade_rules table
            $db = \Config\Database::connect();

            // Get overall statistics
            $totalRuleApplications = $db->table('trade_rules tr')
                ->join('trades t', 't.id = tr.trade_id')
                ->where('t.user_id', $userId)
                ->where('t.deleted_at IS NULL')
                ->countAllResults();

            $totalFollowed = $db->table('trade_rules tr')
                ->join('trades t', 't.id = tr.trade_id')
                ->where('t.user_id', $userId)
                ->where('t.deleted_at IS NULL')
                ->where('tr.followed', 1)
                ->countAllResults();

            $totalViolated = $totalRuleApplications - $totalFollowed;
            $consistency = $totalRuleApplications > 0 ? round(($totalFollowed / $totalRuleApplications) * 100) : 0;

            // Get all rules with usage statistics
            $rulesQuery = $db->query("
                SELECT r.rule as name, r.id,
                       COUNT(tr.id) as usage_count,
                       SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                       SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                       CASE
                           WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                           ELSE 0
                       END as adherence_rate,
                       AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_when_followed
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE r.deleted_at IS NULL
                GROUP BY r.id, r.rule
                ORDER BY followed_count DESC, adherence_rate DESC
            ", [$userId]);

            $allRules = $rulesQuery->getResultArray();

            // Transform rules to include category detection
            foreach ($allRules as &$rule) {
                $rule['category'] = $this->detectCategoryFromRuleName($rule['name']);
            }

            // Get most followed rules (top 5 with usage)
            $mostFollowed = array_filter($allRules, function($rule) {
                return $rule['usage_count'] > 0;
            });
            $mostFollowed = array_slice($mostFollowed, 0, 5);

            // Get least used rules (bottom rules with low adherence or no usage)
            $leastUsed = array_filter($allRules, function($rule) {
                return $rule['usage_count'] == 0 || $rule['adherence_rate'] < 50;
            });
            // Sort by adherence rate ascending (worst first)
            usort($leastUsed, function($a, $b) {
                if ($a['usage_count'] == 0 && $b['usage_count'] == 0) return 0;
                if ($a['usage_count'] == 0) return -1;
                if ($b['usage_count'] == 0) return 1;
                return $a['adherence_rate'] <=> $b['adherence_rate'];
            });
            $leastUsed = array_slice($leastUsed, 0, 3);

            // Get impact data for all rules with usage (using existing table structure)
            $impactQuery = $db->query("
                SELECT r.rule as name, r.id,
                       COUNT(tr.id) as total_usage,
                       SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                       SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                       AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_followed,
                       AVG(CASE WHEN tr.followed = 0 THEN t.pnl_amount ELSE NULL END) as avg_pnl_violated
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE r.deleted_at IS NULL
                GROUP BY r.id, r.rule
                HAVING total_usage > 0
                ORDER BY followed_count DESC
            ", [$userId]);

            $impact = $impactQuery->getResultArray();

            // Calculate impact scores (based on adherence rate and P&L impact)
            $impactScores = [];
            foreach ($impact as $rule) {
                $adherenceRate = $rule['total_usage'] > 0 ? ($rule['followed_count'] / $rule['total_usage']) * 100 : 0;
                $pnlImpact = ($rule['avg_pnl_followed'] ?? 0) - ($rule['avg_pnl_violated'] ?? 0);
                $score = ($adherenceRate * 0.6) + (min(max($pnlImpact / 100, 0), 40)); // Score out of 100

                $impactScores[] = [
                    'name' => $rule['name'],
                    'score' => round($score, 1),
                    'avg_pnl_followed' => round($rule['avg_pnl_followed'] ?? 0, 2),
                    'avg_pnl_violated' => round($rule['avg_pnl_violated'] ?? 0, 2),
                    'followed_count' => (int)($rule['followed_count'] ?? 0),
                    'violated_count' => (int)($rule['violated_count'] ?? 0),
                    'total_usage' => (int)($rule['total_usage'] ?? 0),
                    'adherence_rate' => round($adherenceRate, 1)
                ];
            }

            // Sort by score descending
            usort($impactScores, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            $analyticsData = [
                'overall' => [
                    'total_followed' => $totalFollowed,
                    'total_rule_applications' => $totalRuleApplications,
                    'total_violated' => $totalViolated
                ],
                'consistency' => $consistency,
                'mostFollowed' => $mostFollowed,
                'leastUsed' => $leastUsed,
                'impact' => $impact,
                'impactScores' => array_slice($impactScores, 0, 10) // Top 10 rules by impact score
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $analyticsData
            ]);
        } catch (\Exception $e) {
            log_message('error', 'getRulesAnalytics error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading analytics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get rules with their usage statistics
     */
    public function getRulesWithStats()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            // Get rules with real usage statistics
            $db = \Config\Database::connect();

            // Check database structure to determine which fields to select
            $fields = $db->getFieldNames('rules');
            $hasNewStructure = in_array('name', $fields) && in_array('category', $fields);

            if ($hasNewStructure) {
                // New structure with separate name, description, category fields
                $rulesQuery = $db->query("
                    SELECT r.id, r.name, r.description, r.category, r.created_at, r.updated_at,
                           COUNT(tr.id) as usage_count,
                           SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                           SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                           CASE
                               WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                               ELSE 0
                           END as adherence_rate,
                           AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_when_followed
                    FROM rules r
                    LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                    LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ?
                    WHERE r.deleted_at IS NULL
                    GROUP BY r.id, r.name, r.description, r.category, r.created_at, r.updated_at
                    ORDER BY usage_count DESC, r.name ASC
                ", [$userId]);
            } else {
                // Old structure with combined rule field (no deleted_at column)
                $rulesQuery = $db->query("
                    SELECT r.id, r.rule as name, r.created_at, r.updated_at,
                           COUNT(tr.id) as usage_count,
                           SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                           SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                           CASE
                               WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                               ELSE 0
                           END as adherence_rate,
                           AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_when_followed
                    FROM rules r
                    LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                    LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ?
                    GROUP BY r.id, r.rule, r.created_at, r.updated_at
                    ORDER BY usage_count DESC, r.rule ASC
                ", [$userId]);
            }

            $transformedRules = [];
            foreach ($rulesQuery->getResultArray() as $rule) {
                if ($hasNewStructure) {
                    // Use actual category from database
                    $category = $rule['category'] ?: 'general';
                    $description = $rule['description'] ?: '';
                } else {
                    // Parse rule text to extract name, description, and category for old structure
                    $parsedRule = $this->parseRuleText($rule['name']);
                    $category = $parsedRule['category'];
                    $description = $parsedRule['description'];
                    // Update the name to be just the clean name without description/category
                    $rule['name'] = $parsedRule['name'];
                }

                $transformedRules[] = [
                    'id' => $rule['id'],
                    'name' => $rule['name'],
                    'description' => $description,
                    'category' => $category,
                    'user_id' => null, // All rules are global in old structure
                    'is_active' => 1,
                    'created_at' => $rule['created_at'],
                    'updated_at' => $rule['updated_at'],
                    'usage_count' => (int)$rule['usage_count'],
                    'followed_count' => (int)$rule['followed_count'],
                    'violated_count' => (int)$rule['violated_count'],
                    'adherence_rate' => (float)$rule['adherence_rate'],
                    'avg_pnl_when_followed' => $rule['avg_pnl_when_followed'] ? round((float)$rule['avg_pnl_when_followed'], 2) : 0
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'rules' => $transformedRules
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Parse rule text to extract name, description, and category
     */
    private function parseRuleText($ruleText)
    {
        $originalText = $ruleText;
        $name = $ruleText;
        $description = '';
        $category = 'general';

        // Extract category from [category] marker
        if (preg_match('/\[([^\]]+)\]/', $ruleText, $matches)) {
            $category = strtolower(trim($matches[1]));
            // Remove category marker from text
            $ruleText = preg_replace('/\s*\[([^\]]+)\]/', '', $ruleText);

            // Validate category
            $validCategories = ['entry', 'exit', 'risk_management', 'psychology', 'analysis', 'custom'];
            if (!in_array($category, $validCategories)) {
                $category = 'general';
            }
        }

        // Extract description (text after " - ")
        if (strpos($ruleText, ' - ') !== false) {
            $parts = explode(' - ', $ruleText, 2);
            $name = trim($parts[0]);
            $description = trim($parts[1]);
        } else {
            $name = trim($ruleText);
        }

        // If no category was found in markers, try to detect from the text
        if ($category === 'general') {
            $category = $this->detectCategoryFromRuleName($originalText);
        }

        return [
            'name' => $name,
            'description' => $description,
            'category' => $category
        ];
    }

    /**
     * Detect category from rule name using keywords
     */
    private function detectCategoryFromRuleName($ruleName)
    {
        $originalRuleName = $ruleName;
        $ruleName = strtolower($ruleName);

        // First check for explicit category markers [category]
        if (preg_match('/\[([^\]]+)\]/', $originalRuleName, $matches)) {
            $category = strtolower(trim($matches[1]));
            // Map category names to valid categories
            $validCategories = ['entry', 'exit', 'risk_management', 'psychology', 'analysis', 'custom'];
            if (in_array($category, $validCategories)) {
                return $category;
            }
        }

        // Entry-related keywords
        $entryKeywords = ['entry', 'enter', 'buy', 'long', 'short', 'breakout', 'confirmation', 'signal', 'setup', 'pattern', 'wait for', 'candle close'];

        // Exit-related keywords
        $exitKeywords = ['exit', 'sell', 'close', 'profit', 'target', 'take profit', 'book profit', 'square off'];

        // Risk management keywords
        $riskKeywords = ['risk', 'stop', 'loss', 'stoploss', 'stop loss', 'capital', 'size', 'position', 'money', 'management', 'respect'];

        // Psychology keywords
        $psychologyKeywords = ['revenge', 'emotion', 'fear', 'greed', 'discipline', 'patience', 'calm', 'psychology', 'mental', 'feeling', 'avoid'];

        // Analysis keywords
        $analysisKeywords = ['analysis', 'analyze', 'timeframe', 'chart', 'technical', 'fundamental', 'research', 'study', 'check', 'higher'];

        // Check each category
        foreach ($entryKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'entry';
            }
        }

        foreach ($exitKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'exit';
            }
        }

        foreach ($riskKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'risk_management';
            }
        }

        foreach ($psychologyKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'psychology';
            }
        }

        foreach ($analysisKeywords as $keyword) {
            if (strpos($ruleName, $keyword) !== false) {
                return 'analysis';
            }
        }

        // Default to general if no keywords match
        return 'general';
    }
}
