<?php

namespace App\Models;

use CodeIgniter\Model;

class MistakeModel extends Model
{
    protected $table = 'mistakes';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $useSoftDeletes = false;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $allowedFields = [
        'name',
        'category',
        'description',
        'severity',
        'impact',
        'icon',
        'color_class',
        'is_active'
    ];

    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'category' => 'required|max_length[50]',
        'severity' => 'required|in_list[Low,Medium,High]',
        'impact' => 'required|in_list[Minor,Moderate,Critical]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Mistake name is required',
            'max_length' => 'Mistake name cannot exceed 100 characters'
        ],
        'category' => [
            'required' => 'Category is required',
            'max_length' => 'Category cannot exceed 50 characters'
        ]
    ];

    /**
     * Get all active mistakes
     */
    public function getActiveMistakes()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get mistakes by category
     */
    public function getMistakesByCategory($category)
    {
        return $this->where('category', $category)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Get mistake statistics for a user
     */
    public function getUserMistakeStats($userId)
    {
        $db = \Config\Database::connect();
        
        return $db->query("
            SELECT 
                m.id,
                m.name,
                m.category,
                m.severity,
                m.impact,
                COUNT(tm.id) as frequency,
                MAX(t.datetime) as last_occurrence
            FROM mistakes m
            LEFT JOIN trade_mistakes tm ON m.id = tm.mistake_id
            LEFT JOIN trades t ON tm.trade_id = t.id AND t.user_id = ? AND t.deleted_at IS NULL
            WHERE m.is_active = 1
            GROUP BY m.id, m.name, m.category, m.severity, m.impact
            ORDER BY frequency DESC, m.name ASC
        ", [$userId])->getResultArray();
    }

    /**
     * Get mistake frequency by time period
     */
    public function getMistakeFrequencyByPeriod($userId, $period = 'week')
    {
        $db = \Config\Database::connect();
        
        $dateFormat = match($period) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%u'
        };

        return $db->query("
            SELECT 
                DATE_FORMAT(t.datetime, ?) as period,
                COUNT(tm.id) as mistake_count
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL
            GROUP BY DATE_FORMAT(t.datetime, ?)
            ORDER BY period DESC
            LIMIT 10
        ", [$dateFormat, $userId, $dateFormat])->getResultArray();
    }

    /**
     * Get most common mistakes for a user
     */
    public function getMostCommonMistakes($userId, $limit = 5)
    {
        $db = \Config\Database::connect();
        
        return $db->query("
            SELECT 
                m.name,
                m.category,
                m.severity,
                m.impact,
                m.icon,
                m.color_class,
                COUNT(tm.id) as frequency
            FROM mistakes m
            JOIN trade_mistakes tm ON m.id = tm.mistake_id
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL AND m.is_active = 1
            GROUP BY m.id, m.name, m.category, m.severity, m.impact, m.icon, m.color_class
            ORDER BY frequency DESC
            LIMIT ?
        ", [$userId, $limit])->getResultArray();
    }

    /**
     * Get mistake trends (improvement/decline)
     */
    public function getMistakeTrends($userId, $mistakeId = null)
    {
        $db = \Config\Database::connect();
        
        $mistakeFilter = $mistakeId ? "AND tm.mistake_id = $mistakeId" : "";
        
        return $db->query("
            SELECT 
                DATE_FORMAT(t.datetime, '%Y-%m') as month,
                COUNT(tm.id) as mistake_count
            FROM trade_mistakes tm
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL $mistakeFilter
            AND t.datetime >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(t.datetime, '%Y-%m')
            ORDER BY month ASC
        ", [$userId])->getResultArray();
    }

    /**
     * Get mistakes by severity level
     */
    public function getMistakesBySeverity($userId)
    {
        $db = \Config\Database::connect();
        
        return $db->query("
            SELECT 
                m.severity,
                COUNT(tm.id) as count
            FROM mistakes m
            JOIN trade_mistakes tm ON m.id = tm.mistake_id
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL AND m.is_active = 1
            GROUP BY m.severity
            ORDER BY 
                CASE m.severity 
                    WHEN 'High' THEN 3 
                    WHEN 'Medium' THEN 2 
                    WHEN 'Low' THEN 1 
                END DESC
        ", [$userId])->getResultArray();
    }

    /**
     * Get mistake impact analysis
     */
    public function getMistakeImpactAnalysis($userId)
    {
        $db = \Config\Database::connect();
        
        return $db->query("
            SELECT 
                m.name,
                m.impact,
                COUNT(tm.id) as frequency,
                AVG(t.pnl_amount) as avg_pnl_impact
            FROM mistakes m
            JOIN trade_mistakes tm ON m.id = tm.mistake_id
            JOIN trades t ON tm.trade_id = t.id
            WHERE t.user_id = ? AND t.deleted_at IS NULL AND m.is_active = 1
            GROUP BY m.id, m.name, m.impact
            ORDER BY avg_pnl_impact ASC
        ", [$userId])->getResultArray();
    }
}
