<?php
namespace App\Controllers;

use App\Models\UserModel;
use App\Models\TradeModel; // Assuming your trade model is named TradeModel

use App\Libraries\DhanApi;
use CodeIgniter\Controller;
use CodeIgniter\CLI\CLI; // Useful if running via Spark command

helper('cookie'); // Ensure cookie helper is loaded

class TradeFetcher extends BaseController
{

    /**
     * Fetches trades for the currently logged-in user (identified by session cookie).
     * This method can be called via a web route or a Spark command.
     */
    public function fetchCurrentUserTrades()
    {
        $userModel = new UserModel();
        $dhanTradeModel = new TradeModel(); // Use your TradeModel

        $currentUserId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$currentUserId) {
            if ($this->request->isCLI()) {
                CLI::write('No valid user session found.', 'red');
            } else {
                return $this->response->setJSON(['status' => 'error', 'message' => 'No valid user session found or user not logged in.']);
            }
            return;
        }

        // Retrieve the specific user by their ID
        $user = $userModel->where('id', $currentUserId)
                           ->where('access_token IS NOT NULL') // Check if Dhan API is configured
                           ->where('client_id IS NOT NULL')
                           ->first(); // Use first() as we expect only one user

        if (empty($user)) {
            if ($this->request->isCLI()) {
                CLI::write('User not found or Dhan API details not configured.', 'yellow');
            } else {
                return $this->response->setJSON(['status' => 'error', 'message' => 'User not found or Dhan API details not configured for this user.']);
            }
            return;
        }

        // Initialize counters for reporting
        $totalNewTradesSaved = 0;

        $userId = $user['id'];
        
        // IMPORTANT: Decrypt the access_token if it's stored encrypted in the database.
        // Assuming UserModel has a decryptAccessToken method as suggested previously.
        // If not, and it's stored plaintext, this line would be correct but insecure.
        $decryptedAccessToken = $user['access_token']; 
        // If your UserModel encrypts it, change above to:
        // $decryptedAccessToken = $userModel->decryptAccessToken($user['access_token']);

        $dhanClientId = $user['client_id'];

        // Log current user processing (useful for debugging, especially in CLI)
        if ($this->request->isCLI()) {
            CLI::write("--- Processing trades for user ID: {$userId} (Dhan Client ID: {$dhanClientId}) ---", 'cyan');
        } else {
            log_message('info', "Processing trades for user ID: {$userId} (Dhan Client ID: {$dhanClientId})");
        }

        // Instantiate Dhan API library for the current user
        $dhanApi = new DhanApi($decryptedAccessToken, $dhanClientId);
        $trades = $dhanApi->getTrades();

        if ($trades) {
            foreach ($trades as $trade) {
                // IMPORTANT: Re-enable the duplicate trade check
                // Use 'exchangeTradeId' as a unique identifier from Dhan, combined with 'user_id'
                $existingTrade = $dhanTradeModel->where('user_id', $userId)
                                                 ->where('exchange_trade_id', $trade['exchangeTradeId'])
                                                 ->first();

                if (!$existingTrade) {
                    // Prepare data for insertion into your 'dhan_trades' table
                    $dataToSave = [
                        'user_id'           => $userId,
                        'dhan_client_id'    => $trade['dhanClientId'],
                        'order_id'          => $trade['orderId'] ?? null,
                        'exchange_order_id' => $trade['exchangeOrderId'] ?? null,
                        'exchange_trade_id' => $trade['exchangeTradeId'],
                        'transaction_type'  => $trade['transactionType'],
                        'exchange_segment'  => $trade['exchangeSegment'],
                        'product_type'      => $trade['productType'],
                        'order_type'        => $trade['orderType'] ?? null,
                        'trading_symbol'    => $trade['tradingSymbol'],
                        'security_id'       => $trade['securityId'],
                        'quantity'          => $trade['tradedQuantity'],
                        'price'             => $trade['tradedPrice'],
                        'trade_time'        => date('Y-m-d H:i:s', strtotime($trade['createTime'])),
                        'raw_data'          => json_encode($trade)
                    ];

                    // REMOVE THIS DEBUG LINE: echo '<pre>';print_r($dataToSave);exit;

                    if ($dhanTradeModel->insert($dataToSave)) {
                        $totalNewTradesSaved++;
                        if ($this->request->isCLI()) {
                            CLI::write("  - Saved new trade: {$trade['exchangeTradeId']} for {$trade['tradingSymbol']}", 'light_green');
                        }
                    } else {
                        $errors = $dhanTradeModel->errors();
                        $errorMsg = is_array($errors) ? implode(', ', $errors) : 'Unknown error';
                        log_message('error', 'Failed to save trade for user ' . $userId . ' (Trade ID: ' . ($trade['exchangeTradeId'] ?? 'N/A') . '): ' . $errorMsg);
                        if ($this->request->isCLI()) {
                            CLI::error("  - Failed to save trade {$trade['exchangeTradeId']}: {$errorMsg}");
                        }
                    }
                } else {
                    if ($this->request->isCLI()) {
                        CLI::write("  - Trade {$trade['exchangeTradeId']} already exists for user {$userId}, skipping.", 'yellow');
                    }
                }
            }
            if ($this->request->isCLI()) {
                CLI::write("Finished processing for user ID: {$userId}. Saved {$totalNewTradesSaved} new trades.", 'green');
            } else {
                log_message('info', "Finished processing for user ID: {$userId}. Saved {$totalNewTradesSaved} new trades.");
            }
        } else {
            if ($this->request->isCLI()) {
                CLI::write("Could not fetch trades for user ID: {$userId}.", 'red');
            } else {
                log_message('warning', "Could not fetch trades for user ID: {$userId}.");
            }
        }

        // Final response/summary
        if ($this->request->isCLI()) {
            CLI::write('--- Dhan trades fetch process completed ---', 'green');
            CLI::write("Total new trades saved: {$totalNewTradesSaved}", 'green');
        } else {
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Trade fetching process completed.',
                'summary' => [
                    'user_id' => $userId,
                    'new_trades_saved' => $totalNewTradesSaved
                ]
            ]);
        }
    }
}