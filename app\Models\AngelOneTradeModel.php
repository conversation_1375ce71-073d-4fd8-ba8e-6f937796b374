<?php

namespace App\Models;

use CodeIgniter\Model;

class AngelOneTradeModel extends Model
{
    protected $table = 'angel_trades';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'user_id',
        'angel_client_id',
        'order_id',
        'exchange_order_id',
        'exchange_trade_id',
        'transaction_type',
        'exchange_segment',
        'product_type',
        'order_type',
        'trading_symbol',
        'symbol_token',
        'quantity',
        'price',
        'trade_value',
        'trade_date',
        'trade_time',
        'fill_id',
        'fill_time',
        'raw_data',
        'processed'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'angel_client_id' => 'required|max_length[100]',
        'exchange_trade_id' => 'required|max_length[100]',
        'transaction_type' => 'required|in_list[BUY,SELL]',
        'trading_symbol' => 'required|max_length[100]',
        'quantity' => 'required|decimal',
        'price' => 'required|decimal'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'exchange_trade_id' => [
            'required' => 'Exchange trade ID is required'
        ]
    ];

    /**
     * Get unprocessed trades for a user
     */
    public function getUnprocessedTrades($userId)
    {
        return $this->where('user_id', $userId)
                   ->where('processed', 0)
                   ->orderBy('trade_date', 'ASC')
                   ->orderBy('trade_time', 'ASC')
                   ->findAll();
    }

    /**
     * Mark trades as processed
     */
    public function markAsProcessed($tradeIds)
    {
        if (empty($tradeIds)) {
            return false;
        }

        return $this->whereIn('id', $tradeIds)
                   ->set('processed', 1)
                   ->set('updated_at', date('Y-m-d H:i:s'))
                   ->update();
    }

    /**
     * Get trades by symbol for a user
     */
    public function getTradesBySymbol($userId, $symbol, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->where('user_id', $userId)
                       ->where('trading_symbol', $symbol);

        if ($dateFrom) {
            $builder->where('trade_date >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('trade_date <=', $dateTo);
        }

        return $builder->orderBy('trade_date', 'ASC')
                      ->orderBy('trade_time', 'ASC')
                      ->findAll();
    }

    /**
     * Get trade statistics for a user
     */
    public function getTradeStats($userId, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->selectSum('quantity', 'total_quantity')
                       ->selectSum('trade_value', 'total_value')
                       ->selectCount('id', 'total_trades')
                       ->where('user_id', $userId);

        if ($dateFrom) {
            $builder->where('trade_date >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('trade_date <=', $dateTo);
        }

        return $builder->get()->getRowArray();
    }

    /**
     * Get unique symbols traded by user
     */
    public function getUniqueSymbols($userId, $limit = null)
    {
        $builder = $this->select('trading_symbol')
                       ->distinct()
                       ->where('user_id', $userId)
                       ->orderBy('trading_symbol', 'ASC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Check if trade exists by exchange trade ID
     */
    public function tradeExists($exchangeTradeId)
    {
        return $this->where('exchange_trade_id', $exchangeTradeId)
                   ->countAllResults() > 0;
    }

    /**
     * Get recent trades for a user
     */
    public function getRecentTrades($userId, $limit = 10)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('trade_date', 'DESC')
                   ->orderBy('trade_time', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Delete trades older than specified days
     */
    public function deleteOldTrades($days = 90)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->where('trade_date <', $cutoffDate)
                   ->where('processed', 1)
                   ->delete();
    }

    /**
     * Get trades grouped by date
     */
    public function getTradesGroupedByDate($userId, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->select('trade_date, COUNT(*) as trade_count, SUM(trade_value) as total_value')
                       ->where('user_id', $userId)
                       ->groupBy('trade_date')
                       ->orderBy('trade_date', 'DESC');

        if ($dateFrom) {
            $builder->where('trade_date >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('trade_date <=', $dateTo);
        }

        return $builder->findAll();
    }
}
