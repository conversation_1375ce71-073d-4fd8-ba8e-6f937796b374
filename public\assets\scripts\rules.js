// Rules Management JavaScript
let rulesData = [];
let impactChart = null;

$(document).ready(function () {
    console.log('Rules page loaded, base_url:', typeof base_url !== 'undefined' ? base_url : 'undefined');
    loadRules(); // This will now call loadRulesAnalytics() after rules are loaded
    initializeEventListeners();
});

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Modal controls
    $('#createRuleBtn, #addRuleCard').on('click', openCreateRuleModal);
    $('#closeModalBtn, #cancelRuleBtn').on('click', closeCreateRuleModal);

    // Form submission
    $('#ruleForm').on('submit', handleRuleFormSubmit);

    // Search functionality
    $('#searchRules').on('input', filterRules);

    // Filter buttons
    $('.filter-btn').on('click', handleFilterClick);

    // Delete rule functionality
    $(document).on('click', '.delete-rule-btn', handleDeleteRule);

    // Edit rule functionality
    $(document).on('click', '.edit-rule-btn', function() {
        const ruleId = $(this).data('rule-id');
        showEditRuleModal(ruleId);
    });
    $('#closeEditModalBtn, #cancelEditRuleBtn').on('click', hideEditRuleModal);
    $('#editRuleForm').on('submit', handleEditRuleFormSubmit);

    // Analytics controls
    $('#refreshAnalytics').on('click', function() {
        $(this).find('i').addClass('fa-spin');
        loadRulesAnalytics();
        setTimeout(() => {
            $(this).find('i').removeClass('fa-spin');
        }, 1000);
    });
    $('#analyticsTimeframe').on('change', loadRulesAnalytics);

    // Close modal when clicking outside
    $('#createRuleModal').on('click', function(e) {
        if (e.target === this) {
            closeCreateRuleModal();
        }
    });

    $('#editRuleModal').on('click', function(e) {
        if (e.target === this) {
            hideEditRuleModal();
        }
    });
}

/**
 * Load all rules with statistics
 */
function loadRules() {
    $.ajax({
        type: 'GET',
        url: base_url + 'getRulesWithStats',
        dataType: 'json',
        success: function(response) {
            console.log('Rules response:', response);
            if (response && response.success) {
                rulesData = response.rules || [];
                applyFilters(); // Apply current filters instead of showing all
                updateRulesCount(rulesData.length);
                updateFilterCounts(); // Update filter button counts

                // Load analytics after rules are loaded
                loadRulesAnalytics();
            } else {
                console.error('Invalid response:', response);
                showNotification('Error loading rules: ' + (response ? response.message : 'Invalid response'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error Details:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });

            let errorMessage = 'Failed to load rules';
            if (xhr.status === 404) {
                errorMessage = 'Rules endpoint not found (404)';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error (500)';
            } else if (status === 'timeout') {
                errorMessage = 'Request timed out';
            }

            showNotification(errorMessage, 'error');
        }
    });
}

/**
 * Render rules in the grid
 */
function renderRules(rules) {
    const rulesGrid = $('#rulesGrid');
    rulesGrid.empty();
    
    if (rules.length === 0) {
        let emptyMessage = 'No rules found. Create your first trading rule!';

        // Customize message based on current filter
        if (currentFilter === 'most-used') {
            emptyMessage = 'No frequently used rules. Start using your rules to see them here.';
        } else if (currentFilter === 'high-impact') {
            emptyMessage = 'No high-impact rules. Rules with 80%+ impact score will appear here.';
        } else if (currentFilter === 'new') {
            emptyMessage = 'No new rules. Rules created in the last 7 days will appear here.';
        } else if ($('#searchRules').val().trim()) {
            emptyMessage = 'No matching rules. Try adjusting your search terms.';
        }

        rulesGrid.html(`
            <div class="col-span-full text-center py-8">
                <i class="fas fa-list-check text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">${emptyMessage}</p>
            </div>
        `);
        return;
    }
    
    rules.forEach(rule => {
        const ruleCard = createRuleCard(rule);
        rulesGrid.append(ruleCard);
    });
}

/**
 * Create a rule card HTML with new design
 */
function createRuleCard(rule) {
    const adherenceRate = rule.adherence_rate || 0;
    const usageCount = rule.usage_count || 0;
    const followedCount = rule.followed_count || 0;
    const violatedCount = rule.violated_count || 0;

    // Get category info
    const category = rule.category || 'general';
    const categoryInfo = getCategoryInfo(category);

    // Get adherence status
    const adherenceInfo = getAdherenceInfo(adherenceRate);

    return `
        <div class="rule-card gradient-border bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm ${categoryInfo.shadowClass}" data-rule-id="${rule.id}">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center flex-1 min-w-0">
                    <div class="w-10 h-10 rounded-lg ${categoryInfo.gradient} flex items-center justify-center shadow-md mr-3 flex-shrink-0">
                        <i class="${categoryInfo.icon} text-white text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white truncate">${rule.name}</h3>
                    </div>
                </div>
                <span class="text-xs px-2.5 py-1 rounded-full ${categoryInfo.badge} ml-2 flex-shrink-0">${categoryInfo.label}</span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">${rule.description || 'No description available'}</p>
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full ${adherenceInfo.bgColor} flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="${adherenceInfo.icon} ${adherenceInfo.iconColor} text-xs"></i>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-xs text-gray-500 dark:text-gray-400">${adherenceRate}% adherence</span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">${followedCount} times followed${usageCount > 0 ? ` of ${usageCount}` : ''}</span>
                    </div>
                </div>
                <div class="flex space-x-1">
                    <button class="edit-rule-btn p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" data-rule-id="${rule.id}" title="Edit Rule">
                        <i class="fas fa-edit w-3.5 h-3.5"></i>
                    </button>
                    <button class="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-colors delete-rule-btn" data-rule-id="${rule.id}" title="Delete Rule">
                        <i class="fas fa-trash w-3.5 h-3.5"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Get category information for styling
 */
function getCategoryInfo(category) {
    const categories = {
        'entry': {
            icon: 'fas fa-chart-line',
            gradient: 'bg-gradient-to-br from-blue-500 to-indigo-600',
            badge: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            shadowClass: 'shadow-blue',
            label: 'Entry'
        },
        'exit': {
            icon: 'fas fa-sign-out-alt',
            gradient: 'bg-gradient-to-br from-green-500 to-teal-600',
            badge: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            shadowClass: 'shadow-green',
            label: 'Exit'
        },
        'risk_management': {
            icon: 'fas fa-shield-alt',
            gradient: 'bg-gradient-to-br from-red-500 to-pink-600',
            badge: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            shadowClass: 'shadow-red',
            label: 'Risk'
        },
        'psychology': {
            icon: 'fas fa-brain',
            gradient: 'bg-gradient-to-br from-purple-500 to-violet-600',
            badge: 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
            shadowClass: 'shadow-purple',
            label: 'Psychology'
        },
        'analysis': {
            icon: 'fas fa-search',
            gradient: 'bg-gradient-to-br from-indigo-500 to-purple-600',
            badge: 'bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200',
            shadowClass: 'shadow-indigo',
            label: 'Analysis'
        },
        'custom': {
            icon: 'fas fa-cog',
            gradient: 'bg-gradient-to-br from-orange-500 to-amber-600',
            badge: 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
            shadowClass: 'shadow-orange',
            label: 'Custom'
        },
        'general': {
            icon: 'fas fa-list-check',
            gradient: 'bg-gradient-to-br from-gray-500 to-slate-600',
            badge: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
            shadowClass: 'shadow-gray',
            label: 'General'
        }
    };

    return categories[category] || categories['general'];
}

/**
 * Get adherence information for styling
 */
function getAdherenceInfo(rate) {
    if (rate >= 80) {
        return {
            bgColor: 'bg-green-100 dark:bg-green-900',
            icon: 'fas fa-check',
            iconColor: 'text-green-500 dark:text-green-400'
        };
    } else if (rate >= 60) {
        return {
            bgColor: 'bg-yellow-100 dark:bg-yellow-900',
            icon: 'fas fa-exclamation',
            iconColor: 'text-yellow-500 dark:text-yellow-400'
        };
    } else {
        return {
            bgColor: 'bg-red-100 dark:bg-red-900',
            icon: 'fas fa-times',
            iconColor: 'text-red-500 dark:text-red-400'
        };
    }
}

/**
 * Get adherence rate color class (legacy function for compatibility)
 */
function getAdherenceColor(rate) {
    if (rate >= 90) return 'bg-emerald-100 dark:bg-emerald-900/40 text-emerald-700 dark:text-emerald-300';
    if (rate >= 70) return 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300';
    if (rate >= 50) return 'bg-amber-100 dark:bg-amber-900/40 text-amber-700 dark:text-amber-300';
    if (rate > 0) return 'bg-red-100 dark:bg-red-900/40 text-red-700 dark:text-red-300';
    return 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400';
}

/**
 * Format currency with rupee symbol
 */
function formatCurrency(num) {
    if (num === null || num === undefined || isNaN(num)) return '&#8377;0';
    const formatted = Math.abs(num).toLocaleString('en-IN', {maximumFractionDigits: 2});
    return `&#8377;${formatted}`;
}

/**
 * Format currency with sign and rupee symbol
 */
function formatCurrencyWithSign(num) {
    if (num === null || num === undefined || isNaN(num)) return '&#8377;0';
    const sign = num > 0 ? '+' : '';
    const formatted = Math.abs(num).toLocaleString('en-IN', {maximumFractionDigits: 2});
    return `${sign}&#8377;${formatted}`;
}

/**
 * Current active filter
 */
let currentFilter = 'all';

/**
 * Handle filter button clicks
 */
function handleFilterClick() {
    const filterType = $(this).data('filter');
    console.log('Filter button clicked:', filterType);

    // Update active button
    $('.filter-btn').removeClass('active');
    $(this).addClass('active');

    // Update current filter
    currentFilter = filterType;
    console.log('Current filter set to:', currentFilter);

    // Apply filters
    applyFilters();
}

/**
 * Apply current filters (search + category filter)
 */
function applyFilters() {
    console.log('Applying filters. Current filter:', currentFilter, 'Rules data:', rulesData);
    const searchTerm = $('#searchRules').val().toLowerCase();
    let filteredRules = rulesData;

    // Apply search filter
    if (searchTerm) {
        filteredRules = filteredRules.filter(rule =>
            rule.name.toLowerCase().includes(searchTerm) ||
            (rule.description && rule.description.toLowerCase().includes(searchTerm)) ||
            (rule.category && rule.category.toLowerCase().includes(searchTerm))
        );
        console.log('After search filter:', filteredRules.length, 'rules');
    }

    // Apply category filter
    filteredRules = applyRuleFilter(filteredRules, currentFilter);
    console.log('After category filter:', filteredRules.length, 'rules');

    renderRules(filteredRules);
}

/**
 * Filter rules based on search input
 */
function filterRules() {
    applyFilters();
}

/**
 * Apply rule filter based on type
 */
function applyRuleFilter(rules, filterType) {
    console.log('Applying filter:', filterType, 'to rules:', rules);

    switch (filterType) {
        case 'most-used':
            // Filter rules that have been used at least once, then sort by usage frequency
            const usedRules = rules.filter(rule => (rule.usage_count || 0) > 0);
            console.log('Used rules found:', usedRules.length, usedRules);

            if (usedRules.length === 0) {
                // If no rules have been used, show all rules sorted by usage_count anyway
                return rules.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
            }

            return usedRules.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));

        case 'high-impact':
            // Filter rules with high adherence rate (80%+) since we don't have impact_score
            const highImpactRules = rules.filter(rule => (rule.adherence_rate || 0) >= 80);
            console.log('High impact rules found:', highImpactRules.length, highImpactRules);
            return highImpactRules;

        case 'new':
            // Show recently created rules (last 7 days) - if created_at is available
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const newRules = rules.filter(rule => {
                if (rule.created_at) {
                    const createdDate = new Date(rule.created_at);
                    return createdDate >= sevenDaysAgo;
                }
                // If no created_at, consider all rules as potentially new
                return true;
            });
            console.log('New rules found:', newRules.length, newRules);
            return newRules;

        case 'all':
        default:
            console.log('Showing all rules:', rules.length);
            return rules;
    }
}

/**
 * Update rules count display
 */
function updateRulesCount(count) {
    $('#totalRulesCount').text(`(${count})`);
}

/**
 * Update filter button counts
 */
function updateFilterCounts() {
    if (!rulesData || rulesData.length === 0) return;

    // Update total count
    $('#totalRulesCount').text(`(${rulesData.length})`);

    // Note: Individual filter counts can be added here if needed in the future
    // For now, we just update the total count
}

/**
 * Open create rule modal
 */
function openCreateRuleModal() {
    $('#createRuleModal').removeClass('hidden');
    $('#ruleName').focus();
}

/**
 * Close create rule modal
 */
function closeCreateRuleModal() {
    $('#createRuleModal').addClass('hidden');
    $('#ruleForm')[0].reset();
}

/**
 * Handle rule form submission
 */
function handleRuleFormSubmit(e) {
    e.preventDefault();

    const formData = {
        name: $('#ruleName').val(),
        description: $('#ruleDescription').val(),
        category: $('#ruleCategory').val()
    };

    $.ajax({
        type: 'POST',
        url: base_url + 'createRule',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('Rule created successfully!', 'success');
                closeCreateRuleModal();
                loadRules();
                loadRulesAnalytics();
            } else {
                showNotification('Error: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('Failed to create rule', 'error');
        }
    });
}

/**
 * Handle delete rule
 */
function handleDeleteRule(e) {
    e.preventDefault();
    e.stopPropagation();

    const ruleId = $(this).data('rule-id');
    const ruleName = $(this).closest('.rule-card').find('h3').text().trim();

    if (!ruleId) {
        showNotification('Error: No rule ID found', 'error');
        return;
    }

    // Show confirmation dialog
    if (typeof showDialog === 'function') {
        showDialog(
            `Are you sure you want to delete the rule "${ruleName}"? This action cannot be undone.`,
            function() {
                deleteRule(ruleId);
            }
        );
    } else {
        // Fallback to confirm dialog
        if (confirm(`Are you sure you want to delete the rule "${ruleName}"? This action cannot be undone.`)) {
            deleteRule(ruleId);
        }
    }
}

/**
 * Delete rule
 */
function deleteRule(ruleId) {
    $.ajax({
        type: 'POST',
        url: base_url + 'deleteRule/' + ruleId,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('Rule deleted successfully!', 'success');
                loadRules();
                loadRulesAnalytics();
            } else {
                showNotification('Error: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Delete error:', xhr, status, error);
            showNotification('Failed to delete rule', 'error');
        }
    });
}

/**
 * Fetch total trades count for calculating missed opportunities
 */
function fetchTotalTradesCount() {
    return new Promise((resolve) => {
        // Try to get trade count from dashboard metrics API
        $.ajax({
            type: 'POST',
            url: base_url + 'getDashboardMetrics',
            data: { rangeFilter: 1 }, // Use monthly range
            dataType: 'json',
            success: function(response) {
                if (response && response.success && response.data) {
                    const totalTrades = response.data.totalTrades || response.data.total_trades || 0;
                    console.log('Got total trades from dashboard:', totalTrades);
                    resolve(totalTrades);
                } else {
                    console.warn('Could not fetch total trades count from dashboard, using fallback');
                    resolve(calculateFallbackTradeCount());
                }
            },
            error: function() {
                console.warn('Error fetching total trades count, using fallback');
                resolve(calculateFallbackTradeCount());
            }
        });
    });
}

/**
 * Calculate fallback trade count from rules usage
 */
function calculateFallbackTradeCount() {
    if (!rulesData || rulesData.length === 0) return 0;

    // Find the maximum usage count among all rules as a rough estimate
    const maxUsage = Math.max(...rulesData.map(rule => rule.usage_count || 0));
    console.log('Using fallback trade count based on max rule usage:', maxUsage);
    return maxUsage;
}

/**
 * Load rules analytics
 */
function loadRulesAnalytics() {
    console.log('Loading rules analytics...');
    const timeframe = $('#analyticsTimeframe').val() || 30;
    const rangeFilter = timeframe <= 7 ? 0 : timeframe <= 30 ? 1 : timeframe <= 90 ? 2 : 3;

    $.ajax({
        type: 'POST',
        url: base_url + 'getRulesAnalytics',
        data: { rangeFilter: rangeFilter },
        dataType: 'json',
        success: function(response) {
            console.log('Analytics response:', response);
            if (response.success) {
                updateAnalyticsDisplay(response.data);
            } else {
                console.error('Analytics error:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load rules analytics:', xhr, status, error);
        }
    });
}

/**
 * Reset analytics display to default state
 */
function resetAnalyticsDisplay() {
    // Reset card values
    $('#totalRulesFollowed').text('0');
    $('#rulesConsistency').text('0%');
    $('#avgImpactScore').text('0.0');

    // Reset progress bars
    $('#consistencyBar').css('width', '0%');
    $('#mostFollowedBar').css('width', '0%');

    // Reset change indicators with badge styling
    $('#rulesFollowedChange').html('<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0%').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');
    $('#impactScoreChange').html('<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0 pts').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');

    // Reset ratings
    $('#adherenceRating').text('-');
    $('#impactRating').text('No Data');

    // Reset most followed rule
    $('#mostFollowedRule').text('No rules followed yet');
    $('#mostFollowedPercent').text('0%');
    $('#mostFollowedUsage').text('Start following rules to see data');

    // Reset stars
    $('#impactRatingStars .w-2').removeClass('bg-amber-400/80').addClass('bg-slate-300/60 dark:bg-slate-600/60');
}

/**
 * Update analytics display
 */
function updateAnalyticsDisplay(data) {
    console.log('Analytics data received:', data);

    const overall = data.overall || {};
    const mostFollowed = data.mostFollowed || [];
    const leastUsed = data.leastUsed || [];
    const impactScores = data.impactScores || [];
    const impact = data.impact || [];

    // Update Top 5 Most Followed Rules
    renderTopRules(mostFollowed);

    // Update Least Used Rules
    renderLeastUsedRules(leastUsed);
}

/**
 * Update top rules list
 */
function updateTopRulesList(rules) {
    const container = $('#topRulesList');
    container.empty();

    if (!rules || rules.length === 0) {
        container.html(`
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
                    <i class="fas fa-trophy text-2xl text-slate-400 dark:text-slate-500"></i>
                </div>
                <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2">No Rules Data Available</h3>
                <p class="text-xs text-slate-500 dark:text-slate-400 max-w-xs mx-auto leading-relaxed">
                    Start following your trading rules to see which ones you use most frequently
                </p>
            </div>
        `);
        return;
    }

    // Sort rules by usage frequency (descending) and then alphabetically by name
    const sortedRules = [...rules].sort((a, b) => {
        const usageA = a.usage_count || 0;
        const usageB = b.usage_count || 0;

        if (usageA === usageB) {
            return a.name.localeCompare(b.name);
        }
        return usageB - usageA; // Descending order (most used first)
    });

    // Take the top 5 most used rules
    const topRules = sortedRules.slice(0, 5);
    console.log('Top rules by usage frequency:', topRules);

    topRules.forEach((rule, index) => {
        const usageCount = rule.usage_count || 0;
        const adherenceRate = rule.adherence_rate || 0;

        // Determine color based on usage frequency
        let colorClass = '';
        let badgeColor = '';
        let textColor = '';

        if (usageCount >= 15) {
            // Very high usage - Purple/Violet
            colorClass = 'bg-gradient-to-r from-purple-500/90 to-purple-600/90';
            badgeColor = 'bg-gradient-to-r from-purple-500 to-purple-600';
            textColor = 'text-purple-600 dark:text-purple-400';
        } else if (usageCount >= 10) {
            // High usage - Blue
            colorClass = 'bg-gradient-to-r from-blue-500/90 to-blue-600/90';
            badgeColor = 'bg-gradient-to-r from-blue-500 to-blue-600';
            textColor = 'text-blue-600 dark:text-blue-400';
        } else if (usageCount >= 5) {
            // Medium usage - Green
            colorClass = 'bg-gradient-to-r from-emerald-500/90 to-emerald-600/90';
            badgeColor = 'bg-gradient-to-r from-emerald-500 to-emerald-600';
            textColor = 'text-emerald-600 dark:text-emerald-400';
        } else if (usageCount >= 3) {
            // Low-medium usage - Orange
            colorClass = 'bg-gradient-to-r from-orange-500/90 to-orange-600/90';
            badgeColor = 'bg-gradient-to-r from-orange-500 to-orange-600';
            textColor = 'text-orange-600 dark:text-orange-400';
        } else if (usageCount >= 1) {
            // Low usage - Yellow/Amber
            colorClass = 'bg-gradient-to-r from-amber-500/90 to-amber-600/90';
            badgeColor = 'bg-gradient-to-r from-amber-500 to-amber-600';
            textColor = 'text-amber-600 dark:text-amber-400';
        } else {
            // No usage - Red
            colorClass = 'bg-gradient-to-r from-rose-500/90 to-rose-600/90';
            badgeColor = 'bg-gradient-to-r from-rose-500 to-rose-600';
            textColor = 'text-rose-600 dark:text-rose-400';
        }

        // Enhanced rank badge
        const rankBadge = index < 3 ?
            `<div class="rank-badge ${badgeColor}">${index + 1}</div>` :
            `<div class="rank-badge bg-gradient-to-r from-slate-400 to-slate-500">${index + 1}</div>`;

        const ruleItem = `
            <div class="top-rules-item">
                <div class="flex items-start mb-3">
                    ${rankBadge}
                    <div class="flex-1 ml-3">
                        <h4 class="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-1 leading-tight">${rule.name}</h4>
                        <div class="flex items-center space-x-3">
                            <span class="text-xs text-slate-500 dark:text-slate-400">Used ${usageCount} times</span>
                        </div>
                    </div>
                    <div class="text-right flex-shrink-0">
                        <span class="text-lg font-bold ${textColor}">${usageCount}</span>
                        <div class="text-xs text-slate-500 dark:text-slate-400">times used</div>
                    </div>
                </div>
                <div class="top-rules-bar ${colorClass}" style="width: ${Math.max(20, Math.min(100, (usageCount / Math.max(...topRules.map(r => r.usage_count || 0))) * 100))}%">
                    <span class="text-xs font-medium">${usageCount} uses</span>
                </div>
            </div>
        `;
        container.append(ruleItem);
    });
}

/**
 * Update least used rules section
 */
function updateLeastUsedRules(rulesData, totalTrades = 0) {
    console.log('updateLeastUsedRules called with:', { rulesData, totalTrades, rulesLength: rulesData?.length });
    const container = $('#leastUsedRulesContainer');
    container.empty();

    if (!rulesData || rulesData.length === 0) {
        container.html(`
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
                    <i class="fas fa-exclamation-circle text-2xl text-slate-400 dark:text-slate-500"></i>
                </div>
                <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2">No Rules Available</h3>
                <p class="text-xs text-slate-500 dark:text-slate-400 max-w-xs mx-auto leading-relaxed">
                    Create some trading rules to see usage statistics and identify underutilized rules
                </p>
            </div>
        `);
        return;
    }

    // Sort rules by usage count (ascending) and then alphabetically by name
    const sortedRules = [...rulesData].sort((a, b) => {
        const usageA = a.usage_count || 0;
        const usageB = b.usage_count || 0;

        if (usageA === usageB) {
            return a.name.localeCompare(b.name);
        }
        return usageA - usageB;
    });

    console.log('Sorted rules for least used:', sortedRules);

    // Take the 5 least used rules (or all if fewer than 5)
    const leastUsedRules = sortedRules.slice(0, 5);
    console.log('Least used rules to display:', leastUsedRules);

    leastUsedRules.forEach(rule => {
        const usageCount = rule.usage_count || 0;
        const missedCount = totalTrades - usageCount;
        const missedPercentage = totalTrades > 0 ? ((missedCount / totalTrades) * 100).toFixed(1) : 0;

        console.log(`Rule: ${rule.name}, Used: ${usageCount}, Total Trades: ${totalTrades}, Missed: ${missedCount}, Missed %: ${missedPercentage}%`);

        // Determine colors based on missed percentage (how often rule was NOT used)
        let badgeColor, textColor, statusIcon, colorClass;

        if (usageCount === 0) {
            badgeColor = 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
            textColor = 'text-red-600 dark:text-red-400';
            statusIcon = 'fas fa-times-circle';
            colorClass = 'from-red-500/90 to-red-600/90';
        } else if (missedPercentage >= 80) {
            badgeColor = 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
            textColor = 'text-amber-600 dark:text-amber-400';
            statusIcon = 'fas fa-exclamation-triangle';
            colorClass = 'from-amber-500/90 to-amber-600/90';
        } else {
            badgeColor = 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
            textColor = 'text-blue-600 dark:text-blue-400';
            statusIcon = 'fas fa-info-circle';
            colorClass = 'from-blue-500/90 to-blue-600/90';
        }

        const html = `
            <div class="rules-impact-item mb-4">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1 pr-4">
                        <h4 class="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-1 leading-tight">${rule.name}</h4>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeColor}">
                                <i class="${statusIcon} w-3 h-3 mr-1"></i>
                                ${usageCount === 0 ? 'Never used' : usageCount === 1 ? 'Used once' : `Used ${usageCount} times`}
                            </span>
                        </div>
                    </div>
                    <div class="text-right flex-shrink-0">
                        <div class="flex items-center justify-end space-x-1 mb-1">
                            <span class="text-lg font-bold ${textColor}">${missedPercentage}%</span>
                        </div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">missed opportunities</div>
                    </div>
                </div>

                <!-- Missed opportunities bar -->
                <div class="w-full bg-slate-100 dark:bg-slate-700 rounded-full h-6 relative overflow-hidden">
                    <div class="h-full bg-gradient-to-r ${colorClass} rounded-full transition-all duration-700 ease-out flex items-center justify-center" style="width: ${Math.max(5, Math.min(100, parseFloat(missedPercentage) || 5))}%">
                        <span class="text-xs font-medium text-white">${missedCount} missed</span>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    });
}

/**
 * Render Top 5 Most Followed Rules
 */
function renderTopRules(rules) {
    const container = $('#topRulesList');
    container.empty();

    if (!rules || rules.length === 0) {
        container.html(`
            <div class="text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <i class="fas fa-chart-line text-gray-400 text-xl"></i>
                </div>
                <p class="text-gray-500 dark:text-gray-400 text-sm">No rule usage data available yet</p>
                <p class="text-gray-400 dark:text-gray-500 text-xs mt-1">Start following your rules to see analytics</p>
            </div>
        `);
        return;
    }

    // Take top 5 rules and render them
    const topRules = rules.slice(0, 5);
    // Use consistent blue color for all progress rings to match r.html
    const colors = [
        { stroke: '#3a82f6', text: 'blue-500 dark:text-blue-400' },
        { stroke: '#3a82f6', text: 'blue-500 dark:text-blue-400' },
        { stroke: '#3a82f6', text: 'blue-500 dark:text-blue-400' },
        { stroke: '#3a82f6', text: 'blue-500 dark:text-blue-400' },
        { stroke: '#3a82f6', text: 'blue-500 dark:text-blue-400' }
    ];

    topRules.forEach((rule, index) => {
        const adherenceRate = Math.round(rule.adherence_rate || 0);
        const usageCount = rule.usage_count || 0;
        const color = colors[index] || colors[0];
        const strokeDashoffset = 100 - adherenceRate;

        // Get category info for proper display
        const categoryInfo = getCategoryInfo(rule.category);

        // Determine change indicator based on adherence rate
        let changeClass, changeIcon, changeText;

        if (adherenceRate >= 75) {
            // High adherence - positive green
            changeClass = 'text-green-500 bg-green-100 dark:bg-green-900/50';
            changeIcon = 'fas fa-arrow-up';
            changeText = '+12%';
        } else if (adherenceRate >= 50) {
            // Medium adherence - neutral yellow
            changeClass = 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/50';
            changeIcon = 'fas fa-minus';
            changeText = '+5%';
        } else {
            // Low adherence - negative red
            changeClass = 'text-red-500 bg-red-100 dark:bg-red-900/50';
            changeIcon = 'fas fa-arrow-down';
            changeText = '-2%';
        }

        const ruleHtml = `
            <div class="flex items-center">
                <div class="relative w-12 h-12 mr-4">
                    <svg class="progress-ring w-full h-full" viewBox="0 0 36 36">
                        <circle class="progress-ring__circle" stroke="#e2e8f0" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                        <circle class="progress-ring__circle" stroke="${color.stroke}" stroke-width="3" stroke-linecap="round" fill="transparent" r="16" cx="18" cy="18" stroke-dasharray="100 100" stroke-dashoffset="${strokeDashoffset}"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-${color.text}">${adherenceRate}%</div>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">${rule.name}</h4>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${categoryInfo.label}</p>
                </div>
                <div class="text-xs font-medium ${changeClass} px-2 py-1 rounded-full">
                    <i class="${changeIcon} mr-1"></i>${changeText}
                </div>
            </div>
        `;

        container.append(ruleHtml);
    });
}

/**
 * Render Least Used Rules
 */
function renderLeastUsedRules(rules) {
    const container = $('#leastUsedRulesContainer');
    container.empty();

    if (!rules || rules.length === 0) {
        container.html(`
            <div class="text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-gray-400 text-xl"></i>
                </div>
                <p class="text-gray-500 dark:text-gray-400 text-sm">All rules are being used well!</p>
                <p class="text-gray-400 dark:text-gray-500 text-xs mt-1">Keep up the good discipline</p>
            </div>
        `);
        return;
    }

    // Take bottom 3 rules (least used)
    const leastUsedRules = rules.slice(-3).reverse();
    const icons = [
        { icon: 'fas fa-ban', color: 'red-500', bg: 'from-red-500/20 to-orange-500/20' },
        { icon: 'fas fa-clock', color: 'yellow-500', bg: 'from-yellow-500/20 to-amber-500/20' },
        { icon: 'fas fa-book', color: 'blue-500', bg: 'from-blue-500/20 to-indigo-500/20' }
    ];

    leastUsedRules.forEach((rule, index) => {
        const adherenceRate = Math.round(rule.adherence_rate || 0);
        const iconInfo = icons[index] || icons[0];
        const categoryInfo = getCategoryInfo(rule.category);

        // Calculate missed opportunities percentage (inverse of adherence)
        const missedOpportunities = 100 - adherenceRate;

        const ruleHtml = `
            <div class="flex items-center ${index < leastUsedRules.length - 1 ? 'mb-4' : ''}">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br ${iconInfo.bg} flex items-center justify-center mr-4">
                    <i class="${iconInfo.icon} text-${iconInfo.color} text-lg"></i>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm sm:text-base font-medium text-gray-800 dark:text-gray-100">${rule.name}</h4>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${categoryInfo.label}</p>
                </div>
                <div class="text-red-500 text-xs font-medium bg-red-100 dark:bg-red-900/50 px-2 py-1 rounded-full">${missedOpportunities}%</div>
            </div>
        `;

        container.append(ruleHtml);
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    if (typeof createToast === 'function') {
        if (type === 'error') {
            createToast('error', 'Error', message);
        } else if (type === 'success') {
            createToast('success', 'Success', message);
        } else {
            createToast('info', 'Info', message);
        }
    } else {
        // Fallback to alert if createToast is not available
        if (type === 'error') {
            alert('Error: ' + message);
        } else {
            alert(message);
        }
    }
}

/**
 * Edit Rule Functionality
 */

// Show edit rule modal
function showEditRuleModal(ruleId) {
    // Get rule data
    $.ajax({
        type: 'GET',
        url: base_url + 'getRule/' + ruleId,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const rule = response.rule;

                // Populate form fields
                $('#editRuleId').val(rule.id);
                $('#editRuleName').val(rule.name);
                $('#editRuleDescription').val(rule.description || '');
                $('#editRuleCategory').val(rule.category || 'general');

                // Show modal
                $('#editRuleModal').removeClass('hidden');
            } else {
                showNotification('Error loading rule: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('Failed to load rule data', 'error');
        }
    });
}

// Hide edit rule modal
function hideEditRuleModal() {
    $('#editRuleModal').addClass('hidden');
    $('#editRuleForm')[0].reset();
}

// Handle edit rule form submission
function handleEditRuleFormSubmit(e) {
    e.preventDefault();

    const ruleId = $('#editRuleId').val();
    const formData = {
        name: $('#editRuleName').val(),
        description: $('#editRuleDescription').val(),
        category: $('#editRuleCategory').val()
    };

    $.ajax({
        type: 'POST',
        url: base_url + 'updateRule/' + ruleId,
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('Rule updated successfully!', 'success');
                hideEditRuleModal();
                loadRules();
                loadRulesAnalytics();
            } else {
                showNotification('Error: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('Failed to update rule', 'error');
        }
    });
}
