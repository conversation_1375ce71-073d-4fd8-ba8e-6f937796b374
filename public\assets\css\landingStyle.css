body {
    font-family: 'Poppins', sans-serif;
    transition: background-color 0.3s, color 0.3s;
}

.dashboard-mockup {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-radius: 1rem;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.testimonial-card {
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: scale(1.03);
}

.toggle-checkbox:checked {
    right: 0;
    border-color: #2563eb;
}

.toggle-checkbox:checked+.toggle-label {
    background-color: #2563eb;
}

.trading-illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

.chart-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: dash 3s ease-in-out forwards;
}

.onboarding-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.onboarding-overlay.active {
    opacity: 1;
    pointer-events: all;
}

.onboarding-container {
    background-color: white;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    padding: 2rem;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.onboarding-overlay.active .onboarding-container {
    transform: translateY(0);
}

.progress-bar {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background-color: #4f46e5;
    transition: width 0.3s ease;
}

.question-container {
    display: none;
}

.question-container.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

.option-card {
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-card:hover {
    border-color: #4f46e5;
    transform: translateY(-2px);
}

.option-card.selected {
    border-color: #4f46e5;
    background-color: #f5f3ff;
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #4338ca;
}

.btn-outline {
    border: 1px solid #4f46e5;
    color: #4f46e5;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background-color: #f5f3ff;
}

.dark .onboarding-container {
    background-color: #1e293b;
    color: white;
}

.dark .option-card {
    border-color: #334155;
    background-color: #1e293b;
}

.dark .option-card:hover {
    border-color: #2563eb;
}

.dark .option-card.selected {
    border-color: #2563eb;
    background-color: #312e81;
}

.dark .btn-outline {
    border-color: #2563eb;
    color: #2563eb;
}

.dark .btn-outline:hover {
    background-color: #312e81;
}

/* FAQ Styles */
.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer.active {
    max-height: 500px;
}

.faq-toggle i {
    transition: transform 0.3s ease;
}

.faq-toggle.active i {
    transform: rotate(180deg);
}

/* Pricing badge */
.pricing-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #10B981;
    color: white;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Modern Testimonial Styles */
.testimonial-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.testimonial-card-modern {
    position: relative;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .testimonial-card-modern {
    background: #1e293b;
    border-color: #334155;
}

.testimonial-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px -5px rgba(0, 0, 0, 0.15);
}

.testimonial-quote {
    position: absolute;
    top: 0;
    right: 2rem;
    font-size: 5rem;
    line-height: 1;
    color: rgba(79, 70, 229, 0.1);
}

.dark .testimonial-quote {
    color: rgba(99, 102, 241, 0.1);
}

.testimonial-rating {
    display: flex;
    margin-bottom: 1rem;
}

.testimonial-avatar {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dark .testimonial-avatar {
    border-color: #1e293b;
}

.testimonial-highlight {
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, #4f46e5, #2563eb);
    color: white;
}

.dark .testimonial-highlight {
    background: linear-gradient(135deg, #2563eb, #818cf8);
}

.testimonial-highlight .testimonial-quote {
    color: rgba(255, 255, 255, 0.2);
}

.testimonial-highlight .testimonial-rating i {
    color: #fbbf24;
}

.testimonial-highlight .testimonial-avatar {
    border-color: rgba(255, 255, 255, 0.2);
}

/* Pricing badge */
.pricing-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #10B981;
    color: white;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.price-card {
    transition: all 0.3s ease;
}

.price-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.price-card-popular {
    border: 2px solid #10B981;
    position: relative;
}

.popular-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    background-color: #10B981;
    color: white;
    font-weight: bold;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* New CTA Styles */
.cta-section {
    position: relative;
    overflow: hidden;
}

.cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234f46e5' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.9), rgba(99, 102, 241, 0.9));
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.dark .cta-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.8), rgba(99, 102, 241, 0.8));
}

.cta-button-primary {
    background-color: white;
    color: #4f46e5;
    transition: all 0.3s ease;
}

.cta-button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.cta-button-secondary {
    border: 2px solid white;
    color: white;
    transition: all 0.3s ease;
}

.cta-button-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, rgba(240, 240, 240, 0) 0%, rgba(240, 240, 240, 0.2) 50%, rgba(240, 240, 240, 0) 100%);
}

.accent-bg {
    background-color: #3b82f6;
}

.accent-bg:hover {
    background-color: color-mix(in srgb, #3b82f6, black 10%);
}

.trading-illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

.chart-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: dash 3s ease-in-out forwards;
}

@keyframes dash {
    to {
        stroke-dashoffset: 0;
    }
}

.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-15px);
    }

    100% {
        transform: translateY(0px);
    }
}



/* Comparison Table Styles */
.comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 2rem;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .comparison-table th,
.dark .comparison-table td {
    border-color: rgba(255, 255, 255, 0.1);
}

.comparison-table th {
    font-weight: 600;
    background-color: rgba(79, 70, 229, 0.1);
}

.dark .comparison-table th {
    background-color: rgba(99, 102, 241, 0.1);
}

.comparison-table .feature {
    text-align: left;
    font-weight: 500;
}

.comparison-table .highlight {
    background-color: rgba(79, 70, 229, 0.05);
    position: relative;
}

.dark .comparison-table .highlight {
    background-color: rgba(99, 102, 241, 0.05);
}

.comparison-table .highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #4f46e5;
}

.dark .comparison-table .highlight::after {
    background-color: #6366f1;
}

.comparison-check {
    color: #10b981;
    font-size: 1.2rem;
}

.comparison-x {
    color: #ef4444;
    font-size: 1.2rem;
}

.comparison-maybe {
    color: #f59e0b;
    font-size: 1.2rem;
}