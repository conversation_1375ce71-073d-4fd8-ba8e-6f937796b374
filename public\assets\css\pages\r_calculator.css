/* .glass-effect {
    background: rgba(31, 41, 55, 0.25);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.glass-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.4) 0%, rgba(17, 24, 39, 0.4) 100%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
} */

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    min-height: 300px;
}

.input-field {
    min-width: 0;
    /* background: rgba(31, 41, 55, 0.3); */
    border: 1px solid rgba(148, 163, 184, 0.15);
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.input-field:focus {
    border-color: rgba(37, 99, 235, 0.5);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
    background: rgba(31, 41, 55, 0.5);
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
}

.radio-btn {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #4b5563;
    border-radius: 50%;
    background: transparent;
    position: relative;
    margin-right: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-btn:checked {
    border-color: #2563eb;
}

.radio-btn:checked:after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: #2563eb;
    border-radius: 50%;
    top: 2px;
    left: 2px;
}

.tooltip {
    position: absolute;
    padding: 8px 12px;
    background: rgba(29, 78, 216, 0.95);
    color: white;
    border-radius: 8px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    z-index: 100;
    border: 1px solid rgba(165, 180, 252, 0.2);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.glow {
    box-shadow: 0 0 15px rgba(37, 99, 235, 0.5);
}

.rupee-icon::before {
    content: '₹';
    margin-right: 4px;
}