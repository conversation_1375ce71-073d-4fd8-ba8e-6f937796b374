<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\UserModel;

class SubscriptionFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Skip subscription check for certain routes
        $skipRoutes = [
            'login',
            'logout', 
            'register',
            'forgot-password',
            'reset-password',
            'verify-email',
            'check-subscription-status',
            'subscription-expired-logout'
        ];

        $currentRoute = service('router')->getMatchedRoute();
        if ($currentRoute) {
            $routeName = $currentRoute[0] ?? '';
            foreach ($skipRoutes as $skipRoute) {
                if (strpos($routeName, $skipRoute) !== false) {
                    return;
                }
            }
        }

        // Check if user is logged in
        $session = session();
        $userId = $session->get('user_id');
        
        if (!$userId) {
            return; // Not logged in, let auth filter handle it
        }

        // Check subscription status
        $userModel = new UserModel();
        $user = $userModel->find($userId);
        
        if (!$user) {
            // User not found, logout
            $session->destroy();
            return redirect()->to('/login')->with('error', 'User account not found.');
        }

        // Check if subscription has expired
        if ($this->hasSubscriptionExpired($user)) {
            // Set expiration flag in session for graceful logout
            $session->set('subscription_expired', true);
            
            // For AJAX requests, return JSON response
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'subscription_expired' => true,
                    'message' => 'Your subscription has expired. Please renew to continue using the service.',
                    'redirect' => base_url('/login')
                ])->setStatusCode(401);
            }
            
            // For regular requests, redirect to login with message
            $session->destroy();
            return redirect()->to('/login')->with('subscription_expired', 'Your subscription has expired. Please renew to continue using the service.');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do after
    }

    /**
     * Check if user's subscription has expired
     */
    private function hasSubscriptionExpired($user)
    {
        // If no subscription end date, user has free access
        if (empty($user['sub_end'])) {
            return false;
        }

        // Check if subscription end date has passed
        $subscriptionEnd = strtotime($user['sub_end']);
        $currentTime = time();

        return $subscriptionEnd <= $currentTime;
    }
}
