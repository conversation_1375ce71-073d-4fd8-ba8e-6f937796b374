<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class AdminAuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = \Config\Services::session();
        
        // Check if admin is logged in
        if (!$session->get('admin_logged_in')) {
            // For AJAX requests, return JSON response
            if ($request->isAJAX()) {
                return \Config\Services::response()
                    ->setStatusCode(401)
                    ->setJSON(['error' => 'Unauthorized', 'redirect' => '/admin/login']);
            }
            
            // For regular requests, redirect to login
            return redirect()->to('/admin/login')->with('error', 'Please login to access admin panel.');
        }

        // Check permissions if specified
        if (!empty($arguments)) {
            $adminPermissions = $session->get('admin_permissions') ?? [];
            $requiredPermission = $arguments[0];
            
            if (!in_array($requiredPermission, $adminPermissions)) {
                // For AJAX requests, return JSON response
                if ($request->isAJAX()) {
                    return \Config\Services::response()
                        ->setStatusCode(403)
                        ->setJSON(['error' => 'Insufficient permissions']);
                }
                
                // For regular requests, redirect with error
                return redirect()->to('/admin/dashboard')->with('error', 'You do not have permission to access this resource.');
            }
        }

        // Check for remember me cookie if session is about to expire
        $this->checkRememberMe($session);
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add security headers for admin panel
        $response->setHeader('X-Frame-Options', 'DENY');
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        return $response;
    }

    /**
     * Check remember me cookie and extend session if valid
     */
    private function checkRememberMe($session)
    {
        $request = \Config\Services::request();
        $rememberCookie = $request->getCookie('admin_remember');
        
        if ($rememberCookie && !$session->get('admin_logged_in')) {
            $cookieData = base64_decode($rememberCookie);
            $parts = explode('|', $cookieData);
            
            if (count($parts) === 2) {
                $adminId = $parts[0];
                $username = $parts[1];
                
                // Verify admin still exists and is active
                $adminModel = new \App\Models\AdminModel();
                $admin = $adminModel->where('id', $adminId)
                                  ->where('username', $username)
                                  ->where('status', 'active')
                                  ->first();
                
                if ($admin) {
                    // Restore session
                    $sessionData = [
                        'admin_id' => $admin['id'],
                        'admin_username' => $admin['username'],
                        'admin_email' => $admin['email'],
                        'admin_full_name' => $admin['full_name'],
                        'admin_role' => $admin['role'],
                        'admin_permissions' => $adminModel->getPermissions($admin['role']),
                        'admin_logged_in' => true
                    ];
                    
                    $session->set($sessionData);
                    
                    // Update last login
                    $adminModel->update($admin['id'], ['last_login' => date('Y-m-d H:i:s')]);
                }
            }
        }
    }
}
