"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CalendarDays, Calendar, CalendarRange, Zap, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

interface GenerateSummaryProps {
  selectedTimeframe: 7 | 15 | 30
  onTimeframeChange: (timeframe: 7 | 15 | 30) => void
  onGenerate: () => void
}

const timeframeOptions = [
  {
    days: 7 as const,
    title: "Weekly",
    description: "Last 7 days",
    icon: CalendarDays,
    color: "from-blue-500 to-blue-600",
  },
  {
    days: 15 as const,
    title: "Bi-Weekly",
    description: "Last 15 days",
    icon: Calendar,
    color: "from-purple-500 to-purple-600",
  },
  {
    days: 30 as const,
    title: "Monthly",
    description: "Last 30 days",
    icon: CalendarRange,
    color: "from-green-500 to-green-600",
  },
]

export function GenerateSummary({ selectedTimeframe, onTimeframeChange, onGenerate }: GenerateSummaryProps) {
  return (
    <Card className="bg-slate-800 border-slate-700 shadow-xl">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle className="text-2xl text-white">Generate AI Summary</CardTitle>
            <CardDescription className="text-slate-400 text-base">
              Select a time frame to analyze your trading performance
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2 bg-blue-600/20 rounded-lg px-4 py-2 border border-blue-500/30">
            <Sparkles className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-blue-300">AI-Powered Analysis</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {timeframeOptions.map((option) => {
            const Icon = option.icon
            const isSelected = selectedTimeframe === option.days

            return (
              <Card
                key={option.days}
                className={cn(
                  "cursor-pointer transition-all duration-300 hover:scale-105 border-2",
                  isSelected
                    ? "border-blue-500 bg-slate-700 shadow-lg shadow-blue-500/20"
                    : "border-slate-600 bg-slate-800 hover:border-slate-500",
                )}
                onClick={() => onTimeframeChange(option.days)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-white text-lg">{option.title}</h3>
                      <p className="text-slate-400 mt-1">{option.description}</p>
                    </div>
                    <div
                      className={cn(
                        "w-12 h-12 rounded-xl flex items-center justify-center",
                        isSelected ? `bg-gradient-to-br ${option.color}` : "bg-slate-700",
                      )}
                    >
                      <Icon className={cn("w-6 h-6", isSelected ? "text-white" : "text-slate-400")} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="flex justify-center">
          <Button
            onClick={onGenerate}
            size="lg"
            className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Zap className="w-5 h-5 mr-2" />
            Generate Summary
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
