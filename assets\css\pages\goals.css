.gradient-bg {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.glass-card {
    background: rgba(15, 23, 42, 0.5);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.goal-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.goal-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.goal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #6366f1 100%);
}

.streak-ring {
    stroke-dasharray: 314;
    stroke-dashoffset: 314;
    animation: dash 1.5s ease-out forwards;
    transform-origin: center;
    transform: rotate(-90deg);
}

@keyframes dash {
    to {
        stroke-dashoffset: var(--dash-offset);
    }
}

.fade-in {
    animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #6366f1 100%);
    transition: all 0.3s ease;
    border-radius: 3px 3px 0 0;
}

.glow {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

.modal-enter {
    animation: modalEnter 0.4s ease-out forwards;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.goal-type-option {
    transition: all 0.2s ease;
}

.goal-type-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.goal-type-option.selected {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.custom-radio {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #334155;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.custom-radio:checked {
    border-color: #3b82f6;
}

.custom-radio:checked::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 8px;
    height: 8px;
    background-color: #3b82f6;
    border-radius: 50%;
}

.timeframe-option {
    transition: all 0.2s ease;
}

.timeframe-option:hover {
    background-color: rgba(30, 41, 59, 0.5);
}

.timeframe-option.selected {
    background-color: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.risk-type-option {
    transition: all 0.2s ease;
}

.risk-type-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.risk-type-option.selected {
    border-color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.coming-soon {
    position: relative;
    overflow: hidden;
}

.coming-soon::after {
    content: 'Coming Soon';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 1.1rem;
    z-index: 10;
}

/* Modern progress bar */
.progress-container {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 0.6s ease;
}

/* Minimalist chart container */
.chart-container {
    width: 100%;
    height: 60px;
    position: relative;
}

/* Modern badge styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

/* Custom timeframe input */
.custom-timeframe-input {
    display: none;
    grid-column: span 3;
}

.custom-timeframe-input.active {
    display: block;
}

/* Modern rectangular card design */
.modern-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-dark) 100%);
}

/* Thick progress bar */
.thick-progress {
    height: 12px;
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.thick-progress-bar {
    height: 100%;
    border-radius: 6px;
    transition: width 0.6s ease;
    background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-dark) 100%);
}

/* Card color variations */
.card-pnl {
    --card-color: #8b5cf6;
    --card-color-dark: #7c3aed;
}

.card-risk {
    --card-color: #f59e0b;
    --card-color-dark: #f97316;
}

.card-behavior {
    --card-color: #10b981;
    --card-color-dark: #059669;
}

.card-behavior-negative {
    --card-color: #ef4444;
    --card-color-dark: #dc2626;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
}

.status-on-track {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.status-at-risk {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.status-missed {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Type badge */
.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    background-color: rgba(var(--card-rgb), 0.1);
    color: var(--card-color);
}

/* Metric value */
.metric-value {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1;
    margin: 0.5rem 0;
    background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Card content layout */
.card-content {
    padding: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 1.5rem;
}

.stat-item {
    background: rgba(30, 41, 59, 0.4);
    border-radius: 8px;
    padding: 0.75rem;
}

.stat-label {
    color: rgba(226, 232, 240, 0.6);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-weight: 600;
    font-size: 1rem;
}

/* Progress info */
.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: rgba(226, 232, 240, 0.6);
    margin-bottom: 0.5rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.8s ease-in;
}

/* Pulse animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}