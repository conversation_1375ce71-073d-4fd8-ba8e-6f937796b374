## Stakeholders

### Create an Stakeholder
```php

$accountId = "acc_GP4lfNA0iIMn5B";

$api->account->fetch("acc_GP4lfNA0iIMn5B")->stakeholders()->create(array(
    "percentage_ownership" => 10,
    "name" => "<PERSON><PERSON><PERSON><PERSON>",
    "email" => "<EMAIL>",
    "relationship" => array(
        "director" => true,
        "executive" => false
    ),
    "phone" => array(
        "primary" => "**********",
        "secondary" => "**********"
    ),
    "addresses" => array(
        "residential" => array(
            "street" => "506, Koramangala 1st block",
            "city" => "Bengaluru",
            "state" => "Karnataka",
            "postal_code" => "560034",
            "country" => "IN"
        )
    ),
    "kyc" => array(
        "pan" => "**********"
    ),
    "notes" => array(
        "random_key_by_partner" => "random_value"
    )
));
```

**Parameters:**

| Name          | Type        | Description                                 |
|---------------|-------------|---------------------------------------------|
| email        | string      | The sub-merchant's business email address.  |
| name*          | string      |  The stakeholder's name as per the PAN card. The maximum length is 255 characters.   |
| percentage_ownership | float | The stakeholder's ownership of the business in percentage. Only two decimal places are allowed. For example, `87.55`. The maximum length is 100 characters. |
| relationship         | boolean      | The stakeholder's relationship with the account’s business. |
| phone         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#create-a-stakeholder) are supported |         
| addresses         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#create-a-stakeholder) are supported |    
| kyc         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#create-a-stakeholder) are supported |      
| notes | object  | A key-value pair  |   

**Response:**
```json
{
  "entity": "stakeholder",
  "relationship": {
    "director": true
  },
  "phone": {
    "primary": "**********",
    "secondary": "**********"
  },
  "notes": {
    "random_key_by_partner": "random_value"
  },
  "kyc": {
    "pan": "**********"
  },
  "id": "sth_GLGgm8fFCKc92m",
  "name": "Gaurav Kumar",
  "email": "<EMAIL>",
  "percentage_ownership": 10,
  "addresses": {
    "residential": {
      "street": "506, Koramangala 1st block",
      "city": "Bengaluru",
      "state": "Karnataka",
      "postal_code": "560034",
      "country": "IN"
    }
  }
}
```

-------------------------------------------------------------------------------------------------------

### Edit Stakeholder
```php
$accountId = "acc_GP4lfNA0iIMn5B";
$stakeholderId = "sth_GOQ4Eftlz62TSL";

$api->account->fetch($accountId)->stakeholders()->edit($stakeholderId, array(
    "percentage_ownership" => 20,
    "name" => "Gauri Kumar",
    "relationship" => array(
        "director" => false,
        "executive" => true
    ),
    "phone" => array(
        "primary" => "**********",
        "secondary" => "**********"
    ),
    "addresses" => array(
        "residential" => array(
            "street" => "507, Koramangala 1st block",
            "city" => "Bangalore",
            "state" => "Karnataka",
            "postal_code" => "560035",
            "country" => "IN"
        )
    ),
    "kyc" => array(
        "pan" => "**********"
    ),
    "notes" => array(
        "random_key_by_partner" => "random_value2"
    )
));
```

**Parameters:**

| Name          | Type        | Description                                 |
|---------------|-------------|---------------------------------------------|
| accountId* | string   | The unique identifier of a sub-merchant account generated by Razorpay.  |
| stakeholderId* | string      | The unique identifier of the stakeholder whose details are to be fetched. |
| name          | string      |  The stakeholder's name as per the PAN card. The maximum length is 255 characters.   |
| percentage_ownership | float | The stakeholder's ownership of the business in percentage. Only two decimal places are allowed. For example, `87.55`. The maximum length is 100 characters. |
| relationship         | boolean      | The stakeholder's relationship with the account’s business. |
| phone         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#update-a-stakeholder) are supported |         
| addresses         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#update-a-stakeholder) are supported |    
| kyc         | object      | All keys listed [here](https://razorpay.com/docs/api/partners/stakeholder/#update-a-stakeholder) are supported |      
| notes | object  | A key-value pair  |   

**Response:**
```json
{
  "id": "acc_GP4lfNA0iIMn5B",
  "type": "standard",
  "status": "created",
  "email": "<EMAIL>",
  "profile": {
    "category": "healthcare",
    "subcategory": "clinic",
    "addresses": {
      "registered": {
        "street1": "507, Koramangala 1st block",
        "street2": "MG Road-1",
        "city": "Bengalore",
        "state": "KARNATAKA",
        "postal_code": "560034",
        "country": "IN"
      }
    }
  },
  "notes": [],
  "created_at": **********,
  "phone": "**********",
  "reference_id": "randomId",
  "business_type": "partnership",
  "legal_business_name": "Acme Corp",
  "customer_facing_business_name": "ABCD Ltd"
}
```
-------------------------------------------------------------------------------------------------------

### Fetch all accounts
```php
$accountId = "acc_GP4lfNA0iIMn5B";

$api->account->fetch($accountId)->stakeholders()->all();

```

**Parameters:**

| Name          | Type        | Description                                 |
|---------------|-------------|---------------------------------------------|
| accountId* | string   | The unique identifier of a sub-merchant account generated by Razorpay.  |

**Response:**
```json
{
  "entity": "collection",
  "items": [
    {
      "id": "GZ13yPHLJof9IE",
      "entity": "stakeholder",
      "relationship": {
        "director": true
      },
      "phone": {
        "primary": "**********",
        "secondary": "**********"
      },
      "notes": {
        "random_key_by_partner": "random_value"
      },
      "kyc": {
        "pan": "**********"
      },
      "name": "Gaurav Kumar",
      "email": "<EMAIL>",
      "percentage_ownership": 10,
      "addresses": {
        "residential": {
          "street": "506, Koramangala 1st block",
          "city": "Bengaluru",
          "state": "Karnataka",
          "postal_code": "560034",
          "country": "in"
        }
      }
    }
  ],
  "count": 1
}
```

-------------------------------------------------------------------------------------------------------

### Fetch an stakeholder
```php
$accountId = "acc_GP4lfNA0iIMn5B";

$stakeholderId = "sth_GOQ4Eftlz62TSL";

$api->account->fetch($accountId)->stakeholders()->fetch($stakeholderId);
```

**Parameters:**

| Name        | Type        | Description                                 |
|-------------|-------------|---------------------------------------------|
| accountId* | string      | The unique identifier of a sub-merchant account generated by Razorpay.  |
| stakeholderId* | string      | The unique identifier of the stakeholder whose details are to be fetched. |

**Response:**
```json
{
  "entity": "stakeholder",
  "relationship": {
    "director": true
  },
  "phone": {
    "primary": "**********",
    "secondary": "**********"
  },
  "notes": {
    "random_key_by_partner": "random_value2"
  },
  "kyc": {
    "pan": "**********"
  },
  "id": "sth_GOQ4Eftlz62TSL",
  "name": "Gauri Kumar",
  "email": "<EMAIL>",
  "percentage_ownership": 20,
  "addresses": {
    "residential": {
      "street": "507, Koramangala 1st block",
      "city": "Bangalore",
      "state": "Karnataka",
      "postal_code": "560035",
      "country": "in"
    }
  }
}
```

### Upload stakeholders documents

```php
$accountId = "acc_00000000000001";

$stakeholderId = "sth_00000000000001";

$payload = [
    'file'=> '/Users/<USER>/Downloads/sample_uploaded.pdf',
    "document_type" => "aadhar_front"
];

$api->account->fetch($accountId)->stakeholders()->uploadStakeholderDoc($stakeholderId, $payload);
```

**Parameters:**

| Name        | Type        | Description                                 |
|-------------|-------------|---------------------------------------------|
| accountId* | string      | The unique identifier of a sub-merchant account generated by Razorpay.  |
| stakeholderId* | string      | The unique identifier of the stakeholder whose details are to be fetched. |
| file* | string      | The URL generated once the business proof document is uploaded.  |
| document_type* | string      | The documents valid for the proof type to be shared. In case of individual_proof_of_address, both the front and back of a document proof must be uploaded. Possible values : <br> individual_proof_of_identification: `personal_pan` <br><br> individual_proof_of_address : `voter_id_back`, `voter_id_front`, `aadhar_front`, `aadhar_back`, `passport_front`, `passport_back` |

**Response:**
```json
{
  "individual_proof_of_address": [
    {
      "type": "aadhar_front",
      "url": "https://rzp.io/i/bzDAbNg"
    }
  ]
}
```
-------------------------------------------------------------------------------------------------------

### Fetch stakeholders documents
```php

$accountId = "acc_00000000000001";

$stakeholderId = "sth_00000000000001";

$api->account->fetch($accountId)->stakeholders()->fetchStakeholderDoc($stakeholderId);
```

**Parameters:**

| Name        | Type        | Description                                 |
|-------------|-------------|---------------------------------------------|
| accountId* | string      | The unique identifier of a sub-merchant account generated by Razorpay.  |

**Response:**
```json
{
  "business_proof_of_identification": [
    {
      "type": "business_proof_url",
      "url": "<https://rzp.io/i/bzDKbNg>"
    }
  ]
}
```
-------------------------------------------------------------------------------------------------------

**PN: * indicates mandatory fields**
<br>
<br>
**For reference click [here](https://razorpay.com/docs/api/partners/stakeholder)**


$result = $api->account->fetch("acc_M83Uw27KXuC7c8")->stakeholders()->fetchStakeholderDoc("sth_M83WuwmrCFa55g", $payload);


$result = $api->account->fetch("acc_M83Uw27KXuC7c8")->stakeholders()->uploadStakeholderDoc("sth_M83WuwmrCFa55g", $payload);