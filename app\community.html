<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Diary | Trading Community</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4fd1c5;
            --primary-dark: #319795;
            --secondary: #805ad5;
            --secondary-dark: #6b46c1;
            --accent: #f687b3;
            --accent-dark: #e53e3e;
            --dark: #1a202c;
            --darker: #171923;
            --light: #f7fafc;
            --gray: #e2e8f0;
            --dark-gray: #2d3748;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background-color: var(--darker);
            color: var(--gray);
            transition: all 0.3s ease;
        }

        .glass-card {
            background: rgba(26, 32, 44, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .sidebar {
            transition: all 0.3s ease;
            background-color: var(--dark);
        }

        .sidebar-collapsed {
            width: 80px !important;
        }

        .sidebar-collapsed .nav-text {
            display: none;
        }

        .sidebar-collapsed .logo-text {
            display: none;
        }

        .sidebar-collapsed .logo-icon {
            margin: 0 auto;
        }

        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
        }

        .glow-button {
            box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }

            50% {
                box-shadow: 0 0 25px rgba(79, 209, 197, 0.8);
            }

            100% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }
        }

        .hashtag {
            color: var(--primary);
        }

        .mention {
            color: var(--accent);
        }

        .stock-tag {
            color: var(--secondary);
        }

        .code-block {
            background-color: #2d3748;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        .reaction-btn:hover {
            transform: scale(1.1);
        }

        .badge-pro {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }

        .badge-gainer {
            background: linear-gradient(90deg, #48bb78, #38b2ac);
        }

        .badge-master {
            background: linear-gradient(90deg, var(--accent), #ed8936);
        }

        .tab-active {
            border-bottom: 2px solid var(--primary);
            color: var(--primary);
        }

        .emoji-picker {
            position: absolute;
            bottom: 50px;
            right: 0;
            z-index: 10;
        }

        .comment-section {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .comment-section.active {
            max-height: 1000px;
            transition: max-height 0.5s ease-in;
        }

        .comment-input {
            background: rgba(45, 55, 72, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .comment-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .neon-text {
            text-shadow: 0 0 5px rgba(79, 209, 197, 0.5);
        }

        .modal {
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal.active .modal-content {
            transform: translateY(0);
        }

        .post-type-btn {
            transition: all 0.2s ease;
        }

        .post-type-btn.active {
            background-color: var(--primary);
            color: white;
        }

        .post-type-btn:hover:not(.active) {
            background-color: rgba(79, 209, 197, 0.1);
        }

        .follow-btn {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            transition: all 0.3s ease;
        }

        .follow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
        }

        .follow-btn.following {
            background: var(--dark-gray);
            border: 1px solid var(--primary);
        }

        .reply-section {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            margin-left: 2rem;
            border-left: 2px solid var(--primary);
            padding-left: 1rem;
        }

        .reply-section.active {
            max-height: 200px;
        }

        .reply-btn {
            color: var(--primary);
            font-size: 0.8rem;
        }

        .reply-btn:hover {
            text-decoration: underline;
        }

        .reply-indicator {
            color: var(--primary);
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -300px;
                z-index: 100;
                width: 300px;
                height: 100vh;
            }

            .sidebar.active {
                left: 0;
            }

            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                z-index: 90;
                display: none;
            }

            .overlay.active {
                display: block;
            }

            .feed-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .feed-header h1 {
                margin-bottom: 1rem;
            }

            .feed-filters {
                width: 100%;
                flex-direction: column;
                gap: 0.5rem;
            }

            .feed-filters select {
                width: 100%;
            }

            .tabs {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0.5rem;
                -webkit-overflow-scrolling: touch;
            }

            .tabs::-webkit-scrollbar {
                display: none;
            }

            .post-actions {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .post-actions>div {
                flex: 1 1 100%;
                justify-content: space-between;
            }

            .post-actions .emoji-picker-container {
                width: 100%;
                text-align: right;
            }

            .user-info {
                flex-direction: column;
                align-items: flex-start;
            }

            .user-badges {
                margin-top: 0.5rem;
                width: 100%;
                justify-content: space-between;
            }

            .modal-content {
                width: 95%;
                margin: 0 auto;
            }

            .post-type-buttons {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0.5rem;
            }

            .post-type-buttons::-webkit-scrollbar {
                display: none;
            }

            .post-type-btn {
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .comment-input-container {
                flex-direction: column;
            }

            .comment-input-container input {
                border-radius: 0.5rem 0.5rem 0 0 !important;
                width: 100%;
            }

            .comment-input-container button {
                border-radius: 0 0 0.5rem 0.5rem !important;
                width: 100%;
            }

            .reply-section {
                margin-left: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div
            class="sidebar w-64 flex-shrink-0 flex flex-col border-r border-gray-800 transition-all duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b border-gray-800">
                <div class="flex items-center space-x-2">
                    <div class="logo-icon">
                        <i class="fas fa-book text-2xl text-teal-400"></i>
                    </div>
                    <span class="logo-text text-xl font-bold text-teal-400 neon-text">TRADE DIARY</span>
                </div>
                <button id="sidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto py-4">
                <nav>
                    <ul class="space-y-2 px-2">
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-white bg-gray-800">
                                <i class="fas fa-home text-teal-400 w-6"></i>
                                <span class="nav-text ml-3">Home Feed</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-pen text-blue-400 w-6"></i>
                                <span class="nav-text ml-3">My Posts</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-lightbulb text-purple-400 w-6"></i>
                                <span class="nav-text ml-3">Setups & Ideas</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-chart-line text-green-400 w-6"></i>
                                <span class="nav-text ml-3">P&L Shares</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-search-dollar text-yellow-400 w-6"></i>
                                <span class="nav-text ml-3">Market Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-binoculars text-red-400 w-6"></i>
                                <span class="nav-text ml-3">Watchlist</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-bell text-indigo-400 w-6"></i>
                                <span class="nav-text ml-3">Notifications</span>
                                <span
                                    class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">3</span>
                            </a>
                        </li>
                    </ul>

                    <div class="border-t border-gray-800 mt-4 pt-4 px-2">
                        <ul class="space-y-2">
                            <li>
                                <a href="#"
                                    class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                    <i class="fas fa-user text-cyan-400 w-6"></i>
                                    <span class="nav-text ml-3">Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                    <i class="fas fa-cog text-gray-400 w-6"></i>
                                    <span class="nav-text ml-3">Settings</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>

            <div class="p-4 border-t border-gray-800">
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                        class="w-10 h-10 rounded-full border-2 border-teal-400">
                    <div class="ml-3">
                        <div class="text-sm font-medium text-white">TraderX</div>
                        <div class="text-xs text-gray-400">Pro Member</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overlay for mobile -->
        <div class="overlay"></div>

        <!-- Main content -->
        <div class="flex-1 overflow-y-auto bg-gray-900">
            <!-- Mobile header -->
            <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-800">
                <button id="mobileSidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div class="text-xl font-bold text-teal-400 neon-text">TRADE DIARY</div>
                <div class="w-6"></div>
            </div>

            <!-- Main feed -->
            <div class="p-4">
                <!-- Feed header with filters -->
                <div class="flex items-center justify-between mb-6 feed-header">
                    <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
                    <div class="flex space-x-2 feed-filters">
                        <div class="relative">
                            <select
                                class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option selected>All Assets</option>
                                <option>Equity</option>
                                <option>Options</option>
                                <option>Futures</option>
                                <option>Crypto</option>
                                <option>Forex</option>
                            </select>
                        </div>
                        <div class="relative">
                            <select
                                class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option selected>Latest</option>
                                <option>Trending</option>
                                <option>Top This Week</option>
                                <option>Most Liked</option>
                                <option>Educational</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="flex border-b border-gray-800 mb-6 tabs">
                    <button class="tab-active px-4 py-2 font-medium text-white">All</button>
                    <button class="px-4 py-2 font-medium text-gray-400 hover:text-white">Setups</button>
                    <button class="px-4 py-2 font-medium text-gray-400 hover:text-white">P&L</button>
                    <button class="px-4 py-2 font-medium text-gray-400 hover:text-white">Analysis</button>
                    <button class="px-4 py-2 font-medium text-gray-400 hover:text-white">Journals</button>
                </div>

                <!-- Posts -->
                <div class="space-y-6" id="postsContainer">
                    <!-- Post 1 - Setup with chart -->
                    <div class="post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                            <!-- Left: User Info -->
                            <div class="flex items-center gap-3">
                                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User"
                                    class="w-10 h-10 rounded-full border-2 border-purple-400">
                                <div>
                                    <div class="font-bold text-white">ChartQueen</div>
                                    <div class="text-xs text-gray-400">
                                        2 hours ago · <span class="text-purple-400">Setup Alert</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Right: Badges and Actions -->
                            <div class="flex flex-wrap items-center gap-2 sm:justify-end">
                                <button
                                    class="follow-btn text-xs text-white px-3 py-1 rounded-full bg-blue-600 hover:bg-blue-700 transition"
                                    onclick="toggleFollow(this)">
                                    Follow
                                </button>
                                <span class="badge-pro text-xs text-white px-2 py-1 rounded-full bg-purple-600">
                                    Pro Analyst
                                </span>
                                <button class="text-gray-400 hover:text-white transition">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>


                        <h3 class="text-lg font-bold text-white mb-3">BankNifty Breakout Setup - 15min Chart</h3>

                        <p class="text-gray-300 mb-4">Identified a bullish flag pattern on BankNifty 15min chart.
                            Breakout above 44,200 with volume confirmation could target 44,600. Stop loss below 44,000.
                        </p>

                        <div class="mb-4 flex justify-start">
                            <img src="https://templatelab.com/wp-content/uploads/2018/10/Free-Organizational-Chart-3-TemplateLab.com_-e1539503242343.jpg"
                                alt="Chart" class="w-full max-w-xl rounded-lg object-cover">
                        </div>



                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="hashtag">#BankNifty</span>
                            <span class="hashtag">#BreakoutSetup</span>
                            <span class="hashtag">#Intraday</span>
                            <span class="mention">@TradeMaster</span>
                            <span class="stock-tag">$BANKNIFTY</span>
                        </div>

                        <div
                            class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-3 border-t border-gray-800 post-actions">
                            <!-- Reactions -->
                            <div class="flex flex-wrap gap-4">
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400"
                                    onclick="toggleLike('like1')">
                                    <i class="far fa-thumbs-up mr-1" id="likeIcon1"></i>
                                    <span id="likeCount1">24</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400"
                                    onclick="toggleComments('comments1')">
                                    <i class="far fa-comment mr-1"></i>
                                    <span>8</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                                    <i class="far fa-share-square mr-1"></i>
                                    <span>Share</span>
                                </button>
                            </div>

                            <!-- Emoji Picker -->
                            <div class="hidden relative">
                                <button class="emoji-trigger text-gray-400 hover:text-yellow-400">
                                    <i class="far fa-smile"></i>
                                </button>
                                <div class="emoji-picker hidden bg-gray-800 rounded-lg p-2 shadow-xl mt-2 sm:mt-0">
                                    <div class="grid grid-cols-6 gap-1">
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👍</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👎</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">🔥</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">💯</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📈</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📉</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Comment section -->
                        <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="comments1">
                            <div class="space-y-4 mb-4">
                                <!-- Existing comments -->
                                <div class="flex">
                                    <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="User"
                                        class="w-8 h-8 rounded-full mr-3">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-white">TradeMaster</div>
                                            <span class="text-xs text-gray-500 ml-2">1h ago</span>
                                        </div>
                                        <p class="text-sm text-gray-300">Great setup! I'll be watching for the breakout
                                            confirmation.</p>
                                        <div class="flex text-xs text-gray-500 mt-1">
                                            <button class="hover:text-teal-400 reply-btn"
                                                onclick="toggleReply('reply1')">Reply</button>
                                        </div>

                                        <!-- Reply section -->
                                        <div class="reply-section" id="reply1">
                                            <div class="flex mt-3">
                                                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full mr-2">
                                                <div class="text-xs text-gray-400">Replying to <span
                                                        class="text-teal-400">TradeMaster</span></div>
                                            </div>
                                            <div class="flex items-start space-x-2 mt-2">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full">
                                                <div class="flex-1 flex comment-input-container">
                                                    <input type="text" placeholder="Write your reply..."
                                                        class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                    <button
                                                        class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Nested comment -->
                                        <div class="mt-3 pl-4 border-l-2 border-gray-700">
                                            <div class="flex">
                                                <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="User"
                                                    class="w-8 h-8 rounded-full mr-3">
                                                <div>
                                                    <div class="flex items-center">
                                                        <div class="text-sm font-medium text-white">OptionQueen</div>
                                                        <span class="reply-indicator">↳ replying to TradeMaster</span>
                                                        <span class="text-xs text-gray-500 ml-2">45m ago</span>
                                                    </div>
                                                    <p class="text-sm text-gray-300">What's your preferred entry method?
                                                        Market or limit order?</p>
                                                    <div class="flex text-xs text-gray-500 mt-1">
                                                        <button class="hover:text-teal-400 reply-btn"
                                                            onclick="toggleReply('reply2')">Reply</button>
                                                    </div>

                                                    <!-- Reply section -->
                                                    <div class="reply-section" id="reply2">
                                                        <div class="flex mt-3">
                                                            <img src="https://randomuser.me/api/portraits/women/44.jpg"
                                                                alt="User" class="w-6 h-6 rounded-full mr-2">
                                                            <div class="text-xs text-gray-400">Replying to <span
                                                                    class="text-teal-400">OptionQueen</span></div>
                                                        </div>
                                                        <div class="flex items-start space-x-2 mt-2">
                                                            <img src="https://randomuser.me/api/portraits/men/32.jpg"
                                                                alt="User" class="w-6 h-6 rounded-full">
                                                            <div class="flex-1 flex comment-input-container">
                                                                <input type="text" placeholder="Write your reply..."
                                                                    class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                                <button
                                                                    class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Comment form -->
                            <div class="flex items-start space-x-3">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                    class="w-8 h-8 rounded-full">
                                <div class="flex-1 flex comment-input-container">
                                    <input type="text" placeholder="Write a comment..."
                                        class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none">
                                    <button
                                        class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg">Post</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Post 2 - P&L Share -->
                    <div class="post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                            <!-- Left: User Info -->
                            <div class="flex items-center gap-3">
                                <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="User"
                                    class="w-10 h-10 rounded-full border-2 border-green-400">
                                <div>
                                    <div class="font-bold text-white">ProfitHunter</div>
                                    <div class="text-xs text-gray-400">5 hours ago · <span
                                            class="text-green-400">P&amp;L Share</span></div>
                                </div>
                            </div>

                            <!-- Right: Badges and Actions -->
                            <div class="flex flex-wrap items-center gap-2 sm:justify-end">
                                <button
                                    class="follow-btn text-xs text-white px-3 py-1 rounded-full bg-blue-600 hover:bg-blue-700 transition"
                                    onclick="toggleFollow(this)">
                                    Following
                                </button>
                                <span class="badge-gainer text-xs text-white px-2 py-1 rounded-full bg-green-600">
                                    Consistent Gainer
                                </span>
                                <button class="text-gray-400 hover:text-white transition">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>


                        <h3 class="text-lg font-bold text-white mb-3">+₹28,500 Today - Nifty Options</h3>

                        <p class="text-gray-300 mb-4">Executed 3 trades today in Nifty options. All were based on the
                            5min ORB strategy I shared last week. Key was waiting for confirmation after the first
                            30min.</p>

                        <div class="mb-4 flex justify-start">
                            <img src="https://templatelab.com/wp-content/uploads/2018/10/Free-Organizational-Chart-3-TemplateLab.com_-e1539503242343.jpg"
                                alt="Chart" class="w-full max-w-xl rounded-lg object-cover">
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="hashtag">#OptionsTrading</span>
                            <span class="hashtag">#ORBStrategy</span>
                            <span class="hashtag">#Nifty</span>
                            <span class="stock-tag">$NIFTY</span>
                        </div>

                        <div class="flex items-center justify-between pt-3 border-t border-gray-800 post-actions">
                            <div class="flex space-x-4">
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400"
                                    onclick="toggleLike('like2')">
                                    <i class="far fa-thumbs-up mr-1" id="likeIcon2"></i>
                                    <span id="likeCount2">42</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400"
                                    onclick="toggleComments('comments2')">
                                    <i class="far fa-comment mr-1"></i>
                                    <span>15</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                                    <i class="far fa-share-square mr-1"></i>
                                    <span>Share</span>
                                </button>
                            </div>
                            <div class="hidden relative emoji-picker-container">
                                <button class="emoji-trigger text-gray-400 hover:text-yellow-400">
                                    <i class="far fa-smile"></i>
                                </button>
                                <div class="emoji-picker hidden bg-gray-800 rounded-lg p-2 shadow-xl">
                                    <div class="grid grid-cols-6 gap-1">
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👍</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👎</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">🔥</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">💯</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📈</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📉</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comment section -->
                        <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="comments2">
                            <div class="space-y-4 mb-4">
                                <!-- Existing comments -->
                                <div class="flex">
                                    <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="User"
                                        class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <div class="text-sm font-medium text-white">TradeLearner</div>
                                        <p class="text-sm text-gray-300">Impressive results! Can you share more details
                                            about your risk management approach?</p>
                                        <div class="flex text-xs text-gray-500 mt-1">
                                            <button class="hover:text-teal-400 reply-btn"
                                                onclick="toggleReply('reply3')">Reply</button>
                                        </div>

                                        <!-- Reply section -->
                                        <div class="reply-section" id="reply3">
                                            <div class="flex mt-3">
                                                <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full mr-2">
                                                <div class="text-xs text-gray-400">Replying to <span
                                                        class="text-teal-400">TradeLearner</span></div>
                                            </div>
                                            <div class="flex items-start space-x-2 mt-2">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full">
                                                <div class="flex-1 flex comment-input-container">
                                                    <input type="text" placeholder="Write your reply..."
                                                        class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                    <button
                                                        class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <img src="https://randomuser.me/api/portraits/women/55.jpg" alt="User"
                                        class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <div class="text-sm font-medium text-white">SwingTrader</div>
                                        <p class="text-sm text-gray-300">What was your position sizing for these trades?
                                        </p>
                                        <div class="flex text-xs text-gray-500 mt-1">
                                            <button class="hover:text-teal-400 reply-btn"
                                                onclick="toggleReply('reply4')">Reply</button>
                                        </div>

                                        <!-- Reply section -->
                                        <div class="reply-section" id="reply4">
                                            <div class="flex mt-3">
                                                <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full mr-2">
                                                <div class="text-xs text-gray-400">Replying to <span
                                                        class="text-teal-400">SwingTrader</span></div>
                                            </div>
                                            <div class="flex items-start space-x-2 mt-2">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full">
                                                <div class="flex-1 flex comment-input-container">
                                                    <input type="text" placeholder="Write your reply..."
                                                        class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                    <button
                                                        class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Comment form -->
                            <div class="flex items-start space-x-3">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                    class="w-8 h-8 rounded-full">
                                <div class="flex-1 flex comment-input-container">
                                    <input type="text" placeholder="Write a comment..."
                                        class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none">
                                    <button
                                        class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg">Post</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Post 3 - Educational with code -->
                    <div class="post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                            <!-- Left: User Info -->
                            <div class="flex items-center gap-3">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                    class="w-10 h-10 rounded-full border-2 border-blue-400">
                                <div>
                                    <div class="font-bold text-white">AlgoTrader</div>
                                    <div class="text-xs text-gray-400">
                                        1 day ago · <span class="text-blue-400">Educational</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Right: Badges and Actions -->
                            <div class="flex flex-wrap items-center gap-2 sm:justify-end">
                                <button
                                    class="follow-btn text-xs text-white px-3 py-1 rounded-full bg-blue-600 hover:bg-blue-700 transition"
                                    onclick="toggleFollow(this)">
                                    Follow
                                </button>
                                <span class="badge-master text-xs text-white px-2 py-1 rounded-full bg-blue-600">
                                    Chart Master
                                </span>
                                <button class="text-gray-400 hover:text-white transition">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>


                        <h3 class="text-lg font-bold text-white mb-3">Python Backtest: EMA Crossover Strategy</h3>

                        <p class="text-gray-300 mb-4">Sharing a simple EMA crossover strategy backtest for BankNifty
                            futures. Tested on 5 years data with 55% win rate and 1.8 risk:reward. Code below:</p>

                        <div class="code-block mb-4">
                            <pre class="text-gray-200"><code>import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy

class EMACrossover(Strategy):
    fast_ema = 9
    slow_ema = 21
    
    def init(self):
        self.fast = self.I(EMA, self.data.Close, self.fast_ema)
        self.slow = self.I(EMA, self.data.Close, self.slow_ema)
    
    def next(self):
        if crossover(self.fast, self.slow):
            self.buy()
        elif crossover(self.slow, self.fast):
            self.sell()

data = pd.read_csv('banknifty_5min.csv', parse_dates=['Date'])
bt = Backtest(data, EMACrossover, commission=.0005)
stats = bt.run()
bt.plot()</code></pre>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="hashtag">#AlgorithmicTrading</span>
                            <span class="hashtag">#Python</span>
                            <span class="hashtag">#Backtesting</span>
                            <span class="mention">@DataScientist</span>
                        </div>

                        <div class="flex items-center justify-between pt-3 border-t border-gray-800 post-actions">
                            <div class="flex space-x-4">
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400"
                                    onclick="toggleLike('like3')">
                                    <i class="far fa-thumbs-up mr-1" id="likeIcon3"></i>
                                    <span id="likeCount3">37</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400"
                                    onclick="toggleComments('comments3')">
                                    <i class="far fa-comment mr-1"></i>
                                    <span>12</span>
                                </button>
                                <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                                    <i class="far fa-share-square mr-1"></i>
                                    <span>Share</span>
                                </button>
                            </div>
                            <div class="hidden relative emoji-picker-container">
                                <button class="emoji-trigger text-gray-400 hover:text-yellow-400">
                                    <i class="far fa-smile"></i>
                                </button>
                                <div class="emoji-picker hidden bg-gray-800 rounded-lg p-2 shadow-xl">
                                    <div class="grid grid-cols-6 gap-1">
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👍</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👎</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">🔥</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">💯</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📈</span>
                                        <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📉</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comment section -->
                        <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="comments3">
                            <div class="space-y-4 mb-4">
                                <!-- Existing comments -->
                                <div class="flex">
                                    <img src="https://randomuser.me/api/portraits/women/66.jpg" alt="User"
                                        class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <div class="text-sm font-medium text-white">CodeNinja</div>
                                        <p class="text-sm text-gray-300">Nice implementation! Have you tried optimizing
                                            the EMA periods?</p>
                                        <div class="flex text-xs text-gray-500 mt-1">
                                            <button class="hover:text-teal-400 reply-btn"
                                                onclick="toggleReply('reply5')">Reply</button>
                                        </div>

                                        <!-- Reply section -->
                                        <div class="reply-section" id="reply5">
                                            <div class="flex mt-3">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full mr-2">
                                                <div class="text-xs text-gray-400">Replying to <span
                                                        class="text-teal-400">CodeNinja</span></div>
                                            </div>
                                            <div class="flex items-start space-x-2 mt-2">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full">
                                                <div class="flex-1 flex comment-input-container">
                                                    <input type="text" placeholder="Write your reply..."
                                                        class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                    <button
                                                        class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <img src="https://randomuser.me/api/portraits/men/77.jpg" alt="User"
                                        class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <div class="text-sm font-medium text-white">DataScientist</div>
                                        <p class="text-sm text-gray-300">Consider adding a volatility filter to improve
                                            performance during choppy markets.</p>
                                        <div class="flex text-xs text-gray-500 mt-1">
                                            <button class="hover:text-teal-400 reply-btn"
                                                onclick="toggleReply('reply6')">Reply</button>
                                        </div>

                                        <!-- Reply section -->
                                        <div class="reply-section" id="reply6">
                                            <div class="flex mt-3">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full mr-2">
                                                <div class="text-xs text-gray-400">Replying to <span
                                                        class="text-teal-400">DataScientist</span></div>
                                            </div>
                                            <div class="flex items-start space-x-2 mt-2">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                                    class="w-6 h-6 rounded-full">
                                                <div class="flex-1 flex comment-input-container">
                                                    <input type="text" placeholder="Write your reply..."
                                                        class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                                    <button
                                                        class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Comment form -->
                            <div class="flex items-start space-x-3">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                                    class="w-8 h-8 rounded-full">
                                <div class="flex-1 flex comment-input-container">
                                    <input type="text" placeholder="Write a comment..."
                                        class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none">
                                    <button
                                        class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg">Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Post Modal -->
    <div class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
        <div class="absolute inset-0 bg-black bg-opacity-70"></div>
        <div class="modal-content glass-card rounded-xl w-full max-w-2xl relative">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-white">Create New Post</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="flex space-x-2 mb-6 post-type-buttons">
                    <button class="post-type-btn active px-4 py-2 rounded-lg" data-type="setup">
                        <i class="fas fa-lightbulb mr-2"></i> Setup
                    </button>
                    <button class="post-type-btn px-4 py-2 rounded-lg" data-type="pnl">
                        <i class="fas fa-chart-line mr-2"></i> P&L
                    </button>
                    <button class="post-type-btn px-4 py-2 rounded-lg" data-type="analysis">
                        <i class="fas fa-search-dollar mr-2"></i> Analysis
                    </button>
                    <button class="post-type-btn px-4 py-2 rounded-lg" data-type="journal">
                        <i class="fas fa-book mr-2"></i> Journal
                    </button>
                </div>

                <div class="mb-4">
                    <input type="text" id="postTitle" placeholder="Post title..."
                        class="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500">
                </div>

                <div class="mb-4">
                    <textarea id="postContent" rows="5"
                        placeholder="Share your trading idea, analysis, or experience..."
                        class="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-teal-500"></textarea>
                </div>

                <div class="mb-4">
                    <div class="flex items-center justify-between">
                        <label class="text-gray-300">Add tags:</label>
                        <button id="addStockTag" class="text-xs text-teal-400 hover:text-teal-300">Add stock
                            symbol</button>
                    </div>
                    <div id="tagsContainer" class="flex flex-wrap gap-2 mt-2">
                        <!-- Tags will be added here -->
                    </div>
                    <input type="text" id="tagInput" placeholder="#hashtag or @mention"
                        class="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-2 mt-2 focus:outline-none focus:ring-2 focus:ring-teal-500">
                </div>

                <div class="mb-6">
                    <label class="text-gray-300">Upload image/chart:</label>
                    <div class="mt-2 flex items-center justify-center w-full">
                        <label for="postImage"
                            class="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-700 rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-700">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-500 mb-2"></i>
                                <p class="text-sm text-gray-400">Click to upload or drag and drop</p>
                            </div>
                            <input id="postImage" type="file" class="hidden" accept="image/*">
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button id="cancelPost"
                        class="px-6 py-2 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800">Cancel</button>
                    <button id="submitPost"
                        class="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-500 glow-button">Post</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating action button -->
    <button id="addPostBtn"
        class="fixed bottom-6 right-6 w-14 h-14 rounded-full bg-teal-500 text-white flex items-center justify-center glow-button shadow-xl hover:bg-teal-400 transition-all">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <script>
        // Sidebar toggle
        const sidebar = document.querySelector('.sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
        const overlay = document.querySelector('.overlay');

        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('sidebar-collapsed');
            sidebarToggle.querySelector('i').classList.toggle('fa-chevron-left');
            sidebarToggle.querySelector('i').classList.toggle('fa-chevron-right');
        });

        mobileSidebarToggle.addEventListener('click', () => {
            sidebar.classList.add('active');
            overlay.classList.add('active');
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });

        // Emoji picker
        const emojiTriggers = document.querySelectorAll('.emoji-trigger');
        emojiTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                const picker = trigger.nextElementSibling;
                document.querySelectorAll('.emoji-picker').forEach(p => {
                    if (p !== picker) p.classList.add('hidden');
                });
                picker.classList.toggle('hidden');
            });
        });

        document.addEventListener('click', () => {
            document.querySelectorAll('.emoji-picker').forEach(picker => {
                picker.classList.add('hidden');
            });
        });

        // Tab switching
        const tabs = document.querySelectorAll('.flex.border-b button');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('tab-active'));
                tab.classList.add('tab-active');
            });
        });

        // Post card hover effect
        const postCards = document.querySelectorAll('.post-card');
        postCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
                card.style.boxShadow = '0 10px 25px rgba(79, 209, 197, 0.1)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
        });

        // Like functionality
        function toggleLike(id) {
            const likeIcon = document.getElementById(`likeIcon${id.slice(-1)}`);
            const likeCount = document.getElementById(id);

            if (likeIcon.classList.contains('far')) {
                likeIcon.classList.remove('far');
                likeIcon.classList.add('fas', 'text-teal-400');
                likeCount.textContent = parseInt(likeCount.textContent) + 1;
            } else {
                likeIcon.classList.remove('fas', 'text-teal-400');
                likeIcon.classList.add('far');
                likeCount.textContent = parseInt(likeCount.textContent) - 1;
            }
        }

        // Comment section toggle
        function toggleComments(id) {
            const commentSection = document.getElementById(id);
            commentSection.classList.toggle('active');

            // Close other comment sections
            document.querySelectorAll('.comment-section').forEach(section => {
                if (section.id !== id && section.classList.contains('active')) {
                    section.classList.remove('active');
                }
            });
        }

        // Reply section toggle
        function toggleReply(id) {
            const replySection = document.getElementById(id);
            replySection.classList.toggle('active');

            // Close other reply sections
            document.querySelectorAll('.reply-section').forEach(section => {
                if (section.id !== id && section.classList.contains('active')) {
                    section.classList.remove('active');
                }
            });
        }

        // Follow button toggle
        function toggleFollow(button) {
            if (button.classList.contains('following')) {
                button.classList.remove('following');
                button.textContent = 'Follow';
            } else {
                button.classList.add('following');
                button.textContent = 'Following';
            }
        }

        // Comment form submission
        document.querySelectorAll('.comment-input').forEach(input => {
            input.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    const commentText = this.value.trim();
                    if (commentText) {
                        const commentSection = this.closest('.comment-section');
                        const commentsContainer = commentSection.querySelector('.space-y-4');

                        const newComment = document.createElement('div');
                        newComment.className = 'flex';
                        newComment.innerHTML = `
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full mr-3">
                            <div>
                                <div class="text-sm font-medium text-white">You</div>
                                <p class="text-sm text-gray-300">${commentText}</p>
                                <div class="flex text-xs text-gray-500 mt-1">
                                    <button class="hover:text-teal-400 reply-btn" onclick="toggleReply('replyNew')">Reply</button>
                                </div>
                                
                                <div class="reply-section" id="replyNew">
                                    <div class="flex mt-3">
                                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-6 h-6 rounded-full mr-2">
                                        <div class="text-xs text-gray-400">Replying to <span class="text-teal-400">You</span></div>
                                    </div>
                                    <div class="flex items-start space-x-2 mt-2">
                                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-6 h-6 rounded-full">
                                        <div class="flex-1 flex comment-input-container">
                                            <input type="text" placeholder="Write your reply..." class="comment-input flex-1 rounded-l-lg px-3 py-1 text-white focus:outline-none text-sm">
                                            <button class="bg-teal-600 hover:bg-teal-500 text-white px-3 rounded-r-lg text-sm">Reply</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        commentsContainer.appendChild(newComment);
                        this.value = '';
                    }
                }
            });
        });

        // Reply form submission
        document.querySelectorAll('.reply-section .comment-input').forEach(input => {
            input.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    const replyText = this.value.trim();
                    if (replyText) {
                        const replySection = this.closest('.reply-section');
                        const parentComment = replySection.closest('.flex');

                        const newReply = document.createElement('div');
                        newReply.className = 'mt-3 pl-4 border-l-2 border-gray-700';
                        newReply.innerHTML = `
                            <div class="flex">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full mr-3">
                                <div>
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-white">You</div>
                                        <span class="reply-indicator">↳ replying to ${parentComment.querySelector('.text-sm.font-medium').textContent}</span>
                                        <span class="text-xs text-gray-500 ml-2">Just now</span>
                                    </div>
                                    <p class="text-sm text-gray-300">${replyText}</p>
                                    <div class="flex text-xs text-gray-500 mt-1">
                                        <button class="hover:text-teal-400 reply-btn" onclick="toggleReply('replyNew')">Reply</button>
                                    </div>
                                </div>
                            </div>
                        `;

                        parentComment.parentNode.insertBefore(newReply, parentComment.nextSibling);
                        this.value = '';
                        replySection.classList.remove('active');
                    }
                }
            });
        });

        // Modal functionality
        const modal = document.querySelector('.modal');
        const addPostBtn = document.getElementById('addPostBtn');
        const closeModal = document.getElementById('closeModal');
        const cancelPost = document.getElementById('cancelPost');
        const submitPost = document.getElementById('submitPost');
        const postTypeBtns = document.querySelectorAll('.post-type-btn');
        const tagInput = document.getElementById('tagInput');
        const tagsContainer = document.getElementById('tagsContainer');
        const addStockTag = document.getElementById('addStockTag');
        const postTitle = document.getElementById('postTitle');
        const postContent = document.getElementById('postContent');
        const postsContainer = document.getElementById('postsContainer');

        // Toggle modal
        function toggleModal() {
            modal.classList.toggle('active');
        }

        addPostBtn.addEventListener('click', toggleModal);
        closeModal.addEventListener('click', toggleModal);
        cancelPost.addEventListener('click', toggleModal);

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                toggleModal();
            }
        });

        // Post type selection
        postTypeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                postTypeBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // Tag input functionality
        tagInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && tagInput.value.trim()) {
                const tag = tagInput.value.trim();
                const tagElement = document.createElement('span');

                if (tag.startsWith('#')) {
                    tagElement.className = 'hashtag text-xs bg-gray-800 px-2 py-1 rounded';
                } else if (tag.startsWith('@')) {
                    tagElement.className = 'mention text-xs bg-gray-800 px-2 py-1 rounded';
                } else if (tag.startsWith('$')) {
                    tagElement.className = 'stock-tag text-xs bg-gray-800 px-2 py-1 rounded';
                } else {
                    tagElement.className = 'text-xs bg-gray-800 px-2 py-1 rounded';
                }

                tagElement.textContent = tag;
                tagsContainer.appendChild(tagElement);
                tagInput.value = '';
            }
        });

        // Add stock tag button
        addStockTag.addEventListener('click', () => {
            tagInput.value = '$';
            tagInput.focus();
        });

        // Submit new post
        submitPost.addEventListener('click', () => {
            const title = postTitle.value.trim();
            const content = postContent.value.trim();
            const activeTypeBtn = document.querySelector('.post-type-btn.active');
            const postType = activeTypeBtn ? activeTypeBtn.dataset.type : 'setup';

            if (title && content) {
                const postElement = document.createElement('div');
                postElement.className = 'post-card glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg mb-6';

                // Get current time for "posted ago" text
                const now = new Date();
                const hours = now.getHours();
                const minutes = now.getMinutes();
                const timeString = `${hours}:${minutes < 10 ? '0' + minutes : minutes}`;

                // Get post type display text
                let typeText = '';
                let typeColor = '';
                switch (postType) {
                    case 'pnl':
                        typeText = 'P&L Share';
                        typeColor = 'text-green-400';
                        break;
                    case 'analysis':
                        typeText = 'Market Analysis';
                        typeColor = 'text-yellow-400';
                        break;
                    case 'journal':
                        typeText = 'Trading Journal';
                        typeColor = 'text-blue-400';
                        break;
                    default:
                        typeText = 'Setup Alert';
                        typeColor = 'text-purple-400';
                }

                // Create post HTML
                postElement.innerHTML = `
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3 user-info">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-10 h-10 rounded-full border-2 border-teal-400">
                            <div>
                                <div class="font-bold text-white">You</div>
                                <div class="text-xs text-gray-400">Just now · <span class="${typeColor}">${typeText}</span></div>
                            </div>
                        </div>
                        <div class="flex space-x-2 user-badges">
                            <span class="badge-pro text-xs text-white px-2 py-1 rounded-full">Pro Member</span>
                            <button class="text-gray-400 hover:text-white">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <h3 class="text-lg font-bold text-white mb-3">${title}</h3>

                    <p class="text-gray-300 mb-4">${content.replace(/\n/g, '<br>')}</p>

                    ${document.getElementById('postImage').files.length > 0 ? `
                    <div class="mb-4">
                        <img src="${URL.createObjectURL(document.getElementById('postImage').files[0])}" alt="Post image" class="w-full rounded-lg">
                    </div>
                    ` : ''}

                    <div class="flex flex-wrap gap-2 mb-4" id="postTags">
                        ${Array.from(tagsContainer.children).map(tag => tag.outerHTML).join('')}
                    </div>

                    <div class="flex items-center justify-between pt-3 border-t border-gray-800 post-actions">
                        <div class="flex space-x-4">
                            <button class="reaction-btn flex items-center text-gray-400 hover:text-teal-400" onclick="toggleLike('likeNew')">
                                <i class="far fa-thumbs-up mr-1" id="likeIconNew"></i>
                                <span id="likeCountNew">0</span>
                            </button>
                            <button class="reaction-btn flex items-center text-gray-400 hover:text-blue-400" onclick="toggleComments('commentsNew')">
                                <i class="far fa-comment mr-1"></i>
                                <span>0</span>
                            </button>
                            <button class="reaction-btn flex items-center text-gray-400 hover:text-green-400">
                                <i class="far fa-share-square mr-1"></i>
                                <span>Share</span>
                            </button>
                        </div>
                        <div class="relative emoji-picker-container">
                            <button class="emoji-trigger text-gray-400 hover:text-yellow-400">
                                <i class="far fa-smile"></i>
                            </button>
                            <div class="emoji-picker hidden bg-gray-800 rounded-lg p-2 shadow-xl">
                                <div class="grid grid-cols-6 gap-1">
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👍</span>
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">👎</span>
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">🔥</span>
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">💯</span>
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📈</span>
                                    <span class="emoji-option text-xl hover:bg-gray-700 rounded p-1">📉</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="comment-section mt-4 pt-4 border-t border-gray-800" id="commentsNew">
                        <div class="space-y-4 mb-4">
                            <!-- Comments will appear here -->
                        </div>

                        <div class="flex items-start space-x-3">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                            <div class="flex-1 flex comment-input-container">
                                <input type="text" placeholder="Write a comment..." class="comment-input flex-1 rounded-l-lg px-4 py-2 text-white focus:outline-none">
                                <button class="bg-teal-600 hover:bg-teal-500 text-white px-4 rounded-r-lg">Post</button>
                            </div>
                        </div>
                    </div>
                `;

                // Add new post to the top of the container
                postsContainer.insertBefore(postElement, postsContainer.firstChild);

                // Reset form
                postTitle.value = '';
                postContent.value = '';
                tagsContainer.innerHTML = '';
                document.getElementById('postImage').value = '';

                // Close modal
                toggleModal();
            }
        });
    </script>
</body>

</html>