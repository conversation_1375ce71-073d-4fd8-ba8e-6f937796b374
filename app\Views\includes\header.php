<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">




    <link rel="icon" href="<?= base_url() ?>assets/images/logo.png" type="image/png">
    <title><?= isset($title) && !empty($title) ? $title : 'Dashboard'; ?> | Advanced Trading Dashboard</title>
    <script>
        (function() {
            try {
                const darkMode = localStorage.getItem('darkMode');
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (darkMode === 'true' || (darkMode === null && prefersDark)) {
                    document.documentElement.classList.add('dark');
                }
            } catch (e) {
                // fail silently
            }
        })();
    </script>
    <script src="<?= base_url() ?>assets/tailwind-3.4.16/tailwind.js"></script>
    <link rel="stylesheet" href="<?= base_url() ?>assets/font-awesome-pro-5/css/all.min.css">
    <link rel="stylesheet" href="<?= base_url() ?>assets/animate/animate.min.css">

    <!-- Chart.js for analytics charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <link rel="stylesheet" href="<?= base_url() ?>assets/lightbox/lightbox.css">

    <script src="<?= base_url() ?>assets/js/tailwind-config.js"></script>
    <link rel="stylesheet" href="<?= base_url() ?>assets/css/style.css?random=<?= rand() ?>">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q8W8162DZ2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-Q8W8162DZ2');
    </script>
</head>

<body class="bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
    <div class="toast-container" id="toastContainer"></div>
    <!-- Sidebar Overlay -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>