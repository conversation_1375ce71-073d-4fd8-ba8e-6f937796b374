<link rel="stylesheet" href="<?= base_url() ?>assets/css/pages/calculator.css?v=<?= rand() ?>">

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="equal-height-cols grid grid-cols-1 lg:grid-cols-3 gap-6 bg-gray-100 dark:bg-gray-800 rounded-md">
        <!-- Input Section -->
        <div class="col-span-1 lg:col-span-2">
            <div class="glow-card rounded-2xl p-6 h-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700">

                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                    <h2 class="title-font text-2xl font-semibold text-gray-900 dark:text-white">

                        <i class="fas fa-sliders-h text-blue-400 mr-3"></i>
                        Position Parameters
                    </h2>
                    <button id="resetBtn" class="text-blue-400 hover:text-blue-300 transition-colors flex items-center">
                        <i class="fas fa-undo-alt mr-2"></i> Reset Calculator
                    </button>
                </div>

                <!-- Position Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-3 uppercase tracking-wider">Trade
                        Direction</label>
                    <div class="flex gap-4">
                        <label class="radio-btn flex items-center cursor-pointer">
                            <input type="radio" name="positionType" id="long" value="long" checked class="hidden">
                            <span
                                class="w-5 h-5 inline-block rounded-full border-2 border-gray-600 transition-all relative flex items-center justify-center">
                                <span
                                    class="absolute w-2.5 h-2.5 rounded-full bg-white opacity-0 transition-opacity"></span>
                            </span>
                            <span class="ml-3 text-sm font-medium">Long</span>
                        </label>
                        <label class="radio-btn flex items-center cursor-pointer">
                            <input type="radio" name="positionType" id="short" value="short" class="hidden">
                            <span
                                class="w-5 h-5 inline-block rounded-full border-2 border-gray-600 transition-all relative flex items-center justify-center">
                                <span
                                    class="absolute w-2.5 h-2.5 rounded-full bg-white opacity-0 transition-opacity"></span>
                            </span>
                            <span class="ml-3 text-sm font-medium">Short</span>
                        </label>
                    </div>
                </div>

                <!-- Divider -->
                <div class="my-6 border-t border-slate-700/50"></div>

                <!-- Core Input Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                    <!-- Account Size -->
                    <div>
                        <label for="accountSize"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-2.5 uppercase tracking-wider">Account Size
                            (₹)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center text-blue-400">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <input type="number" id="accountSize"
                                class="bg-white dark:bg-slate-800 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-700 text-sm rounded-xl focus:ring-blue-500 block w-full pl-10 p-3.5 transition-all"
                                placeholder="10,000" min="1" value="10000">
                        </div>
                    </div>

                    <!-- Risk Percentage -->
                    <div>
                        <label for="riskPercentage"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-2.5 uppercase tracking-wider">Risk
                            Percentage</label>
                        <div class="relative">
                            <input type="number" id="riskPercentage"
                                class="bg-white dark:bg-slate-800 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-700 text-sm rounded-xl focus:ring-blue-500 block w-full p-3.5 pr-10 transition-all"
                                placeholder="2" min="0.1" max="100" step="0.1" value="2">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 text-blue-400">
                                <i class="fas fa-percent"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Entry Price -->
                    <div>
                        <label for="entryPrice"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-2.5 uppercase tracking-wider">Entry Price
                            (₹)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center text-blue-400">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <input type="number" id="entryPrice" ,
                                class="bg-white dark:bg-slate-800 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-700 text-sm rounded-xl focus:ring-blue-500 block w-full pl-10 p-3.5 transition-all"
                                placeholder="50.00" min="0.0001" step="0.0001" value="50">
                        </div>
                    </div>

                    <!-- Stop Loss -->
                    <div>
                        <label for="stopLoss"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-2.5 uppercase tracking-wider">Stop Loss
                            (₹)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center text-red-400">
                                <i class="fas fa-stop-circle"></i>
                            </div>
                            <input type="number" id="stopLoss" ,
                                class="bg-white dark:bg-slate-800 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-700 text-sm rounded-xl focus:ring-blue-500 block w-full pl-10 p-3.5 transition-all"
                                placeholder="48.00" , min="0.0001" , step="0.0001" , value="48">
                        </div>
                    </div>

                    <!-- Take Profit -->
                    <div>
                        <label for="takeProfit"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-2.5 uppercase tracking-wider">Take Profit
                            (₹)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center text-green-400">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <input type="number" id="takeProfit" ,
                                class="bg-white dark:bg-slate-800 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-700 text-sm rounded-xl focus:ring-blue-500 block w-full pl-10 p-3.5 transition-all"
                                placeholder="55.00" , min="0.0001" , step="0.0001" , value="55">
                        </div>
                    </div>

                    <!-- Leverage -->
                    <div>
                        <label for="leverage"
                            class="block text-sm font-medium text-blue-600 dark:text-blue-100 mb-3 uppercase tracking-wider">Leverage:
                            <span id="leverage-value" class="text-blue-400">1x</span></label>
                        <div class="flex items-center">
                            <i class="fas fa-angle-left text-blue-400 mr-2 cursor-pointer text-sm"
                                id="decrease-leverage"></i>
                            <input type="range" id="leverage-slider" min="1" max="10" value="1"
                                class="w-full h-1.5 dark:bg-slate-700 bg-slate-300 rounded-lg appearance-none cursor-pointer">
                            <i class="fas fa-angle-right text-blue-400 ml-2 cursor-pointer text-sm"
                                id="increase-leverage"></i>
                        </div>
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>1x</span>
                            <span>10x</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Results Section -->
        <div>
            <div class="glow-card rounded-2xl p-6 h-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700">

                <div class="flex items-center justify-between mb-6">
                    <h2 class="title-font text-2xl font-semibold text-white">
                        <i class="fas fa-chart-pie text-blue-400 mr-3"></i>
                        Risk Analysis
                    </h2>
                    <div class="h-8 w-8 rounded-full dark:bg-blue-900/30 bg-blue-500/30 flex items-center justify-center text-blue-400">
                        <i class="fas fa-info-circle"></i>
                    </div>
                </div>

                <!-- Summary Stats -->
                <div class="space-y-4 mb-6">
                    <div
                        class="result-card bg-white dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700 rounded-xl p-5 border border-blue-900/20">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-xs text-blue-300 uppercase tracking-wider mb-1 font-medium">Risk Amount
                                </p>
                                <p id="riskAmount" class="text-2xl font-bold text-blue-400">₹200.00</p>
                            </div>
                            <div
                                class="h-12 w-12 rounded-xl dark:bg-blue-900/30 bg-blue-500/30 backdrop-blur flex items-center justify-center">
                                <i class="fas fa-shield-alt text-blue-400 text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <div
                        class="result-card bg-white dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700 rounded-xl p-5 border border-emerald-900/20">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-xs text-emerald-300 uppercase tracking-wider mb-1 font-medium">Reward
                                    Amount</p>
                                <p id="rewardAmount" class="text-2xl font-bold text-emerald-400">₹500.00</p>
                            </div>
                            <div
                                class="h-12 w-12 rounded-xl dark:bg-emerald-900/30 bg-emerald-500/30 backdrop-blur flex items-center justify-center">
                                <i class="fas fa-trophy text-emerald-400 text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- RR Ratio and Position Size -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="result-card bg-white dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700 rounded-xl p-4 border">
                        <p class="text-xs text-gray-400 uppercase tracking-wider mb-1">Risk/Reward</p>
                        <p id="riskRewardRatio" class="text-xl font-semibold">1:2.5</p>
                        <div class="flex items-center text-xs text-yellow-400 mt-1">
                            <i class="fas fa-star mr-1"></i>
                            <span>Good</span>
                        </div>
                    </div>

                    <div class="result-card bg-white dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700 rounded-xl p-4 border">
                        <p class="text-xs text-gray-400 uppercase tracking-wider mb-1">Position Size</p>
                        <p id="positionSize" class="text-xl font-semibold">100 units</p>
                        <div class="flex items-center text-xs text-blue-400 mt-1">
                            <i class="fas fa-cube mr-1"></i>
                            <span>Optimal</span>
                        </div>
                    </div>
                </div>

                <!-- Risk Gauge -->
                <div class="mb-6">
                    <div class="flex justify-between mb-2">
                        <span class="text-xs font-medium text-gray-400 uppercase tracking-wider">Risk Level</span>
                        <span id="riskLevel" class="text-xs font-medium text-blue-400">Moderate</span>
                    </div>
                    <div class="w-full dark:bg-slate-800 bg-slate-300 rounded-full risk-gauge">
                        <div id="riskGauge"
                            class="h-full rounded-full bg-gradient-to-r from-emerald-500 via-yellow-400 to-red-500"
                            style="width: 1%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1 px-1">
                        <span class="text-emerald-400">Low</span>
                        <span class="text-yellow-400">Medium</span>
                        <span class="text-red-400">High</span>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>