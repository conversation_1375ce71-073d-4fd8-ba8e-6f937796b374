<?php

namespace App\Controllers;

use \App\Models\UserModel;
use \App\Models\TradeModel;
use \App\Models\PnlGoalsModel;

helper('cookie');

class Strategy extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
        $this->trademodel = new TradeModel();
        $this->pnlgoalsmodel = new PnlGoalsModel();
    }

    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'Strategies';
        $data['active'] = 'strategy';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'strategy';
        $data['main_content'] = 'pages/strategy';

        return view('includes/template', $data);
    }

    public function fetchStrategyCards()
{
    if (!$this->request->isAJAX()) {
        return $this->response->setJSON([]);
    }

    $months = (int) $this->request->getPost('months');
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';
    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

    $startDate = date('Y-m-d', strtotime("-{$months} months"));

    $db = \Config\Database::connect();

    // Step 1: Get total trades count for user (for usage %)
    $totalQuery = $db->table('trades')
        ->selectCount('id', 'total')
        ->where('user_id', $userId)
        ->where('market_type', $marketTypeFilter)
        ->where('deleted_at', null)
        ->where('datetime >=', $startDate)
        ->get()
        ->getRowArray();

    $totalTrades = $totalQuery['total'] ?? 1;

    // Step 2: Aggregate by strategy
    $builder = $db->table('trades t');
    $builder->select("
        s.strategy, s.id AS sid, s.user_id as strategy_owner_id,
        COUNT(t.id) as trades_count,
        SUM(CASE WHEN t.pnl_amount > 0 THEN t.pnl_amount ELSE 0 END) as total_profit,
        ABS(SUM(CASE WHEN t.pnl_amount < 0 THEN t.pnl_amount ELSE 0 END)) as total_loss,
        SUM(CASE WHEN t.pnl_amount > 0 THEN 1 ELSE 0 END) as winning_trades,
        AVG((t.entry_price - t.stop_loss) / t.entry_price * 100) as avg_risk,
        SUM(t.pnl_amount) as net_profit
    ");
    $builder->join('strategies s', 's.id = t.strategy', 'left');
    $builder->where('t.user_id', $userId);
    $builder->where('t.strategy IS NOT NULL');
    $builder->where('t.market_type', $marketTypeFilter);
    $builder->where('t.deleted_at', null);
    $builder->where('t.datetime >=', $startDate);
    $builder->groupBy('t.strategy');

    $query = $builder->get();
    $results = $query->getResultArray();

    // Step 3: Prepare final data
    $formatted = [];
    foreach ($results as $row) {
        $usagePercent = round(($row['trades_count'] / $totalTrades) * 100, 1);
        $profitFactor = ($row['total_loss'] > 0)
            ? round($row['total_profit'] / $row['total_loss'], 2)
            : 'Perfect';

        $formatted[] = [
            'strategy'       => $row['strategy'],
            'id'             => $row['sid'],
            'usage_percent'  => $usagePercent,
            'profit_factor'  => $profitFactor,
            'avg_risk'       => round($row['avg_risk'], 2) . '%',
            'total_profit'   => '+$' . number_format($row['net_profit'], 2),
            'win_rate'       => round(($row['winning_trades'] / $row['trades_count']) * 100, 1),
            'is_owner'       => $row['strategy_owner_id'] == $userId
        ];
    }

    return $this->response->setJSON($formatted);
}


    public function updateStrategy()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type.'
            ]);
        }

        $id = $this->request->getVar('id');
        $strategy = trim($this->request->getVar('strategy'));
        $description = trim($this->request->getVar('description'));
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        // Validate input
        if (empty($id) || empty($strategy) || empty($description)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'All fields are required.'
            ]);
        }

        $db = \Config\Database::connect();
        $builder = $db->table('strategies');

        // Ensure the strategy belongs to the logged-in user
        $existing = $builder->where('id', $id)
            ->where('user_id', $userId)
            ->get()
            ->getRow();

        if (!$existing) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Strategy not found or access denied.'
            ]);
        }

        // Update the strategy
        $builder->where('id', $id)
            ->update([
                'strategy' => $strategy,
                'description' => $description,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Strategy updated successfully.'
        ]);
    }


    public function recentTrades()
{
    if (!$this->request->isAJAX()) {
        return $this->response->setJSON(['success' => false, 'message' => 'Invalid Request']);
    }

    $page = (int) $this->request->getPost('page') ?: 1;
    $perPage = 4;
    $offset = ($page - 1) * $perPage;

    $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
    $marketTypeFilter = $this->request->getPost('marketTypeFilter') ?? '1';

    $db = \Config\Database::connect();

    // Fetch paginated trades
    $builder = $db->table('trades t');
    $builder->select('t.*, s.strategy AS strategy_name');
    $builder->join('strategies s', 's.id = t.strategy', 'left');
    $builder->where('t.user_id', $userId);
    $builder->where('t.market_type', $marketTypeFilter);
    $builder->where('t.deleted_at', null);
    $builder->where('t.strategy !=', null);
    $builder->orderBy('t.datetime', 'DESC');
    $builder->limit($perPage, $offset);

    $trades = $builder->get()->getResultArray();

    // Total count for pagination
    $total = $db->table('trades')
        ->where('user_id', $userId)
        ->where('market_type', $marketTypeFilter)
        ->where('deleted_at', null)
        ->where('strategy !=', null)
        ->countAllResults();

    return $this->response->setJSON([
        'trades' => $trades,
        'total' => $total,
        'page' => $page,
        'per_page' => $perPage,
        'total_pages' => ceil($total / $perPage)
    ]);
}


    public function getEditStrategyData()
    {
        $id = $this->request->getVar('id');

        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Strategy ID is required.'
            ]);
        }

        $db = \Config\Database::connect();
        $builder = $db->table('strategies');
        $strategyDetails = $builder->where('id', $id)->where('deleted_at', null)->get()->getRowArray();

        if ($strategyDetails) {
            return $this->response->setJSON([
                'success' => true,
                'data' => $strategyDetails
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Strategy not found.'
            ]);
        }
    }

    public function getStrategyDetails()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid Request']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $strategyId = $this->request->getPost('id');
        $monthFilter = (int) $this->request->getPost('months');

        if (!in_array($monthFilter, [1, 3, 12])) {
            $monthFilter = 1;
        }

        $fromDate = date('Y-m-d', strtotime("-$monthFilter months"));
        $db = \Config\Database::connect();

        // Get strategy info
        $strategy = $db->table('strategies')
            ->select('id, strategy, description')
            ->where('id', $strategyId)
            ->where('deleted_at', null)
            ->get()
            ->getRowArray();

        if (!$strategy) {
            return $this->response->setJSON(['success' => false, 'message' => 'Strategy not found']);
        }

        // Fetch relevant trades
        $trades = $db->table('trades')
            ->where('strategy', $strategyId)
            ->where('user_id', $userId)
            ->where('deleted_at', null)
            ->where('datetime >=', $fromDate)
            ->get()
            ->getResultArray();

        // Init counters
        $totalPnl = 0;
        $totalProfit = 0;
        $totalLoss = 0;
        $winningTrades = 0;
        $losingTrades = 0;
        $totalTrades = count($trades);
        $totalRiskPercent = 0;
        $riskCount = 0;

        foreach ($trades as $trade) {
            $pnl = (float)$trade['pnl_amount'];
            $ep = (float)$trade['entry_price'];
            $sl = (float)$trade['stop_loss'];

            $totalPnl += $pnl;

            if ($pnl >= 0) {
                $winningTrades++;
                $totalProfit += $pnl;
            } else {
                $losingTrades++;
                $totalLoss += abs($pnl);
            }

            // Calculate % risk per trade
            if ($ep > 0 && $sl > 0) {
                $riskPercent = abs($ep - $sl) / $ep * 100;
                $totalRiskPercent += $riskPercent;
                $riskCount++;
            }
        }

        // Final calculated metrics
        $winRate = $totalTrades > 0 ? ($winningTrades / $totalTrades) * 100 : 0;
        $profitFactor = ($totalLoss > 0) ? ($totalProfit / $totalLoss) : ($totalProfit > 0 ? INF : 0);
        $riskPerTrade = $riskCount > 0 ? ($totalRiskPercent / $riskCount) : 0;

        $metrics = [
            'win_rate' => round($winRate, 2),
            'total_profit' => round($totalPnl, 2), // Net PnL (includes losses)
            'profit_factor' => is_infinite($profitFactor) ? '∞' : round($profitFactor, 2),
            'risk_per_trade' => round($riskPerTrade, 2) // ✅ Now in %
        ];

        // Recent 3 trades for this strategy
        $recentTrades = $db->table('trades')
            ->select('symbol, trade_type, pnl_percent, datetime')
            ->where('strategy', $strategyId)
            ->where('user_id', $userId)
            ->where('deleted_at', null)
            ->where('datetime >=', $fromDate)
            ->orderBy('datetime', 'DESC')
            ->limit(3)
            ->get()
            ->getResultArray();

        return $this->response->setJSON([
            'success' => true,
            'strategy' => $strategy,
            'metrics' => $metrics,
            'trades' => $recentTrades,
        ]);
    }

    public function saveUserStrategy()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $strategy = trim($this->request->getPost('name'));
        $description = trim($this->request->getPost('description'));

        if (empty($strategy) || empty($description)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'All fields are required.'
            ]);
        }

        $data = [
            'user_id'     => $userId,
            'strategy'    => $strategy,
            'description' => $description,
            'created_at'  => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $builder = $db->table('strategies');
        $builder->insert($data);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Strategy saved successfully'
        ]);
    }
}
