<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAngelOneTotpKey extends Migration
{
    public function up()
    {
        // Add TOTP key field to users table for Angel One
        $fields = [
            'angel_totp_key' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Angel One TOTP secret key (encrypted)'
            ]
        ];

        $this->forge->addColumn('users', $fields);
    }

    public function down()
    {
        // Remove TOTP key field from users table
        $this->forge->dropColumn('users', ['angel_totp_key']);
    }
}
