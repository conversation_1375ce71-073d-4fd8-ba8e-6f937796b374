<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login | Advanced Trading Dashboard</title>
    <script src="<?= base_url() ?>assets/tailwind-3.4.16/tailwind.js"></script>
    <link rel="stylesheet" href="<?= base_url() ?>assets/font-awesome-pro-5/css/all.min.css">
    <script src="<?= base_url() ?>assets/login/tailwind-config.js"></script>
    <link rel="stylesheet" href="<?= base_url() ?>assets/login/style.css?random=<?= rand() ?>">
</head>

<body class="dark:bg-dark-900 min-h-screen flex items-center justify-center p-4">
    <!-- Toast Container - Updated for top sliding -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="w-full max-w-md relative">
        <!-- Dark Mode Toggle -->
        <!-- <button id="darkModeToggle"
            class="absolute -top-12 right-0 p-3 rounded-full bg-white dark:bg-dark-800 shadow-lg hover:shadow-xl transition-all duration-300 z-10">
            <i class="fas fa-moon dark:hidden text-gray-700"></i>
            <i class="fas fa-sun hidden dark:inline text-yellow-300"></i>
        </button> -->

        <!-- Floating Background Elements -->
        <div class="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-primary-400 opacity-20 blur-3xl animate-float">
        </div>
        <div
            class="absolute -bottom-20 -right-20 w-40 h-40 rounded-full bg-purple-400 opacity-20 blur-3xl animate-float animation-delay-2000">
        </div>

        <!-- Auth Card -->
        <div class="auth-card rounded-2xl shadow-xl overflow-hidden relative z-0">
            <!-- Tab Indicator -->
            <div id="tabIndicator" class="absolute top-0 left-0 h-1 bg-primary-500 w-1/2"></div>

            <!-- Tabs -->
            <div class="flex">
                <button id="loginTab"
                    class="flex-1 py-5 px-6 text-center font-semibold text-primary-600 dark:text-primary-400">Login</button>
                <button id="registerTab"
                    class="flex-1 py-5 px-6 text-center font-semibold text-gray-500 dark:text-gray-400">Register</button>
            </div>

            <!-- Forms Container -->
            <div class="px-8 pb-8 pt-2">
                <!-- Login Form -->
                <div id="loginForm" class="space-y-6">
                    <div class="text-center">
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Welcome back</h2>
                        <p class="text-gray-500 dark:text-gray-400 mt-1">Sign in to your account</p>
                    </div>

                    <!-- Google Login Button -->
                    <button
                        class="social-btn w-full flex items-center justify-center py-3 px-4 rounded-xl bg-white dark:bg-dark-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all"
                        id="google-signin">
                        <img src="https://www.google.com/favicon.ico" alt="Google" class="w-5 h-5 mr-3">
                        <span class="text-gray-700 dark:text-gray-200 font-medium">Continue with Google</span>
                    </button>

                    <div class="flex items-center my-4">
                        <div class="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
                        <span class="px-3 text-gray-400 dark:text-gray-500 text-sm">or</span>
                        <div class="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
                    </div>

                    <form class="space-y-4" id="loginFormElement">
                        <div class="input-group">
                            <label for="loginEmail" class="input-label">Email address</label>
                            <div class="input-container">
                                <input type="email" id="loginEmail" name="loginEmail" class="form-input logReq">
                                <div id="loginEmailError" class="error-message">Please enter a valid email address</div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="loginPassword" class="input-label">Password</label>
                            <div class="input-container">
                                <input type="password" id="loginPassword" name="loginPassword" class="form-input logReq"
                                    minlength="6">
                                <span class="toggle-password absolute right-3 top-3 text-gray-400 dark:text-gray-500">
                                    <i class="far fa-eye-slash" id="toggleLoginPassword"></i>
                                </span>
                                <div id="loginPasswordError" class="error-message">Password must be at least 6
                                    characters</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input id="rememberMe" type="checkbox" class="checkbox">
                                <label for="rememberMe"
                                    class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Remember me</label>
                            </div>
                            <a href="#"
                                class="text-sm font-medium text-primary-600 dark:text-primary-400 hover:underline">Forgot
                                password?</a>
                        </div>

                        <button type="button"
                            class="w-full py-3 px-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-xl shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden"
                            onclick="signin('<?= base_url('signin') ?>')">
                            Sign In
                        </button>
                    </form>

                    <p class="text-center text-sm text-gray-600 dark:text-gray-400">
                        Don't have an account?
                        <button class="toggle-form text-primary-600 dark:text-primary-400 font-medium hover:underline"
                            data-target="register">Sign up</button>
                    </p>
                </div>

                <!-- Register Form -->
                <div id="registerForm" class="space-y-6 hidden">
                    <div class="text-center">
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Create account</h2>
                        <p class="text-gray-500 dark:text-gray-400 mt-1">Join us today</p>
                    </div>

                    <!-- Google Register Button -->
                    <button id="google-signup"
                        class="social-btn w-full flex items-center justify-center py-3 px-4 rounded-xl bg-white dark:bg-dark-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
                        <img src="https://www.google.com/favicon.ico" alt="Google" class="w-5 h-5 mr-3">
                        <span class="text-gray-700 dark:text-gray-200 font-medium">Continue with Google</span>
                    </button>

                    <div class="flex items-center my-4">
                        <div class="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
                        <span class="px-3 text-gray-400 dark:text-gray-500 text-sm">or</span>
                        <div class="flex-1 border-t border-gray-200 dark:border-gray-700"></div>
                    </div>

                    <form id="registerFormElement" method="POST" action="#" enctype="multipart/formdata"
                        class="space-y-4">
                        <div class="input-group">
                            <label for="registerName" class="input-label">Full name</label>
                            <div class="input-container">
                                <input type="text" id="registerName" autocomplete="off" name="registerName"
                                    class="form-input regReq" minlength="3">
                                <div id="registerNameError" class="error-message">Name must be at least 3 characters
                                </div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="registerEmail" class="input-label">Email address</label>
                            <div class="input-container">
                                <input type="email" id="registerEmail" autocomplete="off" name="registerEmail"
                                    class="form-input regReq">
                                <div id="registerEmailError" class="error-message">Please enter a valid email address
                                </div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="registerPassword" class="input-label">Password</label>
                            <div class="input-container">
                                <input type="password" id="registerPassword" autocomplete="off" name="registerPassword"
                                    class="form-input regReq" minlength="6">
                                <span class="toggle-password absolute right-3 top-3 text-gray-400 dark:text-gray-500">
                                    <i class="far fa-eye-slash" id="toggleRegisterPassword"></i>
                                </span>
                                <div id="registerPasswordError" class="error-message">Password must be at least 6
                                    characters</div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="registerConfirmPassword" class="input-label">Confirm password</label>
                            <div class="input-container">
                                <input type="password" id="registerConfirmPassword" autocomplete="off"
                                    name="registerConfirmPassword" class="form-input regReq">
                                <span class="toggle-password absolute right-3 top-3 text-gray-400 dark:text-gray-500">
                                    <i class="far fa-eye-slash" id="toggleRegisterConfirmPassword"></i>
                                </span>
                                <div id="registerConfirmPasswordError" class="error-message">Passwords do not match
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input id="acceptTerms" name="acceptTerms" type="checkbox" class="checkbox" required>
                            <label for="acceptTerms" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                I agree to the <a href="<?= base_url('terms-and-conditions') ?>"
                                    class="text-primary-600 dark:text-primary-400 hover:underline">Terms</a> and <a
                                    href="<?= base_url('privacy-policy') ?>" class="text-primary-600 dark:text-primary-400 hover:underline">Privacy
                                    Policy</a>
                            </label>
                            <div id="acceptTermsError" class="error-message ml-2">You must accept the terms</div>
                        </div>

                        <button type="button"
                            class="w-full py-3 px-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-xl shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden"
                            onclick="signup('<?= base_url('signup') ?>')">
                            Sign Up
                        </button>
                    </form>

                    <p class="text-center text-sm text-gray-600 dark:text-gray-400">
                        Already have an account?
                        <button class="toggle-form text-primary-600 dark:text-primary-400 font-medium hover:underline"
                            data-target="login">Sign in</button>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const base_url = '<?= base_url() ?>';
    </script>

    <script src="<?= base_url() ?>assets/jquery/jquery.min.js"></script>
    <script src="<?= base_url() ?>assets/login/main.js?random=<?= rand() ?>"></script>

    <script>
        document.getElementById('google-signup').addEventListener('click', function () {
            window.location.href = "<?= base_url('googleLogin') ?>";
        });

        document.getElementById('google-signin').addEventListener('click', function () {
            window.location.href = "<?= base_url('googleLogin') ?>";
        });
    </script>
</body>

</html>