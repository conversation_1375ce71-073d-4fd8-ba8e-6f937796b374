<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Community Profile' ?> - Trade Diary</title>
    <link rel="icon" type="image/x-icon" href="<?= base_url() ?>assets/images/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="<?= base_url() ?>assets/jquery/jquery.min.js"></script>
</head>
<body class="bg-gray-900 text-gray-100">

<?php
// Get user badge display
$badgeConfig = [
    'pro' => ['icon' => 'fas fa-crown', 'color' => 'text-yellow-400', 'label' => 'Pro Trader'],
    'gainer' => ['icon' => 'fas fa-chart-line', 'color' => 'text-green-400', 'label' => 'Top Gainer'],
    'master' => ['icon' => 'fas fa-star', 'color' => 'text-purple-400', 'label' => 'Master Trader'],
    'analyst' => ['icon' => 'fas fa-brain', 'color' => 'text-blue-400', 'label' => 'Market Analyst'],
    'none' => ['icon' => 'fas fa-user', 'color' => 'text-gray-400', 'label' => 'Community Member']
];

$userBadge = $badgeConfig[$userDetails['badge'] ?? 'none'];
?>

<style>
    :root {
        --primary: #4fd1c5;
        --primary-dark: #319795;
        --secondary: #805ad5;
        --secondary-dark: #6b46c1;
        --accent: #f687b3;
        --accent-dark: #e53e3e;
        --dark: #1a202c;
        --darker: #171923;
        --light: #f7fafc;
        --gray: #e2e8f0;
        --dark-gray: #2d3748;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    ::-webkit-scrollbar-track {
        background: var(--darker);
        border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb {
        background: var(--primary);
        border-radius: 4px;
        transition: background 0.3s ease;
    }
    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }

    /* Smooth scrolling */
    .smooth-scroll {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    .scroll-container {
        will-change: scroll-position;
    }

    /* Animation for buttons */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    .pulse-animation {
        animation: pulse 1.5s infinite;
    }

    /* Custom glow effect */
    .glow-effect {
        box-shadow: 0 0 15px rgba(99, 102, 241, 0.3);
    }

    /* Modal transition */
    .modal {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .modal-enter {
        opacity: 0;
        transform: scale(0.9);
    }
    .modal-enter-active {
        opacity: 1;
        transform: scale(1);
    }
    .modal-exit {
        opacity: 1;
        transform: scale(1);
    }
    .modal-exit-active {
        opacity: 0;
        transform: scale(0.9);
    }

    /* Community sidebar styles */
    .community-sidebar {
        transition: transform 0.3s ease-in-out;
    }

    .community-sidebar.collapsed {
        transform: translateX(-100%);
    }

    @media (max-width: 768px) {
        .community-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 50;
            transform: translateX(-100%);
        }

        .community-sidebar.mobile-open {
            transform: translateX(0);
        }

        .mobile-header {
            display: block;
            background: #1f2937;
            padding: 1rem;
            border-bottom: 1px solid #374151;
        }

        .mobile-menu-btn {
            background: #374151;
            color: #d1d5db;
            border: none;
            padding: 0.5rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .mobile-menu-btn:hover {
            background: #4b5563;
            color: #ffffff;
        }
    }

    @media (min-width: 769px) {
        .mobile-header {
            display: none;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 640px) {
        .profile-header {
            flex-direction: column;
            text-align: center;
            align-items: center;
        }
        .profile-header .relative.group {
            margin-right: 0;
            margin-bottom: 1rem;
        }
        .profile-stats {
            justify-content: center;
        }
        .profile-actions {
            justify-content: center;
            margin-top: 1rem;
            width: 100%;
        }
        .profile-header .flex-1 {
            width: 100%;
            text-align: center;
        }
        .profile-header .flex-1 h1,
        .profile-header .flex-1 p {
            text-align: center;
        }
        .profile-header .flex-1 .bg-gray-700 {
            margin-left: auto;
            margin-right: auto;
        }
    }

    /* Navigation styles */
    .nav-item {
        transition: all 0.2s ease;
    }

    .nav-item:hover {
        transform: translateX(4px);
    }

    .nav-item.active {
        background: linear-gradient(135deg, #4fd1c5, #319795);
        box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
    }

    /* Neon text effect */
    .neon-text {
        text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    }
</style>

<!-- Community Page Container -->
<div class="community-container">
    <div class="flex h-screen overflow-hidden bg-gray-900">
        <!-- Community Sidebar -->
        <div class="community-sidebar sidebar w-64 flex-shrink-0 flex flex-col bg-gray-800 border-r border-gray-700 transition-all duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <div class="flex items-center space-x-2">
                    <div class="logo-icon">
                        <i class="fas fa-users text-2xl text-teal-400"></i>
                    </div>
                    <span class="logo-text text-xl font-bold text-teal-400 neon-text">COMMUNITY</span>
                </div>
                <button id="sidebarToggle" class="hidden md:block text-gray-400 hover:text-white focus:outline-none transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <!-- Mobile close button -->
                <button id="mobileSidebarClose" class="md:hidden text-gray-400 hover:text-white focus:outline-none transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto py-4 smooth-scroll scroll-container">
                <nav>
                    <ul class="space-y-2 px-2">
                        <li>
                            <a href="<?= base_url('community') ?>" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-home text-teal-400 w-6"></i>
                                <span class="nav-text ml-3">Home Feed</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-notifications" onclick="navigateToSection('notifications'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-bell text-yellow-400 w-6"></i>
                                <span class="nav-text ml-3">Notifications</span>
                                <span id="notificationBadge" class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 hidden">0</span>
                            </a>
                        </li>
                    </ul>

                    <div class="border-t border-gray-700 mt-4 pt-4 px-2">
                        <ul class="space-y-2">
                            <li>
                                <a href="<?= base_url('community/profile') ?>" class="nav-item flex items-center p-3 rounded-lg text-white bg-gray-700 transition-colors active">
                                    <i class="fas fa-user text-cyan-400 w-6"></i>
                                    <span class="nav-text ml-3">Community Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?= base_url('dashboard') ?>" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                    <i class="fas fa-arrow-left text-gray-400 w-6"></i>
                                    <span class="nav-text ml-3">Back to Dashboard</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>

            <div class="p-4 border-t border-gray-700">
                <div class="flex items-center">
                    <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="User" class="w-10 h-10 rounded-full border-2 border-teal-400">
                    <div class="ml-3">
                        <div class="text-sm font-medium text-white"><?= $userDetails['full_name'] ?? 'TraderX' ?></div>
                        <div class="text-xs text-gray-400">Community Member</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Community Profile Content -->
        <div class="flex-1 overflow-y-auto bg-gray-900 smooth-scroll scroll-container main-content">
            <!-- Mobile header with menu button -->
            <div class="md:hidden mobile-header">
                <div class="flex items-center justify-between">
                    <button id="mobileSidebarToggle" class="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-lg font-bold text-white">Community Profile</h1>
                    <div class="w-10"></div> <!-- Spacer for centering -->
                </div>
            </div>

            <!-- Profile Content -->
            <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- Profile Header -->
        <div class="bg-gray-800 rounded-2xl shadow-xl p-6 mb-8 relative glow-effect">
            <div class="flex flex-wrap profile-header">
                <!-- Profile Picture -->
                <div class="relative group mr-6 mb-4 sm:mb-0">
                    <div class="relative">
                        <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="Profile"
                              class="w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-indigo-600 object-cover shadow-lg">
                        <div class="absolute -bottom-2 -right-2 bg-indigo-600 rounded-full p-1">
                            <i class="<?= $userBadge['icon'] ?> text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-indigo-600 bg-opacity-70 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer">
                        <i class="fas fa-camera text-2xl"></i>
                    </div>
                </div>
                                
                <!-- Profile Info -->
                <div class="flex-1 min-w-0">
                    <div class="flex flex-col h-full">
                        <div class="flex-1">
                            <h1 id="profileName" class="text-2xl sm:text-3xl font-bold truncate"><?= esc($userDetails['full_name']) ?></h1>
                            <p class="text-indigo-400 mb-2">@<?= esc(strtolower(str_replace(' ', '_', $userDetails['full_name']))) ?></p>
                            <p id="profileBio" class="text-gray-300 mb-4 max-w-2xl">
                                <?= esc($userDetails['bio'] ?? 'Welcome to my trading journey! Sharing insights and learning from the community.') ?>
                            </p>
                            <div id="profileTradingStyle" class="bg-gray-700 <?= $userBadge['color'] ?> px-3 py-1 rounded-full text-sm inline-block mb-4">
                                <i class="<?= $userBadge['icon'] ?> mr-1"></i> <?= $userBadge['label'] ?>
                            </div>
                        </div>
                                                
                        <!-- Stats -->
                        <div class="flex flex-wrap profile-stats gap-4 sm:gap-6 mt-4 pt-4 border-t border-gray-700">
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold"><?= number_format($userDetails['posts_count']) ?></div>
                                <div class="text-gray-400 text-xs sm:text-sm">Posts</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold"><?= number_format($userDetails['followers_count']) ?></div>
                                <div class="text-gray-400 text-xs sm:text-sm">Followers</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold"><?= number_format($userDetails['following_count']) ?></div>
                                <div class="text-gray-400 text-xs sm:text-sm">Following</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl sm:text-2xl font-bold"><?= $userDetails['years_since_joining'] ?></div>
                                <div class="text-gray-400 text-xs sm:text-sm">Years</div>
                            </div>
                        </div>
                    </div>
                </div>
                                
                <!-- Profile Actions -->
                <div class="flex profile-actions space-x-2 mt-4 sm:mt-0">
                    <button id="editBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-2 py-1 rounded-md transition-colors flex items-center text-xs">
                        <i class="fas fa-edit mr-1 text-xs"></i>Edit
                    </button>
                </div>
            </div>
        </div>
                
        <!-- Content Tabs -->
        <div class="flex overflow-x-auto border-b border-gray-700 mb-6 scrollbar-hide">
            <button id="postsTab" class="px-4 py-2 font-medium text-indigo-400 border-b-2 border-indigo-400 whitespace-nowrap">
                <i class="fas fa-newspaper mr-2"></i>Your Posts
            </button>
            <button id="followersTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-users mr-2"></i>Followers (<?= number_format($userDetails['followers_count']) ?>)
            </button>
            <button id="followingTab" class="px-4 py-2 font-medium text-gray-400 hover:text-indigo-300 whitespace-nowrap">
                <i class="fas fa-user-friends mr-2"></i>Following (<?= number_format($userDetails['following_count']) ?>)
            </button>
        </div>
                
        <!-- Posts Content -->
        <div id="postsContent" class="space-y-6">
            <!-- Posts will be loaded here via AJAX -->
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading your posts...</p>
            </div>
        </div>
                
        <!-- Followers Content (Hidden by default) -->
        <div id="followersContent" class="hidden">
            <!-- Follower Filters -->
            <div class="bg-gray-800 rounded-xl p-4 mb-6 flex flex-wrap gap-3">
                <div class="relative flex-1 min-w-[200px]">
                    <input type="text" placeholder="Search followers..." class="bg-gray-700 text-white w-full px-4 py-2 rounded-lg pl-10">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <select class="bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <option>All Followers</option>
                    <option>Recently Active</option>
                    <option>Most Engaged</option>
                    <option>New Followers</option>
                </select>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-filter mr-2"></i>Filter
                </button>
            </div>
                    
            <!-- Follower List -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="followersList">
                <!-- Followers will be loaded here via AJAX -->
                <div class="text-center py-8 col-span-full">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                    <p class="text-gray-400 mt-4">Loading followers...</p>
                </div>
            </div>
        </div>
                
        <!-- Following Content (Hidden by default) -->
        <div id="followingContent" class="hidden">
            <!-- Following Filters -->
            <div class="bg-gray-800 rounded-xl p-4 mb-6 flex flex-wrap gap-3">
                <div class="relative flex-1 min-w-[200px]">
                    <input type="text" placeholder="Search following..." class="bg-gray-700 text-white w-full px-4 py-2 rounded-lg pl-10">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <select class="bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <option>All Following</option>
                    <option>Most Active</option>
                    <option>Recently Followed</option>
                    <option>Verified Only</option>
                </select>
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-filter mr-2"></i>Filter
                </button>
            </div>
                    
            <!-- Following List -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="followingList">
                <!-- Following will be loaded here via AJAX -->
                <div class="text-center py-8 col-span-full">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400 mx-auto"></div>
                    <p class="text-gray-400 mt-4">Loading following...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden modal">
        <div class="bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold">
                    <i class="fas fa-user-edit mr-2 text-indigo-400"></i> Edit Profile
                </h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="profileForm">
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-400 mb-1">Name</label>
                        <input type="text" id="editName" value="<?= esc($userDetails['full_name']) ?>"
                               class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-1">Bio</label>
                        <textarea id="editBio" class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg h-24 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Tell us about your trading journey..."><?= esc($userDetails['bio'] ?? '') ?></textarea>
                    </div>

                    <div>
                        <label class="block text-gray-400 mb-1">Badge</label>
                        <select id="editBadge" class="bg-gray-700 text-white w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="none" <?= ($userDetails['badge'] ?? 'none') === 'none' ? 'selected' : '' ?>>Community Member</option>
                            <option value="pro" <?= ($userDetails['badge'] ?? 'none') === 'pro' ? 'selected' : '' ?>>Pro Trader</option>
                            <option value="gainer" <?= ($userDetails['badge'] ?? 'none') === 'gainer' ? 'selected' : '' ?>>Top Gainer</option>
                            <option value="master" <?= ($userDetails['badge'] ?? 'none') === 'master' ? 'selected' : '' ?>>Master Trader</option>
                            <option value="analyst" <?= ($userDetails['badge'] ?? 'none') === 'analyst' ? 'selected' : '' ?>>Market Analyst</option>
                        </select>
                    </div>

                    <div class="pt-2">
                        <button type="submit" id="saveProfile" class="bg-indigo-600 hover:bg-indigo-700 text-white w-full py-2 rounded-lg transition-colors">
                            <i class="fas fa-save mr-2"></i>Save Changes
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Mobile sidebar overlay -->
<div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

<script>
// Set base URL for JavaScript
const base_url = '<?= base_url() ?>';

// Sidebar functionality
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.community-sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const mobileSidebarClose = document.getElementById('mobileSidebarClose');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    // Desktop sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('collapsed')) {
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-right');
            } else {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
            }
        });
    }

    // Mobile sidebar toggle
    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', function() {
            sidebar.classList.add('mobile-open');
            sidebarOverlay.classList.remove('hidden');
        });
    }

    // Mobile sidebar close
    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', function() {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.add('hidden');
        });
    }

    // Close sidebar when clicking overlay
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.add('hidden');
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.add('hidden');
        }
    });
});
</script>

<!-- Load community profile JavaScript -->
<script src="<?= base_url() ?>assets/scripts/community_profile.js"></script>

</body>
</html>
