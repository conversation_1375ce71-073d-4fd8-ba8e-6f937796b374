<?php

namespace App\Controllers;

use App\Models\TransactionModel;

use Datetime;

use Razorpay\Api\Api;
require(APPPATH . 'ThirdParty/Razorpay/Razorpay.php');

class PaymentController extends BaseController
{

    public function __construct()
    {
        $this->transactionmodel = new TransactionModel();
    }

    public function createOrder()
    {
        $db = \Config\Database::connect();

        $period = $this->request->getVar('period');

        if ($period == 'annual') {
            $amount = 999;
        }
        else if ($period == 'rupee') {
            $amount = 1;
        }
        else{
            $amount = 299;
        }

        $api_key = "***********************";
        $api_secret = "cPJ4nLW8LeIicYBCLG0ZhWUT";

        try {
            $recieptValue = $db->query("SELECT reciept FROM reciepts WHERE id = 1")->getRowArray()['reciept'];
            $reciept = 'TDPAY' . str_pad($recieptValue, 5, '0', STR_PAD_LEFT);
            // Create Razorpay order
            $api = new Api($api_key, $api_secret);
            $order = $api->order->create([
                'receipt' => $reciept,
                'amount' => $amount * 100,
                'currency' => 'INR',
                'notes' => []
            ]);

            if (!isset($order['id'])) {
                throw new Exception("Failed to create order with Razorpay.");
            }

            $order_id = $order['id'];
            $amount = $order['amount'];

            $db->query("UPDATE reciepts SET reciept = reciept + 1 WHERE id = 1");

            // Return JSON response
            return $this->response->setJSON([
                'status' => 'success',
                'order_id' => $order_id,
                'amount' => $amount,
                'period' => $period,
            ]);
        } catch (Exception $e) {
            log_message('error', 'Razorpay Order Error: ' . $e->getMessage());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveTransaction()
    {
        $request = $this->request;

        $data = [
            'status' => $request->getPost('status'),
            'payment_id' => $request->getPost('payment_id'),
            'order_id' => $request->getPost('order_id'),
            'signature' => $request->getPost('signature'),
            'amount' => $request->getPost('amount'),
            'period' => $request->getPost('period'),
            'error_reason' => $request->getPost('error_reason'),
            'created_at' => date('Y-m-d H:i:s'),
        ];

        if ($this->transactionmodel->insert($data)) {
            if ($data['status'] == 'success') {
                $this->response->setCookie([
                    'name' => 'osrgdthfudtrshf',
                    'value' => $data['payment_id'],
                    'expire' => 60 * 60 * 24, // 1 day
                    'httponly' => true,
                    // 'secure' => true, // if using HTTPS
                ]);
            }
            echo json_encode(['status' => true, 'message' => 'Transaction details saved.']);
        } else {
            echo json_encode(['status' => false, 'message' => 'Failed to save transaction.'], 500);
        }
    }

    public function subscribe()
    {
        $data['title'] = "Subscribe";
        $data['active'] = "dashboard";
        $data['userDetails'] = $this->usermodel->find($this->decrypt_cookie_value(get_cookie('user_id')));
        $pid = $data['userDetails']['payment_id'];

        $result = $this->usermodel->query(
            "SELECT `period` FROM transactions WHERE payment_id = ? AND `status` = 'success' LIMIT 1",
            [$pid]
        )->getRowArray();

        $data['lastPlanDuration'] = $result['period'] ?? 0;
        $data['tags'] = $this->tagsmodel->findAll();
        $data['sub_active'] = "";
        $data['customScript'] = "renewal";
        $data['main_content'] = "Frontend/subscribe";
        return view('Frontend/includes/template', $data);
    }

    public function saveRenewalTransaction()
    {
        $request = $this->request;

        $data = [
            'status' => $request->getPost('status'),
            'payment_id' => $request->getPost('payment_id'),
            'order_id' => $request->getPost('order_id'),
            'signature' => $request->getPost('signature'),
            'amount' => $request->getPost('amount'),
            'period' => $request->getPost('period'),
            'error_reason' => $request->getPost('error_reason'),
            'created_at' => date('Y-m-d H:i:s'),
        ];

        if ($this->transactionmodel->insert($data)) {

            $userId = $this->decrypt_cookie_value(get_cookie('user_id'));
            $user = $this->usermodel->find($userId);

            $period = (int) $data['period']; // assume in months
            $now = new DateTime();
            $subscriptionEnd = new DateTime($user['subscriptionEnd']);

            if ($subscriptionEnd > $now) {
                // Subscription is active, extend from current end date
                $newStart = new DateTime($user['subscriptionStart']);
                $newEnd = $subscriptionEnd->modify("+$period months");
            } else {
                // Subscription expired, start from now
                $newStart = $now;
                $newEnd = (clone $now)->modify("+$period months");
            }

            // Update the user's subscription
            $this->usermodel->update($userId, [
                'subscriptionStart' => $newStart->format('Y-m-d H:i:s'),
                'subscriptionEnd' => $newEnd->format('Y-m-d H:i:s'),
                'payment_id' => $request->getPost('payment_id'),
            ]);

            echo json_encode(['status' => true, 'message' => 'Transaction details saved and subscription updated.']);
        } else {
            echo json_encode(['status' => false, 'message' => 'Failed to save transaction.'], 500);
        }
    }

}