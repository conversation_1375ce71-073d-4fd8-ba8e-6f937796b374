<style>
    .ai-test-container {
        min-height: 100vh;
        background: #f8fafc;
        padding: 24px;
    }

    .dark .ai-test-container {
        background: #0f172a;
    }

    .max-container {
        max-width: 1400px;
        margin: 0 auto;
    }

    .test-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
    }

    .dark .test-card {
        background: #1e293b;
        border: 1px solid #334155;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Mobile Responsive Main Container */
    @media (max-width: 768px) {
        .ai-test-container {
            padding: 16px;
        }

        .max-container {
            max-width: 100%;
        }

        .test-card {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    }

    @media (max-width: 480px) {
        .ai-test-container {
            padding: 12px;
        }

        .test-card {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
        }
    }

    .analysis-result {
        margin-top: 24px;
    }

    .loading-container {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid #cbd5e1;
        border-radius: 20px;
        padding: 60px 40px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .dark .loading-container {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        border: 1px solid #334155;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .loading-container {
            padding: 40px 20px;
            border-radius: 16px;
            margin: 0 16px;
            width: 100%;
            max-width: 100%;
        }
    }
    
    @media (max-width: 480px) {
        .loading-container {
            padding: 30px 15px;
            border-radius: 14px;
            margin: 0;
            width: 100%;
        }
    }

    .loading-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .ai-icon-container {
        width: 120px;
        height: 120px;
        margin: 0 auto 40px;
        border-radius: 50%;
        background: conic-gradient(from 0deg, #3b82f6, #8b5cf6, #ec4899, #3b82f6);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        animation: rotate 3s linear infinite;
    }

    @media (max-width: 768px) {
        .ai-icon-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
        }
    }

    .ai-icon-container::before {
        content: '';
        position: absolute;
        inset: 3px;
        border-radius: 50%;
        background: #ffffff;
        z-index: 1;
    }

    .dark .ai-icon-container::before {
        background: #1e293b;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .ai-icon-inner {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6);
        position: relative;
        z-index: 2;
        animation: pulse 2s ease-in-out infinite;
    }

    .ai-icon-inner i {
        font-size: 32px;
        color: white;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 768px) {
        .ai-icon-inner {
            width: 64px;
            height: 64px;
        }

        .ai-icon-inner i {
            font-size: 24px;
        }
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 10px;
        overflow: hidden;
        margin: 40px 0;
        position: relative;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .dark .progress-bar {
        background: #334155;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 768px) {
        .progress-bar {
            margin: 30px 0;
            height: 6px;
        }
    }

    @media (max-width: 480px) {
        .progress-bar {
            margin: 25px 0;
            height: 5px;
        }
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
        background-size: 200% 100%;
        border-radius: 10px;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        animation: progressGlow 2s ease-in-out infinite;
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }

    @keyframes progressGlow {
        0%, 100% {
            background-position: 0% 50%;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        }
        50% {
            background-position: 100% 50%;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.7);
        }
    }

    .step-indicators {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
        margin-top: 50px;
        padding: 0 20px;
    }

    @media (max-width: 768px) {
        .step-indicators {
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 40px;
            padding: 0 10px;
        }

        .step-indicators .step-indicator:nth-child(4),
        .step-indicators .step-indicator:nth-child(5) {
            grid-column: span 1;
        }

        .step-indicators .step-indicator:nth-child(4) {
            grid-column: 1 / 2;
            grid-row: 2;
        }

        .step-indicators .step-indicator:nth-child(5) {
            grid-column: 3 / 4;
            grid-row: 2;
        }
    }

    @media (max-width: 480px) {
        .step-indicators {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 30px;
            padding: 0 5px;
        }

        .step-indicators .step-indicator:nth-child(4),
        .step-indicators .step-indicator:nth-child(5) {
            grid-column: span 1;
            grid-row: auto;
        }

        .step-indicators .step-indicator:nth-child(5) {
            grid-column: 1 / 3;
            justify-self: center;
        }
    }

    .step-indicator {
        text-align: center;
        position: relative;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        margin: 0 auto 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid #cbd5e1;
        background: #f1f5f9;
        color: #64748b;
        font-weight: 600;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .dark .step-circle {
        border: 3px solid #475569;
        background: #334155;
        color: #94a3b8;
    }

    @media (max-width: 768px) {
        .step-circle {
            width: 40px;
            height: 40px;
            margin: 0 auto 12px;
            border: 2px solid #cbd5e1;
        }

        .dark .step-circle {
            border: 2px solid #475569;
        }
    }

    @media (max-width: 480px) {
        .step-circle {
            width: 36px;
            height: 36px;
            margin: 0 auto 10px;
        }
    }

    .step-circle::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .step-circle.active {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-color: #3b82f6;
        color: white;
        transform: scale(1.15);
        box-shadow: 0 15px 35px -10px rgba(59, 130, 246, 0.6);
        animation: activeStep 1s ease-in-out;
    }

    .step-circle.active::before {
        left: 100%;
    }

    .step-circle.completed {
        background: linear-gradient(135deg, #10b981, #059669);
        border-color: #10b981;
        color: white;
        transform: scale(1.05);
        box-shadow: 0 10px 25px -8px rgba(16, 185, 129, 0.4);
    }

    @keyframes activeStep {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1.15); }
    }

    .step-label {
        font-size: 12px;
        font-weight: 500;
        color: #64748b;
        transition: all 0.3s ease;
        line-height: 1.4;
    }

    .dark .step-label {
        color: #64748b;
    }

    @media (max-width: 768px) {
        .step-label {
            font-size: 10px;
            line-height: 1.3;
        }
    }

    @media (max-width: 480px) {
        .step-label {
            font-size: 9px;
            line-height: 1.2;
        }
    }

    .step-label.active {
        color: #3b82f6;
        font-weight: 600;
        transform: translateY(-2px);
    }

    .step-label.completed {
        color: #10b981;
        font-weight: 600;
    }

    .loading-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 12px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        animation: titlePulse 2s ease-in-out infinite;
        transition: opacity 0.3s ease-in-out;
    }

    .dark .loading-title {
        color: white;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .loading-subtitle {
        font-size: 16px;
        color: #64748b;
        margin-bottom: 40px;
        font-weight: 400;
        line-height: 1.5;
        transition: opacity 0.3s ease-in-out;
    }

    .dark .loading-subtitle {
        color: #94a3b8;
    }

    @media (max-width: 768px) {
        .loading-title {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            font-size: 14px;
            margin-bottom: 30px;
            padding: 0 10px;
        }
    }

    @media (max-width: 480px) {
        .loading-title {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .loading-subtitle {
            font-size: 13px;
            margin-bottom: 25px;
            padding: 0 5px;
        }
    }

    @keyframes titlePulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    .did-you-know {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 12px;
        padding: 20px;
        margin-top: 40px;
        text-align: left;
    }

    .did-you-know-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #3b82f6;
        margin-bottom: 8px;
    }

    .did-you-know-title i {
        margin-right: 8px;
        font-size: 18px;
    }

    .did-you-know-text {
        font-size: 14px;
        color: #475569;
        line-height: 1.6;
    }

    .dark .did-you-know-text {
        color: #cbd5e1;
    }

    @media (max-width: 768px) {
        .did-you-know {
            padding: 16px;
            margin-top: 30px;
            border-radius: 10px;
        }

        .did-you-know-title {
            font-size: 14px;
            margin-bottom: 6px;
        }

        .did-you-know-title i {
            font-size: 16px;
            margin-right: 6px;
        }

        .did-you-know-text {
            font-size: 12px;
            line-height: 1.5;
        }
    }

    @media (max-width: 480px) {
        .did-you-know {
            padding: 12px;
            margin-top: 25px;
            width: 100%;
            box-sizing: border-box;
        }

        .did-you-know-title {
            font-size: 13px;
        }

        .did-you-know-text {
            font-size: 11px;
            word-wrap: break-word;
        }
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }

    @media (max-width: 768px) {
        .summary-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
    }

    @media (max-width: 480px) {
        .summary-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
    }

    .summary-item {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .dark .summary-item {
        background: #334155;
        border: 1px solid #475569;
    }

    @media (max-width: 768px) {
        .summary-item {
            padding: 16px;
            border-radius: 10px;
        }
    }

    @media (max-width: 480px) {
        .summary-item {
            padding: 12px;
            border-radius: 8px;
        }
    }

    .summary-value {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .summary-label {
        font-size: 14px;
        color: #64748b;
    }

    .dark .summary-label {
        color: #94a3b8;
    }

    @media (max-width: 768px) {
        .summary-value {
            font-size: 24px;
            margin-bottom: 6px;
        }

        .summary-label {
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .summary-value {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .summary-label {
            font-size: 11px;
        }
    }

    .positive { color: #10b981; }
    .negative { color: #ef4444; }
    .neutral { color: #6b7280; }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        color: white;
        padding: 16px 32px;
        border-radius: 12px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    @media (max-width: 768px) {
        .btn-primary {
            padding: 14px 28px;
            font-size: 16px;
            border-radius: 10px;
            gap: 10px;
        }
    }

    @media (max-width: 480px) {
        .btn-primary {
            padding: 12px 24px;
            font-size: 14px;
            border-radius: 8px;
            gap: 8px;
            width: 100%;
            justify-content: center;
        }
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6);
    }

    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Date Range Selector Styles */
    .date-range-container {
        background: rgba(248, 250, 252, 0.5);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
    }

    .dark .date-range-container {
        background: rgba(30, 41, 59, 0.5);
        border: 1px solid #334155;
    }

    .date-range-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 8px;
        color: #475569;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
    }

    .dark .date-range-btn {
        background: #334155;
        border: 1px solid #475569;
        color: #cbd5e1;
    }

    .date-range-btn:hover {
        background: #e2e8f0;
        border-color: #94a3b8;
        color: #1e293b;
        transform: translateY(-1px);
    }

    .dark .date-range-btn:hover {
        background: #475569;
        border-color: #64748b;
        color: #f1f5f9;
    }

    .date-range-btn.active {
        background: #3b82f6;
        border-color: #2563eb;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .date-range-btn.active:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    .custom-date-input {
        padding: 8px 12px;
        background: #ffffff;
        border: 1px solid #cbd5e1;
        border-radius: 6px;
        color: #1e293b;
        font-size: 14px;
        min-width: 140px;
    }

    .dark .custom-date-input {
        background: #1e293b;
        border: 1px solid #475569;
        color: #f1f5f9;
    }

    .custom-date-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #059669;
        border: none;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-secondary:hover {
        background: #047857;
        transform: translateY(-1px);
    }

    /* Mobile Responsive Date Range */
    @media (max-width: 768px) {
        .date-range-container {
            padding: 16px;
        }

        .date-range-btn {
            padding: 8px 12px;
            font-size: 13px;
        }

        .custom-date-input {
            min-width: 120px;
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .date-range-btn {
            padding: 6px 10px;
            font-size: 12px;
        }

        .custom-date-input {
            min-width: 100px;
            font-size: 12px;
        }
    }

    .header-section {
        display: flex;
        flex-direction: column;
        gap: 24px;
        margin-bottom: 32px;
    }

    @media (min-width: 768px) {
        .header-section {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .header-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
    }

    .ai-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #3b82f6;
    }

    .dark .ai-badge {
        background: rgba(59, 130, 246, 0.2);
        border: 1px solid rgba(59, 130, 246, 0.3);
        color: #93c5fd;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-4px); }
    }

    .error-container {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        border-radius: 12px;
        padding: 20px;
        margin-top: 24px;
    }

    .error-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
    }

    .error-title {
        color: #fca5a5;
        font-weight: 600;
    }

    .error-message {
        color: #fecaca;
        line-height: 1.5;
    }

    .info-section {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
    }

    .dark .info-section {
        background: #1e293b;
        border: 1px solid #334155;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .info-grid {
        display: grid;
        gap: 12px;
        font-size: 14px;
    }

    .info-item {
        display: flex;
        gap: 8px;
    }

    .info-label {
        font-weight: 600;
        color: #1e293b;
        min-width: 120px;
    }

    .dark .info-label {
        color: white;
    }

    .info-value {
        color: #64748b;
    }

    .dark .info-value {
        color: #94a3b8;
    }

    /* Structured Analysis Styles */
    .structured-analysis {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .analysis-section {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05);
    }

    .dark .analysis-section {
        background: #1e293b;
        border: 1px solid #334155;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .analysis-section {
            padding: 20px;
            border-radius: 10px;
        }
    }

    @media (max-width: 480px) {
        .analysis-section {
            padding: 16px;
            border-radius: 8px;
        }
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }

    .section-icon.performance {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .section-icon.strengths {
        background: rgba(34, 197, 94, 0.2);
        color: #4ade80;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .section-icon.weaknesses {
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .section-icon.actions {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .section-icon.psychology {
        background: rgba(147, 51, 234, 0.2);
        color: #a78bfa;
        border: 1px solid rgba(147, 51, 234, 0.3);
    }

    .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
    }

    .dark .section-title {
        color: white;
    }

    .section-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .performance-text {
        color: #475569;
        line-height: 1.6;
        margin-bottom: 20px;
    }

    .dark .performance-text {
        color: #cbd5e1;
    }

    .confidence-bar {
        background: rgba(248, 250, 252, 0.5);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 16px;
    }

    .dark .confidence-bar {
        background: rgba(51, 65, 85, 0.5);
        border: 1px solid #475569;
    }

    .confidence-label {
        color: #64748b;
        font-size: 14px;
        margin-bottom: 12px;
        display: block;
    }

    .dark .confidence-label {
        color: #94a3b8;
    }

    .progress-container {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .progress-bar-custom {
        flex: 1;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
    }

    .dark .progress-bar-custom {
        background: #334155;
    }

    .progress-fill-custom {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        border-radius: 4px;
        transition: width 0.5s ease;
    }

    .confidence-value, .score-value {
        color: #64748b;
        font-size: 14px;
        font-weight: 500;
    }

    .dark .confidence-value, .dark .score-value {
        color: #94a3b8;
    }

    .strength-item, .weakness-item, .action-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid transparent;
    }

    .strength-item {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.3);
    }

    .weakness-item {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
    }

    .action-item {
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.3);
    }

    .item-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: 4px;
    }

    .strength-item .item-icon {
        background: rgba(34, 197, 94, 0.2);
        color: #4ade80;
    }

    .weakness-item .item-icon {
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
    }

    .action-item .item-icon {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
    }

    .item-text {
        color: #475569;
        line-height: 1.6;
        margin: 0;
        flex: 1;
    }

    .dark .item-text {
        color: #cbd5e1;
    }

    .action-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .priority-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        align-self: flex-start;
    }

    .priority-badge.high {
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border: 1px solid rgba(239, 68, 68, 0.5);
    }

    .priority-badge.critical {
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border: 1px solid rgba(239, 68, 68, 0.5);
    }

    .psychology-content {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: rgba(147, 51, 234, 0.1);
        border: 1px solid rgba(147, 51, 234, 0.3);
        border-radius: 12px;
    }

    .psychology-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(147, 51, 234, 0.2);
        color: #a78bfa;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: 4px;
    }

    .psychology-text {
        flex: 1;
    }

    .psychology-text p {
        color: #475569;
        line-height: 1.6;
        margin-bottom: 16px;
    }

    .dark .psychology-text p {
        color: #cbd5e1;
    }

    .self-awareness-section {
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 8px;
        padding: 16px;
    }

    .dark .self-awareness-section {
        background: #334155;
        border: 1px solid #475569;
    }

    .self-awareness-section h5 {
        color: #1e293b;
        font-weight: 600;
        margin-bottom: 12px;
        font-size: 16px;
    }

    .dark .self-awareness-section h5 {
        color: white;
    }

    .score-note {
        color: #64748b;
        font-size: 12px;
        margin-top: 8px;
        margin-bottom: 0;
    }

    /* Fallback and no-data styles */
    .raw-analysis {
        margin-top: 20px;
    }

    .raw-text {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        color: #475569;
        line-height: 1.6;
        font-family: 'Inter', sans-serif;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .dark .raw-text {
        background: #334155;
        border: 1px solid #475569;
        color: #cbd5e1;
    }

    .no-data {
        color: #64748b;
        font-style: italic;
        text-align: center;
        padding: 20px;
        background: #f8fafc;
        border: 1px dashed #cbd5e1;
        border-radius: 8px;
    }

    .dark .no-data {
        color: #94a3b8;
        background: #1e293b;
        border: 1px dashed #475569;
    }

    /* Mobile responsive adjustments for step icons */
    @media (max-width: 768px) {
        .step-circle i {
            font-size: 14px !important;
        }
    }

    @media (max-width: 480px) {
        .step-circle i {
            font-size: 12px !important;
        }
    }

    /* Mobile responsive header */
    @media (max-width: 768px) {
        .header-section {
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }

        .header-content h1 {
            font-size: 24px !important;
        }

        .header-content p {
            font-size: 16px !important;
        }
    }

    @media (max-width: 480px) {
        .header-content h1 {
            font-size: 20px !important;
        }

        .header-content p {
            font-size: 14px !important;
        }
    }
</style>

<div class="ai-test-container">
    <div class="max-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <div class="header-icon">
                    <i class="fas fa-brain text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-1">AI Trading Analysis</h1>
                    <p class="text-gray-600 dark:text-slate-400 text-lg">Comprehensive performance insights powered by DeepSeek AI</p>
                </div>
            </div>
            <div class="ai-badge">
                <i class="fas fa-sparkles"></i>
                <span>AI-Powered Analysis</span>
            </div>
        </div>

        <!-- Main Analysis Card -->
        <div class="test-card">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 mb-8">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Generate AI Summary</h2>
                    <p class="text-gray-600 dark:text-slate-400 text-base" id="analysis-description">Analyze your last 30 days of trading performance with advanced AI insights</p>
                </div>
                <button id="analyze-btn" class="btn-primary">
                    <i class="fas fa-robot"></i>
                    Analyze My Trading
                </button>
            </div>

            <!-- Date Range Selector -->
            <div class="date-range-container mb-6">
                <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-calendar-alt text-blue-400"></i>
                        <span class="text-gray-900 dark:text-white font-medium">Analysis Period:</span>
                    </div>

                    <div class="flex flex-wrap gap-2">
                        <button class="date-range-btn active" data-range="30" data-label="Last 30 Days">
                            <i class="fas fa-calendar-day"></i>
                            Last 30 Days
                        </button>
                        <button class="date-range-btn" data-range="60" data-label="Last 60 Days">
                            <i class="fas fa-calendar-week"></i>
                            Last 60 Days
                        </button>
                        <button class="date-range-btn" data-range="90" data-label="Last 90 Days">
                            <i class="fas fa-calendar"></i>
                            Last 90 Days
                        </button>
                        <button class="date-range-btn" data-range="custom" data-label="Custom Range">
                            <i class="fas fa-calendar-alt"></i>
                            Custom Range
                        </button>
                    </div>
                </div>

                <!-- Custom Date Range Inputs -->
                <div id="custom-date-inputs" class="hidden mt-4 p-4 bg-gray-100 dark:bg-slate-800/50 rounded-lg border border-gray-300 dark:border-slate-600">
                    <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                        <div class="flex items-center gap-2">
                            <label class="text-gray-700 dark:text-slate-300 text-sm font-medium">From:</label>
                            <input type="date" id="start-date" class="custom-date-input">
                        </div>
                        <div class="flex items-center gap-2">
                            <label class="text-gray-700 dark:text-slate-300 text-sm font-medium">To:</label>
                            <input type="date" id="end-date" class="custom-date-input">
                        </div>
                        <button id="apply-custom-range" class="btn-secondary">
                            <i class="fas fa-check"></i>
                            Apply Range
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reports Summary -->
            <div id="reports-summary" class="hidden">
                <h3 class="text-xl font-semibold mb-6 text-white flex items-center gap-3">
                    <div class="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center">
                        <i class="fas fa-chart-bar text-blue-400"></i>
                    </div>
                    Your Trading Summary (<span id="period-display">Last 30 Days</span>)
                </h3>
                <div class="summary-grid" id="summary-grid">
                    <!-- Summary items will be populated here -->
                </div>
            </div>

            <!-- Loading State -->
            <div id="loading-state" class="hidden">
                <div class="loading-container">
                    <!-- AI Icon -->
                    <div class="ai-icon-container">
                        <div class="ai-icon-inner">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>

                    <!-- Title and Subtitle -->
                    <h2 class="loading-title" id="loading-title">Evaluating Risk Parameters</h2>
                    <p class="loading-subtitle" id="loading-subtitle">Calculating drawdowns and risk exposure</p>

                    <!-- Progress Bar -->
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>

                    <!-- Step Indicators -->
                    <div class="step-indicators">
                        <div class="step-indicator">
                            <div class="step-circle" id="step-1">
                                <i class="fas fa-database" style="font-size: 16px;"></i>
                            </div>
                            <span class="step-label">Data Collection</span>
                        </div>
                        <div class="step-indicator">
                            <div class="step-circle" id="step-2">
                                <i class="fas fa-search" style="font-size: 16px;"></i>
                            </div>
                            <span class="step-label">Pattern Recognition</span>
                        </div>
                        <div class="step-indicator">
                            <div class="step-circle" id="step-3">
                                <i class="fas fa-lightbulb" style="font-size: 16px;"></i>
                            </div>
                            <span class="step-label">Insight Generation</span>
                        </div>
                        <div class="step-indicator">
                            <div class="step-circle" id="step-4">
                                <i class="fas fa-shield-alt" style="font-size: 16px;"></i>
                            </div>
                            <span class="step-label">Risk Assessment</span>
                        </div>
                        <div class="step-indicator">
                            <div class="step-circle" id="step-5">
                                <i class="fas fa-chart-line" style="font-size: 16px;"></i>
                            </div>
                            <span class="step-label">Report Compilation</span>
                        </div>
                    </div>

                    <!-- Did You Know Section -->
                    <div class="did-you-know">
                        <div class="did-you-know-title">
                            <i class="fas fa-info-circle"></i>
                            Did You Know?
                        </div>
                        <div class="did-you-know-text" id="did-you-know-text">
                            Our AI analyzes over 120 different metrics from your trading history to identify patterns and opportunities for improvement.
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Result -->
            <div id="analysis-container" class="hidden">
                <h3 class="text-xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center gap-3">
                    <div class="w-8 h-8 rounded-lg bg-purple-600/20 flex items-center justify-center">
                        <i class="fas fa-brain text-purple-400"></i>
                    </div>
                    AI Analysis Results
                </h3>
                <div id="analysis-result" class="analysis-result">
                    <!-- AI analysis will be displayed here -->
                </div>

                <!-- API Usage Info -->
                <div id="api-usage" class="mt-6 p-4 bg-blue-50 dark:bg-blue-600/10 border border-blue-200 dark:border-blue-500/30 rounded-lg">
                    <div class="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                        <i class="fas fa-info-circle"></i>
                        <strong>API Usage:</strong> <span id="usage-info"></span>
                    </div>
                </div>
            </div>

            <!-- Error State -->
            <div id="error-container" class="hidden">
                <div class="error-container">
                    <div class="error-header">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                        <h4 class="error-title">Analysis Failed</h4>
                    </div>
                    <p id="error-message" class="error-message"></p>
                </div>
            </div>
        </div>

        <!-- Information Section -->
        <div class="info-section">
            <h3 class="text-xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center gap-3">
                <div class="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                Analysis Information
            </h3>
            <div class="info-grid">

                <div class="info-item">
                    <span class="info-label">Data Source:</span>
                    <span class="info-value">All trades from the past 30 days</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Analysis Focus:</span>
                    <span class="info-value">Performance metrics, strategy effectiveness, emotional patterns</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Output Format:</span>
                    <span class="info-value">Structured insights with specific actionable recommendations</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Response Time:</span>
                    <span class="info-value">Typically 10-20 seconds (optimized for faster analysis)</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const analyzeBtn = document.getElementById('analyze-btn');
    const loadingState = document.getElementById('loading-state');
    const analysisContainer = document.getElementById('analysis-container');
    const errorContainer = document.getElementById('error-container');
    const reportsummary = document.getElementById('reports-summary');

    // Date range elements
    const dateRangeBtns = document.querySelectorAll('.date-range-btn');
    const customDateInputs = document.getElementById('custom-date-inputs');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const applyCustomRangeBtn = document.getElementById('apply-custom-range');
    const analysisDescription = document.getElementById('analysis-description');
    const periodDisplay = document.getElementById('period-display');

    // Current date range state
    let currentDateRange = {
        type: '30', // '30', '60', '90', 'custom'
        label: 'Last 30 Days',
        startDate: null,
        endDate: null
    };

    // Initialize date inputs with default values
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    endDateInput.value = today.toISOString().split('T')[0];
    startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];

    // Date range button handlers
    dateRangeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const range = this.dataset.range;
            const label = this.dataset.label;

            // Update active state
            dateRangeBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Handle custom range
            if (range === 'custom') {
                customDateInputs.classList.remove('hidden');
                currentDateRange.type = 'custom';
                currentDateRange.label = 'Custom Range';
            } else {
                customDateInputs.classList.add('hidden');
                currentDateRange.type = range;
                currentDateRange.label = label;
                currentDateRange.startDate = null;
                currentDateRange.endDate = null;

                // Update description
                analysisDescription.textContent = `Analyze your ${label.toLowerCase()} of trading performance with advanced AI insights`;
            }
        });
    });

    // Apply custom range handler
    applyCustomRangeBtn.addEventListener('click', function() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;

        if (!startDate || !endDate) {
            alert('Please select both start and end dates');
            return;
        }

        if (new Date(startDate) >= new Date(endDate)) {
            alert('Start date must be before end date');
            return;
        }

        currentDateRange.startDate = startDate;
        currentDateRange.endDate = endDate;

        // Calculate days difference
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        currentDateRange.label = `${diffDays} Days (${formatDate(startDate)} - ${formatDate(endDate)})`;

        // Update description
        analysisDescription.textContent = `Analyze your custom date range of trading performance with advanced AI insights`;

        // Hide custom inputs
        customDateInputs.classList.add('hidden');
    });

    // Helper function to format date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    // Loading animation variables
    let loadingInterval;
    let currentStep = 0;
    const loadingSteps = [
        {
            title: "Collecting Trading Data",
            subtitle: "Gathering performance metrics, trades, and historical patterns",
            progress: 20,
            stepIndex: 1,
            didYouKnow: "Our AI analyzes over 120 different metrics from your trading history to identify patterns and opportunities for improvement."
        },
        {
            title: "Recognizing Patterns",
            subtitle: "Identifying trading behaviors and market correlations",
            progress: 40,
            stepIndex: 2,
            didYouKnow: "Pattern recognition helps identify your most profitable trading setups and common mistake patterns."
        },
        {
            title: "Generating Insights",
            subtitle: "Creating personalized analysis and recommendations",
            progress: 60,
            stepIndex: 3,
            didYouKnow: "Advanced algorithms compare your performance against optimal trading strategies to find improvement areas."
        },
        {
            title: "Evaluating Risk Parameters",
            subtitle: "Calculating drawdowns and risk exposure",
            progress: 80,
            stepIndex: 4,
            didYouKnow: "Risk assessment includes drawdown analysis, position sizing evaluation, and emotional trading detection."
        },
        {
            title: "Compiling Final Report",
            subtitle: "Finalizing comprehensive trading analysis",
            progress: 100,
            stepIndex: 5,
            didYouKnow: "Your personalized report includes actionable insights to improve your trading performance by up to 30%."
        }
    ];

    analyzeBtn.addEventListener('click', function() {
        // Reset states
        hideAllStates();
        loadingState.classList.remove('hidden');
        analyzeBtn.disabled = true;
        analyzeBtn.innerHTML = '<i class="fas fa-cog fa-spin mr-2"></i>Analyzing...';

        // Start loading animation
        startLoadingAnimation();

        // Prepare date range data
        let dateRangeData = {
            type: currentDateRange.type,
            label: currentDateRange.label
        };

        if (currentDateRange.type === 'custom') {
            dateRangeData.startDate = currentDateRange.startDate;
            dateRangeData.endDate = currentDateRange.endDate;
        }

        // Make API call
        fetch(base_url + 'ai-test/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                dateRange: dateRangeData
            })
        })
        .then(response => response.json())
        .then(data => {
            stopLoadingAnimation();
            completeLoadingAnimation();

            // Show completion animation for 1.5 seconds before showing results
            setTimeout(() => {
                hideAllStates();

                if (data.success) {
                    // Show reports summary
                    displayReportsSummary(data.reports_summary);
                    reportsummary.classList.remove('hidden');

                    // Show AI analysis
                    displayAnalysis(data.data);
                    analysisContainer.classList.remove('hidden');

                    // Smooth scroll to results
                    setTimeout(() => {
                        analysisContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 100);
                } else {
                    showError(data.message || 'Unknown error occurred');
                }
            }, 1500); // 1.5 second delay to show completion animation
        })
        .catch(error => {
            stopLoadingAnimation();
            completeLoadingAnimation();

            setTimeout(() => {
                hideAllStates();
                showError('Network error: ' + error.message);
            }, 1000);
        })
        .finally(() => {
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<i class="fas fa-robot"></i>Analyze My Trading';
        });
    });

    function startLoadingAnimation() {
        currentStep = 0;
        resetLoadingSteps();
        updateLoadingStep();

        loadingInterval = setInterval(() => {
            if (currentStep < loadingSteps.length - 1) {
                currentStep++;
                updateLoadingStep();
            }
        }, 2500); // Increased delay for better UX
    }

    function stopLoadingAnimation() {
        if (loadingInterval) {
            clearInterval(loadingInterval);
            loadingInterval = null;
        }
    }

    function resetLoadingSteps() {
        // Reset all step indicators
        for (let i = 1; i <= 5; i++) {
            const stepCircle = document.getElementById(`step-${i}`);
            const stepLabel = stepCircle.nextElementSibling;

            stepCircle.classList.remove('active', 'completed');
            stepLabel.classList.remove('active', 'completed');
        }

        // Reset progress bar
        document.getElementById('progress-fill').style.width = '0%';
    }

    function updateLoadingStep() {
        const step = loadingSteps[currentStep];

        // Update title and subtitle with animation
        const titleElement = document.getElementById('loading-title');
        const subtitleElement = document.getElementById('loading-subtitle');
        const didYouKnowElement = document.getElementById('did-you-know-text');

        titleElement.style.opacity = '0';
        subtitleElement.style.opacity = '0';

        setTimeout(() => {
            titleElement.textContent = step.title;
            subtitleElement.textContent = step.subtitle;
            didYouKnowElement.textContent = step.didYouKnow;

            titleElement.style.opacity = '1';
            subtitleElement.style.opacity = '1';
        }, 200);

        // Update progress bar with smooth animation
        setTimeout(() => {
            document.getElementById('progress-fill').style.width = step.progress + '%';
        }, 300);

        // Update step indicators
        for (let i = 1; i <= 5; i++) {
            const stepCircle = document.getElementById(`step-${i}`);
            const stepLabel = stepCircle.nextElementSibling;

            if (i < step.stepIndex) {
                // Completed steps
                stepCircle.classList.remove('active');
                stepCircle.classList.add('completed');
                stepLabel.classList.remove('active');
                stepLabel.classList.add('completed');
            } else if (i === step.stepIndex) {
                // Current active step
                stepCircle.classList.add('active');
                stepCircle.classList.remove('completed');
                stepLabel.classList.add('active');
                stepLabel.classList.remove('completed');
            } else {
                // Future steps
                stepCircle.classList.remove('active', 'completed');
                stepLabel.classList.remove('active', 'completed');
            }
        }
    }

    function completeLoadingAnimation() {
        // Mark all steps as completed
        for (let i = 1; i <= 5; i++) {
            const stepCircle = document.getElementById(`step-${i}`);
            const stepLabel = stepCircle.nextElementSibling;

            stepCircle.classList.remove('active');
            stepCircle.classList.add('completed');
            stepLabel.classList.remove('active');
            stepLabel.classList.add('completed');
        }

        // Complete progress bar
        document.getElementById('progress-fill').style.width = '100%';

        // Update final message
        setTimeout(() => {
            document.getElementById('loading-title').textContent = 'Analysis Complete!';
            document.getElementById('loading-subtitle').textContent = 'Your comprehensive trading report is ready';
        }, 500);
    }

    function hideAllStates() {
        loadingState.classList.add('hidden');
        analysisContainer.classList.add('hidden');
        errorContainer.classList.add('hidden');
        reportsummary.classList.add('hidden');

        // Reset loading animation
        stopLoadingAnimation();
        resetLoadingState();
    }

    function resetLoadingState() {
        // Reset step indicators
        for (let i = 1; i <= 3; i++) {
            const stepCircle = document.getElementById(`step-${i}`);
            const stepLabel = stepCircle.nextElementSibling;

            stepCircle.classList.remove('active');
            stepLabel.classList.remove('active');
        }

        // Reset progress
        document.getElementById('progress-fill').style.width = '0%';

        // Reset to first step
        document.getElementById('loading-title').textContent = loadingSteps[0].title;
        document.getElementById('loading-subtitle').textContent = loadingSteps[0].subtitle;
    }

    function displayReportsSummary(summary) {
        const summaryGrid = document.getElementById('summary-grid');

        // Update period display
        periodDisplay.textContent = currentDateRange.label;

        const items = [
            { label: 'Total Trades', value: summary.total_trades, class: 'neutral' },
            { label: 'Win Rate', value: summary.win_rate + '%', class: summary.win_rate >= 50 ? 'positive' : 'negative' },
            { label: 'Total P&L', value: '&#8377;' + summary.total_pnl.toLocaleString('en-IN'), class: summary.total_pnl >= 0 ? 'positive' : 'negative' },
            { label: 'Expectancy', value: '&#8377;' + summary.expectancy.toLocaleString('en-IN'), class: summary.expectancy >= 0 ? 'positive' : 'negative' },
            { label: 'Avg Win', value: '&#8377;' + summary.avg_win.toLocaleString('en-IN'), class: 'positive' },
            { label: 'Avg Loss', value: '&#8377;' + Math.abs(summary.avg_loss).toLocaleString('en-IN'), class: 'negative' },
            { label: 'Confidence', value: summary.avg_confidence + '/10', class: summary.avg_confidence >= 7 ? 'positive' : summary.avg_confidence >= 5 ? 'neutral' : 'negative' },
            { label: 'Avg R:R', value: summary.avg_rr_ratio.toFixed(2), class: summary.avg_rr_ratio >= 2 ? 'positive' : summary.avg_rr_ratio >= 1 ? 'neutral' : 'negative' }
        ];

        summaryGrid.innerHTML = items.map(item => `
            <div class="summary-item">
                <div class="summary-value ${item.class}">${item.value}</div>
                <div class="summary-label">${item.label}</div>
            </div>
        `).join('');
    }

    function displayAnalysis(data) {
        const analysisText = data.analysis;
        const analysisContainer = document.getElementById('analysis-result');

        // Log the full analysis text for debugging
        console.log('Full AI Analysis Text:', analysisText);
        console.log('Analysis Text Length:', analysisText.length);

        // Check if response was truncated
        if (data.finish_reason === 'length') {
            console.warn('AI response was truncated due to token limit');
        }

        // Parse the structured analysis
        const structuredAnalysis = parseAnalysisText(analysisText);

        // Log parsed sections for debugging
        console.log('Parsed Analysis Sections:', structuredAnalysis);

        // Check if parsing was successful
        const hasContent = structuredAnalysis.performance ||
                          structuredAnalysis.strengths.length > 0 ||
                          structuredAnalysis.weaknesses.length > 0 ||
                          structuredAnalysis.actions.length > 0 ||
                          structuredAnalysis.psychology;

        if (hasContent) {
            // Create structured HTML
            analysisContainer.innerHTML = createStructuredAnalysisHTML(structuredAnalysis);
        } else {
            // Fallback: display raw analysis text
            console.warn('Structured parsing failed, displaying raw text');
            analysisContainer.innerHTML = `
                <div class="raw-analysis">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3 class="section-title">AI Analysis</h3>
                        </div>
                        <div class="section-content">
                            <div class="raw-text">${escapeHtml(analysisText).replace(/\n/g, '<br>')}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        if (data.usage) {
            document.getElementById('usage-info').textContent =
                `Tokens: ${data.usage.total_tokens || 'N/A'} | Model: ${data.model}${data.finish_reason === 'length' ? ' (Truncated)' : ''}`;
        }
    }

    function parseAnalysisText(text) {
        const sections = {
            performance: '',
            strengths: [],
            weaknesses: [],
            actions: [],
            psychology: ''
        };

        // If text is empty or too short, return empty sections
        if (!text || text.trim().length < 10) {
            console.warn('Analysis text is empty or too short:', text);
            return sections;
        }

        // Clean the text - remove extra whitespace, normalize line breaks, and remove formatting symbols
        let cleanText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n').trim();

        // Remove any remaining asterisks and hash symbols that might have escaped backend cleaning
        cleanText = cleanText.replace(/\*{1,}/g, '');
        cleanText = cleanText.replace(/#{1,}/g, '');

        // Clean up excessive whitespace
        cleanText = cleanText.replace(/\s+/g, ' ');
        cleanText = cleanText.replace(/\n{3,}/g, '\n\n');

        // Split by numbered sections with more flexible regex
        const performanceMatch = cleanText.match(/1\.\s*PERFORMANCE[:\s]*(.*?)(?=2\.\s*STRENGTHS|$)/is);
        const strengthsMatch = cleanText.match(/2\.\s*STRENGTHS[:\s]*(.*?)(?=3\.\s*WEAKNESSES|$)/is);
        const weaknessesMatch = cleanText.match(/3\.\s*WEAKNESSES[:\s]*(.*?)(?=4\.\s*ACTIONS|$)/is);
        const actionsMatch = cleanText.match(/4\.\s*ACTIONS[:\s]*(.*?)(?=5\.\s*PSYCHOLOGY|$)/is);
        const psychologyMatch = cleanText.match(/5\.\s*PSYCHOLOGY[:\s]*(.*?)$/is);

        if (performanceMatch) {
            sections.performance = performanceMatch[1].trim();
            console.log('Parsed Performance:', sections.performance);
        } else {
            console.warn('Could not parse PERFORMANCE section from:', cleanText.substring(0, 200));
        }

        if (strengthsMatch) {
            sections.strengths = parseListItems(strengthsMatch[1]);
            console.log('Parsed Strengths:', sections.strengths);
        } else {
            console.warn('Could not parse STRENGTHS section');
        }

        if (weaknessesMatch) {
            sections.weaknesses = parseListItems(weaknessesMatch[1]);
            console.log('Parsed Weaknesses:', sections.weaknesses);
        } else {
            console.warn('Could not parse WEAKNESSES section');
        }

        if (actionsMatch) {
            sections.actions = parseListItems(actionsMatch[1]);
            console.log('Parsed Actions:', sections.actions);
        } else {
            console.warn('Could not parse ACTIONS section');
        }

        if (psychologyMatch) {
            sections.psychology = psychologyMatch[1].trim();
            console.log('Parsed Psychology:', sections.psychology);
        } else {
            console.warn('Could not parse PSYCHOLOGY section');
        }

        return sections;
    }

    function parseListItems(text) {
        const items = [];

        if (!text || text.trim().length === 0) {
            return items;
        }

        // Clean the text first
        text = cleanText(text);

        const lines = text.split('\n').filter(line => line.trim());

        for (const line of lines) {
            const trimmed = line.trim();

            // Check for bullet points, dashes, numbered items, or other separators
            if (trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*') ||
                trimmed.startsWith('?') || trimmed.match(/^\d+[\.\)]/)) {
                // Remove the bullet/number and add as new item
                let cleanItem = trimmed.replace(/^[•\-\*\?\d\.\)\s]+/, '').trim();
                // Aggressively remove question marks from anywhere in the beginning
                cleanItem = cleanItem.replace(/^\?\s*/g, '').trim();
                cleanItem = cleanItem.replace(/^\s*\?\s*/g, '').trim();
                // Remove any remaining asterisks or hash symbols
                cleanItem = cleanItem.replace(/[\*#]+/g, '').trim();
                if (cleanItem) {
                    items.push(cleanItem);
                }
            } else if (trimmed && items.length === 0) {
                // If no items yet and we have text, treat as first item
                items.push(trimmed);
            } else if (trimmed && items.length > 0) {
                // Continuation of previous item
                items[items.length - 1] += ' ' + trimmed;
            }
        }

        // Always try intelligent splitting for better readability, even if we found some items
        if (text.trim()) {
            // If we only have one long item, try to split it further
            if (items.length <= 1) {
                const splitItems = intelligentTextSplit(text.trim());
                if (splitItems.length > 1) {
                    items.length = 0; // Clear existing items
                    items.push(...splitItems);
                }
            }

            // If still no items, add the original text
            if (items.length === 0) {
                items.push(text.trim());
            }
        }

        // Final cleanup - remove any remaining question marks from all items
        return items
            .filter(item => item && item.trim().length > 0)
            .map(item => {
                // Final aggressive question mark removal
                item = item.replace(/^\?\s*/g, '').trim();
                item = item.replace(/^\s*\?\s*/g, '').trim();
                return cleanText(item);
            })
            .filter(item => item && item.trim().length > 0);
    }

    function intelligentTextSplit(text) {
        const items = [];
        let workingText = cleanText(text.trim());

        // Remove any remaining formatting symbols
        workingText = workingText.replace(/[\*#]+/g, '');

        // Strategy 1: Split by bullet points embedded in text (most common case)
        const bulletSplits = workingText.split(/\s*•\s*/);
        if (bulletSplits.length > 1) {
            return bulletSplits
                .map(item => item.trim())
                .filter(item => item.length > 5)
                .map(item => {
                    // Clean up each item thoroughly
                    item = cleanText(item);
                    item = item.replace(/^[•\?\-\*\s]+/, '').trim();
                    // Extra aggressive question mark removal
                    item = item.replace(/^\?\s*/g, '').trim();
                    item = item.replace(/^\s*\?\s*/g, '').trim();
                    // Ensure proper sentence ending
                    if (item && !item.match(/[.!?]$/)) {
                        item += '.';
                    }
                    return item;
                })
                .filter(item => item.length > 0);
        }

        // Strategy 2: Split by periods followed by capital letters (sentence boundaries)
        const sentenceSplits = workingText.split(/\.\s+(?=[A-Z])/);
        if (sentenceSplits.length > 1) {
            return sentenceSplits
                .map((item, index) => {
                    item = item.trim();
                    // Add period back except for the last item if it already has punctuation
                    if (index < sentenceSplits.length - 1 && !item.match(/[.!?]$/)) {
                        item += '.';
                    }
                    return item;
                })
                .filter(item => item.length > 10);
        }

        // Strategy 3: Split by specific trading terms that indicate new points
        const tradingTerms = [
            /\.\s*(?=Rule\s+adherence)/gi,
            /\.\s*(?=Strategy\s+)/gi,
            /\.\s*(?=Mistake\s+)/gi,
            /\.\s*(?=Symbol\s+)/gi,
            /\.\s*(?=Emotional\s+)/gi,
            /\.\s*(?=Risk\s+)/gi,
            /\.\s*(?=Capital\s+)/gi,
            /\.\s*(?=Performance\s+)/gi
        ];

        for (const pattern of tradingTerms) {
            const splits = workingText.split(pattern);
            if (splits.length > 1) {
                return splits
                    .map((item, index) => {
                        item = item.trim();
                        if (index < splits.length - 1 && !item.match(/[.!?]$/)) {
                            item += '.';
                        }
                        return item;
                    })
                    .filter(item => item.length > 10);
            }
        }

        // Strategy 4: If text is very long, force split at reasonable points
        if (workingText.length > 200) {
            const forceSplits = [];
            const sentences = workingText.split(/\.\s+/);
            let currentChunk = '';

            for (let i = 0; i < sentences.length; i++) {
                const sentence = sentences[i].trim();
                if (currentChunk.length + sentence.length > 150 && currentChunk.length > 0) {
                    // Add period if missing
                    if (!currentChunk.match(/[.!?]$/)) {
                        currentChunk += '.';
                    }
                    forceSplits.push(currentChunk.trim());
                    currentChunk = sentence;
                } else {
                    currentChunk += (currentChunk ? '. ' : '') + sentence;
                }
            }

            // Add the last chunk
            if (currentChunk.trim()) {
                if (!currentChunk.match(/[.!?]$/)) {
                    currentChunk += '.';
                }
                forceSplits.push(currentChunk.trim());
            }

            if (forceSplits.length > 1) {
                return forceSplits.filter(item => item.length > 0);
            }
        }

        // Fallback: return as single item
        return [workingText];
    }

    // Helper function to clean and format text
    function cleanText(text) {
        if (!text) return '';

        // Convert HTML entities to proper symbols
        text = text.replace(/&#8377;/g, '₹');  // Convert rupee symbol
        text = text.replace(/&amp;/g, '&');   // Convert ampersand
        text = text.replace(/&lt;/g, '<');    // Convert less than
        text = text.replace(/&gt;/g, '>');    // Convert greater than
        text = text.replace(/&quot;/g, '"');  // Convert quotes

        // Remove unwanted question marks at the beginning (multiple patterns)
        text = text.replace(/^\?\s*/g, '').trim();           // Start of string
        text = text.replace(/\s\?\s*/g, ' ').trim();         // Middle of text
        text = text.replace(/^\s*\?\s*/g, '').trim();        // After whitespace

        // Clean up multiple spaces
        text = text.replace(/\s+/g, ' ').trim();

        return text;
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function createStructuredAnalysisHTML(sections) {
        return `
            <div class="structured-analysis">
                <!-- Performance Section -->
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon performance">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="section-title">1. PERFORMANCE</h3>
                    </div>
                    <div class="section-content">
                        <p class="performance-text">${escapeHtml(sections.performance || 'No performance analysis available')}</p>
                        <div class="confidence-bar">
                            <span class="confidence-label">Confidence Level:</span>
                            <div class="progress-container">
                                <div class="progress-bar-custom">
                                    <div class="progress-fill-custom" style="width: 45%"></div>
                                </div>
                                <span class="confidence-value">4.5/10</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Strengths Section -->
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon strengths">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3 class="section-title">2. STRENGTHS</h3>
                    </div>
                    <div class="section-content">
                        ${sections.strengths.length > 0 ? sections.strengths.map(strength => `
                            <div class="strength-item">
                                <div class="item-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="item-text">${escapeHtml(strength)}</p>
                            </div>
                        `).join('') : '<p class="no-data">No strengths identified</p>'}
                    </div>
                </div>

                <!-- Weaknesses Section -->
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon weaknesses">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="section-title">3. WEAKNESSES</h3>
                    </div>
                    <div class="section-content">
                        ${sections.weaknesses.length > 0 ? sections.weaknesses.map(weakness => `
                            <div class="weakness-item">
                                <div class="item-icon">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <p class="item-text">${escapeHtml(weakness)}</p>
                            </div>
                        `).join('') : '<p class="no-data">No weaknesses identified</p>'}
                    </div>
                </div>

                <!-- Actions Section -->
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon actions">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3 class="section-title">4. ACTIONS</h3>
                    </div>
                    <div class="section-content">
                        ${sections.actions.length > 0 ? sections.actions.map((action, index) => `
                            <div class="action-item">
                                <div class="item-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="action-content">
                                    <p class="item-text">${escapeHtml(action)}</p>
                                    ${index === 0 ? '<span class="priority-badge high">High Priority</span>' :
                                      index === 1 ? '<span class="priority-badge critical">Critical</span>' : ''}
                                </div>
                            </div>
                        `).join('') : '<p class="no-data">No actions recommended</p>'}
                    </div>
                </div>

                <!-- Psychology Section -->
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon psychology">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 class="section-title">5. PSYCHOLOGY</h3>
                    </div>
                    <div class="section-content">
                        <div class="psychology-content">
                            <div class="psychology-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="psychology-text">
                                <p>${escapeHtml(sections.psychology || 'No psychology analysis available')}</p>
                                <div class="self-awareness-section">
                                    <h5>Self-Awareness Score</h5>
                                    <div class="progress-container">
                                        <div class="progress-bar-custom">
                                            <div class="progress-fill-custom" style="width: 30%"></div>
                                        </div>
                                        <span class="score-value">3/10</span>
                                    </div>
                                    <p class="score-note">Need better emotional tracking</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function showError(message) {
        document.getElementById('error-message').textContent = message;
        errorContainer.classList.remove('hidden');
    }
});
</script>
