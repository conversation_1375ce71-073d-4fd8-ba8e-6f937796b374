<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAngelOneSupport extends Migration
{
    public function up()
    {
        // Add Angel One specific fields to users table
        $fields = [
            'angel_client_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'Angel One client ID'
            ],
            'angel_access_token' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Angel One access token (encrypted)'
            ],
            'angel_refresh_token' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Angel One refresh token (encrypted)'
            ],
            'angel_feed_token' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Angel One feed token (encrypted)'
            ],
            'angel_connected_on' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When Angel One was connected'
            ]
        ];

        $this->forge->addColumn('users', $fields);

        // Create angel_trades table for storing raw Angel One trade data
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'angel_client_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'order_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'exchange_order_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'exchange_trade_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
            ],
            'transaction_type' => [
                'type' => 'ENUM',
                'constraint' => ['BUY', 'SELL'],
            ],
            'exchange_segment' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'product_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'order_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'trading_symbol' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'symbol_token' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'quantity' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'price' => [
                'type' => 'DECIMAL',
                'constraint' => '15,4',
            ],
            'trade_value' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'trade_date' => [
                'type' => 'DATE',
            ],
            'trade_time' => [
                'type' => 'TIME',
            ],
            'fill_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'fill_time' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'raw_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Complete raw trade data from Angel One'
            ],
            'processed' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'comment' => 'Whether this trade has been processed into trades table'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('angel_client_id');
        $this->forge->addKey('exchange_trade_id');
        $this->forge->addKey('trading_symbol');
        $this->forge->addKey('trade_date');
        $this->forge->addKey('processed');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('angel_trades');

        // Add broker field to trades table if it doesn't exist
        if (!$this->db->fieldExists('broker', 'trades')) {
            $this->forge->addColumn('trades', [
                'broker' => [
                    'type' => 'VARCHAR',
                    'constraint' => 50,
                    'null' => true,
                    'comment' => 'Broker name (dhan, angel_one, etc.)'
                ],
                'broker_order_id' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                    'comment' => 'Original broker order ID'
                ]
            ]);
        }
    }

    public function down()
    {
        // Remove Angel One fields from users table
        $this->forge->dropColumn('users', [
            'angel_client_id',
            'angel_access_token', 
            'angel_refresh_token',
            'angel_feed_token',
            'angel_connected_on'
        ]);

        // Drop angel_trades table
        $this->forge->dropTable('angel_trades');

        // Remove broker fields from trades table
        $this->forge->dropColumn('trades', ['broker', 'broker_order_id']);
    }
}
