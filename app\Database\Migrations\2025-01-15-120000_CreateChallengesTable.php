<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateChallengesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'start_amount' => [
                'type'       => 'DECIMAL',
                'constraint' => '15,2',
                'comment'    => 'Starting capital amount',
            ],
            'target_amount' => [
                'type'       => 'DECIMAL',
                'constraint' => '15,2',
                'comment'    => 'Target capital amount',
            ],
            'current_amount' => [
                'type'       => 'DECIMAL',
                'constraint' => '15,2',
                'null'       => true,
                'comment'    => 'Current capital amount',
            ],
            'timeframe' => [
                'type'       => 'VARCHAR',
                'constraint' => 20,
                'comment'    => 'Challenge timeframe (days, weeks, months, custom)',
            ],
            'custom_days' => [
                'type'       => 'INT',
                'constraint' => 5,
                'null'       => true,
                'comment'    => 'Custom number of days for timeframe',
            ],
            'risk_per_trade' => [
                'type'       => 'DECIMAL',
                'constraint' => '5,2',
                'comment'    => 'Risk percentage per trade',
            ],
            'start_date' => [
                'type'    => 'DATE',
                'comment' => 'Challenge start date',
            ],
            'end_date' => [
                'type'    => 'DATE',
                'comment' => 'Challenge end date',
            ],
            'status' => [
                'type'       => 'ENUM',
                'constraint' => ['active', 'completed', 'failed', 'abandoned'],
                'default'    => 'active',
                'comment'    => 'Challenge status',
            ],
            'progress' => [
                'type'       => 'DECIMAL',
                'constraint' => '5,2',
                'default'    => 0,
                'comment'    => 'Progress percentage towards target',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('status');
        $this->forge->addKey('start_date');
        $this->forge->addKey('end_date');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('challenges');
    }

    public function down()
    {
        $this->forge->dropTable('challenges');
    }
}
