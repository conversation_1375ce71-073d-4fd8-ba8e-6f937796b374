<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capital Growth Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@500;600;700&family=Inter:wght@300;400;500&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        rajdhani: ['Raj<PERSON>ni', 'sans-serif'],
                        inter: ['Inter', 'sans-serif'],
                    },
                    colors: {
                        dark: {
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                        },
                        accent: {
                            blue: '#3b82f6',
                            green: '#22c55e',
                            red: '#ef4444',
                            yellow: '#facc15',
                            purple: '#a855f7',
                        }
                    },
                    boxShadow: {
                        'accent-blue': '0 0 10px #3b82f6, 0 0 20px #3b82f640',
                        'accent-green': '0 0 10px #22c55e, 0 0 20px #22c55e40',
                    },
                    backdropBlur: {
                        xs: '2px',
                        sm: '4px',
                        md: '8px',
                        lg: '12px',
                        xl: '16px',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        .float {
            animation: float 3s ease-in-out infinite;
        }
        .glass {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        .glow-hover:hover {
            box-shadow: 0 0 15px #3b82f6, 0 0 30px #3b82f640;
            transition: all 0.3s ease;
        }
        .progress-ring__circle {
            transition: stroke-dashoffset 0.5s;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: rgba(30, 41, 59, 0.9);
            color: #e2e8f0;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .heatmap-day {
            width: 20px;
            height: 20px;
            margin: 2px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        .heatmap-day:hover {
            transform: scale(1.2);
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        /* Modal styles */
        .modal {
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .modal-content {
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        .modal.active .modal-content {
            transform: translateY(0);
        }
        /* Confidence meter */
        .confidence-meter {
            height: 6px;
            background: linear-gradient(to right, #ef4444, #f59e0b, #22c55e);
            border-radius: 3px;
        }
        .confidence-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #1e293b;
            position: absolute;
            top: -3px;
            transform: translateX(-6px);
        }
        /* Hero section enhancements */
        .hero-gradient {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(34, 197, 94, 0.15) 100%);
        }
        .hero-pattern {
            background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
        }
    </style>
</head>
<body class="bg-dark-900 text-gray-200 font-inter min-h-screen">
    <!-- Header -->
    <header class="glass sticky top-0 z-50 border-b border-gray-800">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-chart-line text-accent-blue text-xl"></i>
                <h1 class="font-rajdhani text-xl font-bold">TradeTrack</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-dark-700 transition-colors">
                    <i class="fas fa-moon text-accent-yellow"></i>
                </button>
                <button id="set-challenge" class="px-4 py-2 bg-accent-blue/10 text-accent-blue rounded-lg text-sm hover:bg-accent-blue/20 flex items-center space-x-1">
                    <i class="fas fa-bullseye"></i>
                    <span>Set Challenge</span>
                </button>
                <button class="px-4 py-2 bg-accent-blue text-white rounded-lg text-sm hover:bg-accent-blue/90 flex items-center space-x-1">
                    <i class="fas fa-plus"></i>
                    <span>New Trade</span>
                </button>
                <div class="hidden md:flex items-center space-x-2">
                    <img src="https://via.placeholder.com/30" alt="User" class="rounded-full">
                    <span class="text-sm">John Trader</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Challenge Setting Modal -->
    <div id="challenge-modal" class="modal fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 opacity-0 invisible">
        <div class="modal-content glass rounded-xl p-6 w-full max-w-md mx-4 transform transition-all">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-rajdhani text-xl font-bold">Set Trading Challenge</h3>
                <button id="close-modal" class="p-2 rounded-full hover:bg-dark-700 transition-colors">
                    <i class="fas fa-times text-gray-400"></i>
                </button>
            </div>
            
            <form id="challenge-form" class="space-y-4">
                <div>
                    <label for="start-amount" class="block text-sm font-medium text-gray-400 mb-1">Starting Capital</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">₹</span>
                        </div>
                        <input type="number" id="start-amount" class="bg-dark-800 border border-gray-700 rounded-lg pl-8 pr-4 py-2 w-full focus:border-accent-blue focus:ring-1 focus:ring-accent-blue" placeholder="1,00,000" required>
                    </div>
                </div>
                
                <div>
                    <label for="target-amount" class="block text-sm font-medium text-gray-400 mb-1">Target Capital</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">₹</span>
                        </div>
                        <input type="number" id="target-amount" class="bg-dark-800 border border-gray-700 rounded-lg pl-8 pr-4 py-2 w-full focus:border-accent-blue focus:ring-1 focus:ring-accent-blue" placeholder="2,50,000" required>
                    </div>
                </div>
                
                <div>
                    <label for="timeframe" class="block text-sm font-medium text-gray-400 mb-1">Timeframe</label>
                    <select id="timeframe" class="bg-dark-800 border border-gray-700 rounded-lg px-4 py-2 w-full focus:border-accent-blue focus:ring-1 focus:ring-accent-blue">
                        <option value="7">1 Week</option>
                        <option value="30" selected>1 Month</option>
                        <option value="90">3 Months</option>
                        <option value="180">6 Months</option>
                        <option value="365">1 Year</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
                
                <div id="custom-days-container" class="hidden">
                    <label for="custom-days" class="block text-sm font-medium text-gray-400 mb-1">Custom Days</label>
                    <input type="number" id="custom-days" class="bg-dark-800 border border-gray-700 rounded-lg px-4 py-2 w-full focus:border-accent-blue focus:ring-1 focus:ring-accent-blue" placeholder="Enter number of days">
                </div>
                
                <div>
                    <label for="risk-per-trade" class="block text-sm font-medium text-gray-400 mb-1">Max Risk Per Trade (%)</label>
                    <input type="number" id="risk-per-trade" class="bg-dark-800 border border-gray-700 rounded-lg px-4 py-2 w-full focus:border-accent-blue focus:ring-1 focus:ring-accent-blue" placeholder="2" min="0.1" max="10" step="0.1">
                </div>
                
                <div class="flex space-x-3 pt-2">
                    <button type="submit" class="flex-1 px-4 py-2 bg-accent-blue text-white rounded-lg font-medium hover:bg-accent-blue/90 hover:shadow-accent-blue transition-all flex items-center justify-center space-x-2">
                        <i class="fas fa-check"></i>
                        <span>Set Challenge</span>
                    </button>
                    <button type="button" id="cancel-challenge" class="flex-1 px-4 py-2 bg-dark-700 text-gray-300 rounded-lg font-medium hover:bg-dark-600 transition-all">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Updated Hero Section - More Spacious on Mobile -->
    <section class="container mx-auto px-4 py-6 md:py-12">
        <div class="glass rounded-2xl p-6 md:p-8 relative overflow-hidden hero-gradient hero-pattern">
            <div class="absolute inset-0 opacity-20"></div>
            <div class="relative z-10">
                <!-- Stacked layout on mobile, side by side on desktop -->
                <div class="flex flex-col space-y-6 md:space-y-0 md:flex-row md:justify-between md:items-center">
                    <div class="space-y-4">
                        <h2 class="font-rajdhani text-2xl md:text-4xl font-bold">🚀 Capital Growth Challenge</h2>
                        <p class="text-gray-400 text-sm md:text-base max-w-lg">
                            Track your progress towards your trading goals with real-time analytics and performance metrics.
                        </p>
                    </div>
                    
                    <div class="bg-dark-800/50 rounded-lg p-4 border border-gray-700 w-full md:w-auto">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <p class="text-xs text-gray-400">Days Remaining</p>
                                <p class="font-rajdhani text-xl font-bold text-accent-yellow">42</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-400">Projected Date</p>
                                <p class="font-rajdhani text-xl font-bold">Dec 15</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress visualization - stacked on mobile, side by side on desktop -->
                <div class="mt-8 md:mt-12 flex flex-col md:flex-row items-center justify-between gap-8">
                    <!-- Progress ring - centered on mobile -->
                    <div class="w-full md:w-1/3 flex flex-col items-center">
                        <div class="relative w-40 h-40 md:w-48 md:h-48">
                            <svg class="w-full h-full" viewBox="0 0 100 100">
                                <circle class="text-dark-700" stroke-width="10" stroke="currentColor" fill="transparent" r="40" cx="50" cy="50" />
                                <circle class="progress-ring__circle text-accent-blue" stroke-width="10" stroke-linecap="round" stroke="currentColor" fill="transparent" r="40" cx="50" cy="50"
                                        stroke-dasharray="251.2" stroke-dashoffset="125.6" />
                                <text x="50" y="50" text-anchor="middle" dy=".3em" class="font-rajdhani text-2xl md:text-3xl font-bold fill-current text-white">42%</text>
                            </svg>
                        </div>
                        <p class="mt-4 text-gray-400 text-sm">Progress to target</p>
                    </div>

                    <!-- Progress bars - full width on mobile -->
                    <div class="w-full md:w-2/3 space-y-6">
                        <!-- Capital Progress -->
                        <div>
                            <div class="grid grid-cols-3 gap-2 mb-2 text-sm md:text-base">
                                <div>
                                    <span class="text-gray-400">Starting</span>
                                    <span class="block md:inline md:ml-2 text-accent-blue font-medium">₹1,00,000</span>
                                </div>
                                <div class="text-center">
                                    <span class="text-gray-400">Current</span>
                                    <span class="block md:inline md:ml-2 text-white font-medium">₹1,42,350</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-gray-400">Target</span>
                                    <span class="block md:inline md:ml-2 text-accent-green font-medium">₹2,50,000</span>
                                </div>
                            </div>
                            <div class="relative h-2 md:h-3 bg-dark-700 rounded-full overflow-hidden">
                                <div class="absolute inset-0 flex">
                                    <div class="bg-accent-blue" style="width: 100%"></div>
                                    <div class="bg-accent-green" style="width: 42%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Daily Target -->
                        <div>
                            <div class="flex justify-between mb-1 text-sm md:text-base">
                                <span class="text-gray-400">Daily Target</span>
                                <span class="text-white">₹2,857/day</span>
                            </div>
                            <div class="w-full bg-dark-700 rounded-full h-2">
                                <div class="bg-accent-purple h-2 rounded-full" style="width: 78%"></div>
                            </div>
                            <div class="flex justify-between mt-1 text-xs text-gray-400">
                                <span>Ahead of schedule</span>
                                <span>+₹1,235 today</span>
                            </div>
                        </div>

                        <!-- Win Rate -->
                        <div>
                            <div class="flex justify-between mb-1 text-sm md:text-base">
                                <span class="text-gray-400">Win Rate</span>
                                <span class="text-white">68%</span>
                            </div>
                            <div class="w-full bg-dark-700 rounded-full h-2">
                                <div class="bg-accent-green h-2 rounded-full" style="width: 68%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- KPI Cards -->
    <section class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Progress to Target -->
            <div class="glass rounded-xl p-5 hover:shadow-accent-blue transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-400 text-sm">Progress to Target</p>
                        <h3 class="font-rajdhani text-2xl font-bold mt-1">42%</h3>
                    </div>
                    <div class="p-2 rounded-lg bg-dark-800 group-hover:bg-accent-blue/10 transition-colors">
                        <i class="fas fa-b bullseye text-accent-blue text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-dark-700 rounded-full h-2">
                        <div class="bg-accent-blue h-2 rounded-full" style="width: 42%"></div>
                    </div>
                </div>
            </div>

            <!-- Average Risk:Reward -->
            <div class="glass rounded-xl p-5 hover:shadow-accent-blue transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-400 text-sm">Avg Risk:Reward</p>
                        <h3 class="font-rajdhani text-2xl font-bold mt-1">1:2.8</h3>
                    </div>
                    <div class="p-2 rounded-lg bg-dark-800 group-hover:bg-accent-blue/10 transition-colors">
                        <i class="fas fa-balance-scale-right text-accent-blue text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-400">Better than 85% of your trades</p>
                </div>
            </div>

            <!-- Highest Profit Day -->
            <div class="glass rounded-xl p-5 hover:shadow-accent-green transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-400 text-sm">Highest Profit Day</p>
                        <h3 class="font-rajdhani text-2xl font-bold mt-1">₹12,450</h3>
                    </div>
                    <div class="p-2 rounded-lg bg-dark-800 group-hover:bg-accent-green/10 transition-colors">
                        <i class="fas fa-arrow-up text-accent-green text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-400">Oct 28, 2023</p>
                </div>
            </div>

            <!-- Max Drawdown -->
            <div class="glass rounded-xl p-5 hover:shadow-accent-blue transition-all duration-300 group">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-gray-400 text-sm">Max Drawdown</p>
                        <h3 class="font-rajdhani text-2xl font-bold mt-1">-8.2%</h3>
                    </div>
                    <div class="p-2 rounded-lg bg-dark-800 group-hover:bg-accent-blue/10 transition-colors">
                        <i class="fas fa-arrow-down text-accent-red text-lg"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-xs text-gray-400">Recovered in 3 days</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Equity Curve -->
    <section class="container mx-auto px-4 py-6">
        <div class="glass rounded-xl p-5">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-rajdhani text-xl font-bold">Equity Curve</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-xs bg-dark-700 rounded-lg hover:bg-accent-blue/10 hover:text-accent-blue">1M</button>
                    <button class="px-3 py-1 text-xs bg-dark-700 rounded-lg hover:bg-accent-blue/10 hover:text-accent-blue">3M</button>
                    <button class="px-3 py-1 text-xs bg-accent-blue/20 text-accent-blue rounded-lg">6M</button>
                    <button class="px-3 py-1 text-xs bg-dark-700 rounded-lg hover:bg-accent-blue/10 hover:text-accent-blue">1Y</button>
                </div>
            </div>
            <div class="h-64 md:h-80">
                <canvas id="equityChart"></canvas>
            </div>
        </div>
    </section>

    <!-- Confidence Meter -->
    <section class="container mx-auto px-4 py-6">
        <div class="glass rounded-xl p-5">
            <h3 class="font-rajdhani text-xl font-bold mb-4">Trading Confidence Index</h3>
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-400">Low Confidence</span>
                <span class="text-sm text-gray-400">High Confidence</span>
            </div>
            <div class="relative mb-4">
                <div class="confidence-meter w-full h-2 rounded-full">
                    <div class="confidence-dot" style="left: 75%;"></div>
                </div>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-400">Your current confidence level: <span class="text-accent-yellow font-medium">High</span></p>
                <p class="text-xs text-gray-500 mt-1">Based on your recent trading performance and consistency</p>
            </div>
        </div>
    </section>

    <!-- Trades Table -->
    <section class="container mx-auto px-4 py-6">
        <div class="glass rounded-xl overflow-hidden">
            <div class="p-5 border-b border-gray-800 flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0">
                <h3 class="font-rajdhani text-xl font-bold">Trade History</h3>
                <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
                    <div class="relative">
                        <input type="text" placeholder="Search trades..." class="bg-dark-800 border border-gray-700 rounded-lg px-4 py-2 text-sm w-full md:w-64 focus:border-accent-blue focus:ring-1 focus:ring-accent-blue">
                        <i class="fas fa-search absolute right-3 top-2.5 text-gray-500"></i>
                    </div>
                    <select class="bg-dark-800 border border-gray-700 rounded-lg px-4 py-2 text-sm focus:border-accent-blue focus:ring-1 focus:ring-accent-blue">
                        <option>All Symbols</option>
                        <option>RELIANCE</option>
                        <option>TATASTEEL</option>
                        <option>HDFCBANK</option>
                    </select>
                    <button class="px-4 py-2 bg-accent-blue/10 text-accent-blue rounded-lg text-sm hover:bg-accent-blue/20 flex items-center space-x-1">
                        <i class="fas fa-filter"></i>
                        <span>Filter</span>
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-800">
                    <thead class="bg-dark-800">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Symbol</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Entry</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Exit</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Qty</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">P&L</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">R:R</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-rajdhani font-medium text-gray-400 uppercase tracking-wider">Notes</th>
                        </tr>
                    </thead>
                    <tbody class="bg-dark-900 divide-y divide-gray-800">
                        <tr class="hover:bg-dark-800/50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">Oct 30, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">RELIANCE</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">BUY</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">2,245.50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">2,278.75</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-400">+₹1,662.50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1:2.5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">Breakout trade</td>
                        </tr>
                        <tr class="hover:bg-dark-800/50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">Oct 28, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">TATASTEEL</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">SELL</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">128.40</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">125.20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">200</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-400">+₹640.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1:1.8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">Support break</td>
                        </tr>
                        <tr class="hover:bg-dark-800/50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">Oct 27, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">HDFCBANK</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">BUY</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1,568.90</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1,550.25</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">75</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-400">-₹1,398.75</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1:0.5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">Early entry</td>
                        </tr>
                        <tr class="hover:bg-dark-800/50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">Oct 25, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">INFY</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">BUY</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1,450.75</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1,478.50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">100</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-400">+₹2,775.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1:3.2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">Earnings play</td>
                        </tr>
                        <tr class="hover:bg-dark-800/50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">Oct 24, 2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">BHARTIARTL</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">SELL</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">925.50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">938.75</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-400">-₹662.50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">1:0.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">Stop hit</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-5 py-3 bg-dark-800 border-t border-gray-800 flex items-center justify-between">
                <div class="text-sm text-gray-400">
                    Showing <span class="font-medium text-gray-200">1</span> to <span class="font-medium text-gray-200">5</span> of <span class="font-medium text-gray-200">24</span> trades
                </div>
                <div class="flex space-x-1">
                    <button class="px-3 py-1 rounded-md bg-dark-700 text-sm text-gray-400 hover:bg-accent-blue/10 hover:text-accent-blue">
                        Previous
                    </button>
                    <button class="px-3 py-1 rounded-md bg-accent-blue/20 text-accent-blue text-sm">
                        1
                    </button>
                    <button class="px-3 py-1 rounded-md bg-dark-700 text-sm text-gray-400 hover:bg-accent-blue/10 hover:text-accent-blue">
                        2
                    </button>
                    <button class="px-3 py-1 rounded-md bg-dark-700 text-sm text-gray-400 hover:bg-accent-blue/10 hover:text-accent-blue">
                        3
                    </button>
                    <button class="px-3 py-1 rounded-md bg-dark-700 text-sm text-gray-400 hover:bg-accent-blue/10 hover:text-accent-blue">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Insights Section -->
    <section class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Top 3 Trades -->
            <div class="glass rounded-xl p-5 lg:col-span-2">
                <h3 class="font-rajdhani text-xl font-bold mb-4">Top 3 Trades</h3>
                <div class="space-y-4">
                    <div class="flex items-center p-3 bg-dark-800 rounded-lg hover:bg-accent-blue/10 group transition-colors">
                        <div class="p-3 rounded-lg bg-accent-blue/10 text-accent-blue mr-4">
                            <i class="fas fa-trophy text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <h4 class="font-medium">RELIANCE</h4>
                                <span class="text-green-400 font-medium">+₹3,245.00</span>
                            </div>
                            <p class="text-sm text-gray-400 mt-1">Oct 15, 2023 • 1:3.5 R:R</p>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-dark-800 rounded-lg hover:bg-accent-blue/10 group transition-colors">
                        <div class="p-3 rounded-lg bg-accent-blue/10 text-accent-blue mr-4">
                            <i class="fas fa-medal text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <h4 class="font-medium">INFY</h4>
                                <span class="text-green-400 font-medium">+₹2,775.00</span>
                            </div>
                            <p class="text-sm text-gray-400 mt-1">Oct 25, 2023 • 1:3.2 R:R</p>
                        </div>
                    </div>
                    <div class="flex items-center p-3 bg-dark-800 rounded-lg hover:bg-accent-blue/10 group transition-colors">
                        <div class="p-3 rounded-lg bg-accent-blue/10 text-accent-blue mr-4">
                            <i class="fas fa-award text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <h4 class="font-medium">TATASTEEL</h4>
                                <span class="text-green-400 font-medium">+₹2,150.00</span>
                            </div>
                            <p class="text-sm text-gray-400 mt-1">Oct 18, 2023 • 1:2.8 R:R</p>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- Most Traded Symbols -->
                <div class="glass rounded-xl p-5">
                    <h3 class="font-rajdhani text-xl font-bold mb-4">Most Traded Symbols</h3>
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>RELIANCE</span>
                                <span class="text-accent-blue">8 trades</span>
                            </div>
                            <div class="w-full bg-dark-800 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 70%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>TATASTEEL</span>
                                <span class="text-accent-blue">6 trades</span>
                            </div>
                            <div class="w-full bg-dark-800 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 55%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>HDFCBANK</span>
                                <span class="text-accent-blue">4 trades</span>
                            </div>
                            <div class="w-full bg-dark-800 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 40%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>INFY</span>
                                <span class="text-accent-blue">3 trades</span>
                            </div>
                            <div class="w-full bg-dark-800 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>BHARTIARTL</span>
                                <span class="text-accent-blue">3 trades</span>
                            </div>
                            <div class="w-full bg-dark-800 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-800 border-t border-gray-800 py-6 mt-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center space-x-2 mb-4 md:mb-0">
                    <i class="fas fa-chart-line text-accent-blue text-xl"></i>
                    <h1 class="font-rajdhani text-xl font-bold">TradeTrack</h1>
                </div>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-accent-blue transition-colors">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-accent-blue transition-colors">
                        <i class="fab fa-discord"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-accent-blue transition-colors">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
            <div class="mt-6 text-center md:text-left">
                <p class="text-xs text-gray-500">© 2023 TradeTrack. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Modal functionality
        const modal = document.getElementById('challenge-modal');
        const openModalBtn = document.getElementById('set-challenge');
        const closeModalBtn = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-challenge');
        const timeframeSelect = document.getElementById('timeframe');
        const customDaysContainer = document.getElementById('custom-days-container');

        openModalBtn.addEventListener('click', () => {
            modal.classList.add('active');
        });

        closeModalBtn.addEventListener('click', () => {
            modal.classList.remove('active');
        });

        cancelBtn.addEventListener('click', () => {
            modal.classList.remove('active');
        });

        timeframeSelect.addEventListener('change', (e) => {
            if (e.target.value === 'custom') {
                customDaysContainer.classList.remove('hidden');
            } else {
                customDaysContainer.classList.add('hidden');
            }
        });

        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            const icon = themeToggle.querySelector('i');
            if (document.documentElement.classList.contains('dark')) {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            } else {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        });

        // Equity Chart
        const ctx = document.getElementById('equityChart').getContext('2d');
        const equityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
                datasets: [{
                    label: 'Equity Curve',
                    data: [100000, 105000, 110000, 115000, 108000, 120000, 125000, 130000, 135000, 142350],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#fff',
                    pointHoverRadius: 6,
                    pointHoverBorderWidth: 2,
                    pointRadius: 4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#1e293b',
                        titleColor: '#f1f5f9',
                        bodyColor: '#e2e8f0',
                        borderColor: '#334155',
                        borderWidth: 1,
                        padding: 12,
                        callbacks: {
                            label: function(context) {
                                return '₹' + context.parsed.y.toLocaleString('en-IN');
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: '#334155',
                            borderColor: '#334155'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    y: {
                        grid: {
                            color: '#334155',
                            borderColor: '#334155'
                        },
                        ticks: {
                            color: '#94a3b8',
                            callback: function(value) {
                                return '₹' + value.toLocaleString('en-IN');
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index',
                }
            }
        });

        // Form submission
        const challengeForm = document.getElementById('challenge-form');
        challengeForm.addEventListener('submit', (e) => {
            e.preventDefault();
            // Here you would typically send the data to your backend
            // For now, we'll just close the modal
            modal.classList.remove('active');
            // Show a success message or update the UI
            alert('Challenge set successfully!');
        });
    </script>
</body>
</html>