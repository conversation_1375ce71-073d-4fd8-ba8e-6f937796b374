# Community Panel Image Upload Setup

This guide helps you set up image uploads for the community panel to work on both local and server environments.

## 🚀 Quick Setup

### 1. Run the Setup Script (Recommended)

After deploying your application to the server:

1. Navigate to your website: `https://yourdomain.com/setup_uploads.php`
2. The script will automatically:
   - Create necessary upload directories
   - Set proper permissions
   - Show any issues that need manual fixing
3. **Delete the setup file** after running it for security

### 2. Manual Setup

If you prefer manual setup or the script doesn't work:

```bash
# Create upload directories
mkdir -p public/uploads/community
mkdir -p public/uploads/screenshots

# Set proper permissions
chmod 755 public/uploads
chmod 755 public/uploads/community
chmod 755 public/uploads/screenshots

# Create security files
echo '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>' > public/uploads/index.html
echo '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>' > public/uploads/community/index.html
```

## 📁 Directory Structure

After setup, your directory structure should look like:

```
public/
├── uploads/
│   ├── .htaccess          # Security rules
│   ├── index.html         # Prevents directory browsing
│   ├── community/         # Community post images
│   │   └── index.html
│   └── screenshots/       # Other uploads
│       └── index.html
```

## 🔧 Configuration

The upload system uses `app/Config/Upload.php` for configuration:

- **Max file size**: 5MB
- **Allowed types**: JPG, PNG, GIF, WebP
- **Upload paths**: Multiple fallback paths for compatibility

## 🛠️ Troubleshooting

### Common Issues:

1. **"No writable upload directory found"**
   - Check directory permissions: `chmod 755 public/uploads/community`
   - Ensure the web server can write to the directory

2. **"Failed to move uploaded file"**
   - Check PHP upload settings in `php.ini`:
     ```ini
     upload_max_filesize = 10M
     post_max_size = 10M
     max_execution_time = 300
     ```

3. **Images not displaying**
   - Verify the `public/uploads` directory is accessible via web
   - Check `.htaccess` rules aren't blocking image access

4. **Permission denied errors**
   - On shared hosting, contact your provider
   - On VPS/dedicated servers, check file ownership:
     ```bash
     chown -R www-data:www-data public/uploads
     ```

### Server-Specific Notes:

**Shared Hosting:**
- Some hosts restrict directory creation
- Contact support if you can't create directories
- May need to use cPanel File Manager

**VPS/Dedicated:**
- Ensure web server user has write permissions
- Check SELinux settings if applicable
- Verify firewall isn't blocking uploads

## 🔒 Security Features

The upload system includes several security measures:

1. **File type validation** - Only allows image files
2. **File size limits** - Prevents large file uploads
3. **Directory protection** - `.htaccess` prevents script execution
4. **Filename randomization** - Prevents file conflicts and guessing
5. **Multiple upload paths** - Fallback options for different server configurations

## 📊 Server Requirements

- **PHP**: 7.4 or higher
- **Extensions**: GD or ImageMagick (for image processing)
- **Permissions**: Write access to `public/uploads` directory
- **Disk Space**: Adequate space for user uploads

## 🔍 Testing

After setup, test the upload functionality:

1. Go to Community Panel
2. Create a new post
3. Try uploading an image
4. Verify the image displays correctly
5. Check server logs for any errors

## 📝 Maintenance

Regular maintenance tasks:

1. **Monitor disk usage** - User uploads can consume space
2. **Clean old files** - Implement cleanup for deleted posts
3. **Backup uploads** - Include uploads in your backup strategy
4. **Update permissions** - Check after server updates

## 🆘 Support

If you encounter issues:

1. Check the application logs in `writable/logs/`
2. Review web server error logs
3. Use the setup script to diagnose problems
4. Contact your hosting provider for permission issues

---

**Note**: Remember to delete `public/setup_uploads.php` after running it for security reasons.
