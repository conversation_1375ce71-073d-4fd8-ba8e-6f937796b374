function loadReferrals(page = 1) {
    $.ajax({
        url: base_url + '/fetchReferralList', // Controller method
        type: 'GET',
        data: { page: page },
        success: function (response) {
            $('#referralTableBody').html(response.table);
            $('#paginationLinks').html(response.pagination);
        },
        error: function () {
            $('#referralTableBody').html('<tr><td colspan="4" class="text-center py-6 text-red-500">Failed to load data.</td></tr>');
        }
    });
}

// On load
$(document).ready(function () {
    loadReferrals();

    // Handle pagination click
    $(document).on('click', '.pagination-link', function (e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page) loadReferrals(page);
    });
});