<?php

namespace App\Libraries;

use App\Models\AngelOneTradeModel;
use App\Models\TradeModel;

/**
 * Angel One Trade Processor
 * 
 * This class handles processing of Angel One trades and converting them
 * to the platform's standard trade format for analysis
 */
class AngelOneTradeProcessor
{
    private $angelTradeModel;
    private $tradeModel;
    private $db;

    public function __construct()
    {
        $this->angelTradeModel = new AngelOneTradeModel();
        $this->tradeModel = new TradeModel();
        $this->db = \Config\Database::connect();
    }

    /**
     * Process all unprocessed Angel One trades for a user
     */
    public function processUserTrades($userId)
    {
        $unprocessedTrades = $this->angelTradeModel->getUnprocessedTrades($userId);
        
        if (empty($unprocessedTrades)) {
            return [
                'success' => true,
                'message' => 'No unprocessed trades found',
                'processed_count' => 0
            ];
        }

        // Group trades by symbol for processing
        $groupedTrades = $this->groupTradesBySymbol($unprocessedTrades);
        
        $processedCount = 0;
        $processedTradeIds = [];

        foreach ($groupedTrades as $symbol => $trades) {
            $result = $this->processSymbolTrades($symbol, $trades, $userId);
            if ($result['success']) {
                $processedCount += $result['trades_created'];
                $processedTradeIds = array_merge($processedTradeIds, $result['processed_ids']);
            }
        }

        // Mark trades as processed
        if (!empty($processedTradeIds)) {
            $this->angelTradeModel->markAsProcessed($processedTradeIds);
        }

        return [
            'success' => true,
            'message' => "Processed {$processedCount} completed trades",
            'processed_count' => $processedCount
        ];
    }

    /**
     * Group trades by symbol
     */
    private function groupTradesBySymbol($trades)
    {
        $grouped = [];
        
        foreach ($trades as $trade) {
            $symbol = $trade['trading_symbol'];
            if (!isset($grouped[$symbol])) {
                $grouped[$symbol] = [];
            }
            $grouped[$symbol][] = $trade;
        }

        return $grouped;
    }

    /**
     * Process trades for a specific symbol
     */
    private function processSymbolTrades($symbol, $trades, $userId)
    {
        // Separate buy and sell trades
        $buyTrades = [];
        $sellTrades = [];

        foreach ($trades as $trade) {
            if ($trade['transaction_type'] === 'BUY') {
                $buyTrades[] = $trade;
            } else {
                $sellTrades[] = $trade;
            }
        }

        // Sort by time
        usort($buyTrades, function($a, $b) {
            return strtotime($a['trade_date'] . ' ' . $a['trade_time']) - strtotime($b['trade_date'] . ' ' . $b['trade_time']);
        });

        usort($sellTrades, function($a, $b) {
            return strtotime($a['trade_date'] . ' ' . $a['trade_time']) - strtotime($b['trade_date'] . ' ' . $b['trade_time']);
        });

        $completedTrades = $this->matchBuySellTrades($buyTrades, $sellTrades);
        $processedIds = [];
        $tradesCreated = 0;

        foreach ($completedTrades as $completedTrade) {
            if ($this->createCompletedTrade($completedTrade, $userId)) {
                $tradesCreated++;
                $processedIds = array_merge($processedIds, $completedTrade['buy_ids'], $completedTrade['sell_ids']);
            }
        }

        return [
            'success' => true,
            'trades_created' => $tradesCreated,
            'processed_ids' => $processedIds
        ];
    }

    /**
     * Match buy and sell trades to create completed trades
     */
    private function matchBuySellTrades($buyTrades, $sellTrades)
    {
        $completedTrades = [];
        $buyIndex = 0;
        $sellIndex = 0;

        while ($buyIndex < count($buyTrades) && $sellIndex < count($sellTrades)) {
            $buy = $buyTrades[$buyIndex];
            $sell = $sellTrades[$sellIndex];

            $buyQty = $buy['quantity'];
            $sellQty = $sell['quantity'];

            if ($buyQty == $sellQty) {
                // Perfect match
                $completedTrades[] = [
                    'buy_trades' => [$buy],
                    'sell_trades' => [$sell],
                    'buy_ids' => [$buy['id']],
                    'sell_ids' => [$sell['id']],
                    'quantity' => $buyQty
                ];
                $buyIndex++;
                $sellIndex++;
            } elseif ($buyQty > $sellQty) {
                // Partial sell
                $completedTrades[] = [
                    'buy_trades' => [$buy],
                    'sell_trades' => [$sell],
                    'buy_ids' => [$buy['id']],
                    'sell_ids' => [$sell['id']],
                    'quantity' => $sellQty
                ];
                
                // Update buy quantity for remaining
                $buyTrades[$buyIndex]['quantity'] -= $sellQty;
                $sellIndex++;
            } else {
                // Partial buy
                $completedTrades[] = [
                    'buy_trades' => [$buy],
                    'sell_trades' => [$sell],
                    'buy_ids' => [$buy['id']],
                    'sell_ids' => [$sell['id']],
                    'quantity' => $buyQty
                ];
                
                // Update sell quantity for remaining
                $sellTrades[$sellIndex]['quantity'] -= $buyQty;
                $buyIndex++;
            }
        }

        return $completedTrades;
    }

    /**
     * Create a completed trade entry
     */
    private function createCompletedTrade($completedTrade, $userId)
    {
        $buyTrades = $completedTrade['buy_trades'];
        $sellTrades = $completedTrade['sell_trades'];
        $quantity = $completedTrade['quantity'];

        // Calculate weighted average prices
        $totalBuyValue = 0;
        $totalSellValue = 0;

        foreach ($buyTrades as $buy) {
            $buyQty = min($buy['quantity'], $quantity);
            $totalBuyValue += $buyQty * $buy['price'];
        }

        foreach ($sellTrades as $sell) {
            $sellQty = min($sell['quantity'], $quantity);
            $totalSellValue += $sellQty * $sell['price'];
        }

        $avgBuyPrice = $totalBuyValue / $quantity;
        $avgSellPrice = $totalSellValue / $quantity;

        $entryAmount = $quantity * $avgBuyPrice;
        $exitAmount = $quantity * $avgSellPrice;
        $pnlAmount = $exitAmount - $entryAmount;
        $pnlPercent = $entryAmount > 0 ? ($pnlAmount / $entryAmount) * 100 : 0;

        // Get the earliest trade time
        $allTrades = array_merge($buyTrades, $sellTrades);
        $tradeTimes = array_map(function($t) {
            return strtotime($t['trade_date'] . ' ' . $t['trade_time']);
        }, $allTrades);

        $earliestTime = min($tradeTimes);

        // Prepare trade data
        $tradeData = [
            'user_id' => $userId,
            'symbol' => $buyTrades[0]['trading_symbol'],
            'market_type' => $this->getMarketTypeFromSegment($buyTrades[0]['exchange_segment']),
            'datetime' => date('Y-m-d H:i:s', $earliestTime),
            'entry_price' => round($avgBuyPrice, 4),
            'entry_quantity' => $quantity,
            'entry_amount' => round($entryAmount, 2),
            'exit_price' => round($avgSellPrice, 4),
            'pnl_amount' => round($pnlAmount, 2),
            'pnl_percent' => round($pnlPercent, 2),
            'trade_type' => $this->determineTradeType($buyTrades[0]),
            'broker' => 'angel_one',
            'broker_order_id' => $buyTrades[0]['order_id'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->tradeModel->insert($tradeData);
    }

    /**
     * Convert Angel One exchange segment to market type
     */
    private function getMarketTypeFromSegment($segment)
    {
        $segmentMap = [
            'NSE' => 'equity',
            'BSE' => 'equity',
            'NFO' => 'futures',
            'BFO' => 'futures',
            'CDS' => 'currency',
            'MCX' => 'commodity'
        ];

        return $segmentMap[$segment] ?? 'equity';
    }

    /**
     * Determine trade type based on product type
     */
    private function determineTradeType($trade)
    {
        $productType = $trade['product_type'];
        
        // Map Angel One product types to our trade types
        $typeMap = [
            'INTRADAY' => 1,
            'DELIVERY' => 2,
            'CARRYFORWARD' => 2,
            'MARGIN' => 1
        ];

        return $typeMap[$productType] ?? 1; // Default to intraday
    }

    /**
     * Get processing statistics
     */
    public function getProcessingStats($userId)
    {
        $totalTrades = $this->angelTradeModel->where('user_id', $userId)->countAllResults();
        $processedTrades = $this->angelTradeModel->where('user_id', $userId)->where('processed', 1)->countAllResults();
        $unprocessedTrades = $totalTrades - $processedTrades;

        return [
            'total_trades' => $totalTrades,
            'processed_trades' => $processedTrades,
            'unprocessed_trades' => $unprocessedTrades,
            'processing_percentage' => $totalTrades > 0 ? round(($processedTrades / $totalTrades) * 100, 2) : 0
        ];
    }
}
