"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Download,
  Share2,
  TrendingUp,
  Trophy,
  Target,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Zap,
  Brain,
  BarChart3,
  RefreshCw,
  TrendingDown,
  Activity,
  AlertCircle,
} from "lucide-react"

interface ResultsSectionProps {
  timeframe: 7 | 15 | 30
  onGenerateNew: () => void
}

export function ResultsSection({ timeframe, onGenerateNew }: ResultsSectionProps) {
  const timeframeText = timeframe === 7 ? "7-Day" : timeframe === 15 ? "15-Day" : "30-Day"

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle className="text-3xl text-white">
                Your <span className="text-blue-400">{timeframeText}</span> AI Trading Analysis
              </CardTitle>
              <CardDescription className="text-slate-400 text-lg">
                Comprehensive performance breakdown with actionable insights
              </CardDescription>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                size="sm"
                className="border-slate-600 bg-slate-700 hover:bg-slate-600 text-slate-300"
              >
                <Download className="w-4 h-4 mr-2" />
                Export PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-slate-600 bg-slate-700 hover:bg-slate-600 text-slate-300"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button
                onClick={onGenerateNew}
                variant="outline"
                size="sm"
                className="border-slate-600 bg-slate-700 hover:bg-slate-600 text-slate-300"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                New Analysis
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 1. PERFORMANCE Section */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-white text-2xl">
            <div className="w-10 h-10 rounded-xl bg-blue-600/20 flex items-center justify-center mr-3">
              <BarChart3 className="w-5 h-5 text-blue-400" />
            </div>
            1. PERFORMANCE
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="bg-slate-700 border-slate-600">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">₹52,904</div>
                  <div className="text-sm text-slate-400">Total Profit (30 days)</div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-slate-700 border-slate-600">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400 mb-1">63.2%</div>
                  <div className="text-sm text-slate-400">Win Rate</div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-slate-700 border-slate-600">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400 mb-1">0.62</div>
                  <div className="text-sm text-slate-400">Risk-Reward Ratio</div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-slate-700 border-slate-600">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400 mb-1">₹2,642</div>
                  <div className="text-sm text-slate-400">Expectancy/Trade</div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600">
            <p className="text-slate-300 leading-relaxed">
              Overall profitable with a solid win rate, but undermined by poor risk-reward management. Expectancy is
              positive, yet low confidence (4.5/10) and emotional volatility indicate inconsistency.
            </p>
            <div className="flex items-center mt-3">
              <span className="text-sm text-slate-400 mr-3">Confidence Level:</span>
              <Progress value={45} className="flex-1 h-2" />
              <span className="text-sm text-slate-400 ml-3">4.5/10</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 2. STRENGTHS Section */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-white text-2xl">
            <div className="w-10 h-10 rounded-xl bg-green-600/20 flex items-center justify-center mr-3">
              <Trophy className="w-5 h-5 text-green-400" />
            </div>
            2. STRENGTHS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4 p-4 bg-green-600/10 rounded-xl border border-green-500/30">
              <div className="w-8 h-8 rounded-full bg-green-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">High Win Rate & Expectancy</h4>
                <p className="text-slate-300">63% win rate and ₹2,642/trade expectancy validate a working edge.</p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-green-600/10 rounded-xl border border-green-500/30">
              <div className="w-8 h-8 rounded-full bg-green-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <TrendingUp className="w-4 h-4 text-green-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Reversal Strategy Dominance</h4>
                <p className="text-slate-300">
                  Reversal trades generated ₹33,325 (63% of total P&L) with only 5 trades, showing high efficiency.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-green-600/10 rounded-xl border border-green-500/30">
              <div className="w-8 h-8 rounded-full bg-green-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <Brain className="w-4 h-4 text-green-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Emotional Discipline Pays</h4>
                <p className="text-slate-300">
                  Calm-state trades (11/19) delivered robust avg gains (₹4,921), proving discipline drives results.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 3. WEAKNESSES Section */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-white text-2xl">
            <div className="w-10 h-10 rounded-xl bg-red-600/20 flex items-center justify-center mr-3">
              <AlertTriangle className="w-5 h-5 text-red-400" />
            </div>
            3. WEAKNESSES
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4 p-4 bg-red-600/10 rounded-xl border border-red-500/30">
              <div className="w-8 h-8 rounded-full bg-red-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <XCircle className="w-4 h-4 text-red-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Poor Risk-Reward Ratio (0.62)</h4>
                <p className="text-slate-300">
                  Avg win (₹5,797) is only 2.1x avg loss (₹2,776), making profits fragile long-term.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-red-600/10 rounded-xl border border-red-500/30">
              <div className="w-8 h-8 rounded-full bg-red-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <Activity className="w-4 h-4 text-red-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Emotional Leakage</h4>
                <p className="text-slate-300">
                  Frustration (5 trades) slashed avg gains to ₹755 vs. ₹4,921 in calm states.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-red-600/10 rounded-xl border border-red-500/30">
              <div className="w-8 h-8 rounded-full bg-red-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <TrendingDown className="w-4 h-4 text-red-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Strategy Imbalance</h4>
                <p className="text-slate-300">
                  Pullback strategy underperformed (only ₹1,000 from 2 trades), suggesting misapplication.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 4. ACTIONS Section */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-white text-2xl">
            <div className="w-10 h-10 rounded-xl bg-blue-600/20 flex items-center justify-center mr-3">
              <Target className="w-5 h-5 text-blue-400" />
            </div>
            4. ACTIONS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4 p-4 bg-blue-600/10 rounded-xl border border-blue-500/30">
              <div className="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <Zap className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Fix R:R Immediately</h4>
                <p className="text-slate-300">
                  Set minimum 1:3 ratio (e.g., ₹3,000 risk → ₹9,000 target). Adjust stop-loss placement or profit
                  targets.
                </p>
                <Badge variant="outline" className="mt-2 border-red-500/50 text-red-400 bg-red-500/10">
                  High Priority
                </Badge>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-blue-600/10 rounded-xl border border-blue-500/30">
              <div className="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <Brain className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Emotion-Trigger Protocol</h4>
                <p className="text-slate-300">
                  Pause trading if frustrated; resume only when calm. Journal emotional triggers post-loss.
                </p>
                <Badge variant="outline" className="mt-2 border-red-500/50 text-red-400 bg-red-500/10">
                  Critical
                </Badge>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-blue-600/10 rounded-xl border border-blue-500/30">
              <div className="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <BarChart3 className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Optimize Pullbacks</h4>
                <p className="text-slate-300">
                  Review pullback setups (chart patterns, entry timing) or pause this strategy until refined.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-blue-600/10 rounded-xl border border-blue-500/30">
              <div className="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <Trophy className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold text-white mb-1">Scale Quality Trades</h4>
                <p className="text-slate-300">
                  Focus on reversal/breakout setups (14/19 trades, 94% of P&L). Limit to 1-2 high-conviction trades/day.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 5. PSYCHOLOGY Section */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-white text-2xl">
            <div className="w-10 h-10 rounded-xl bg-purple-600/20 flex items-center justify-center mr-3">
              <Brain className="w-5 h-5 text-purple-400" />
            </div>
            5. PSYCHOLOGY
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-purple-600/10 rounded-xl p-6 border border-purple-500/30">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 rounded-full bg-purple-600/20 flex items-center justify-center flex-shrink-0 mt-1">
                <AlertCircle className="w-4 h-4 text-purple-400" />
              </div>
              <div>
                <p className="text-slate-300 leading-relaxed mb-4">
                  Frustration directly correlates with underperformance (₹755 vs. ₹4,921 gains), revealing a "loss-tilt
                  cycle". "Unknown" emotional states broke even, highlighting self-awareness gaps.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
                    <h5 className="font-semibold text-white mb-2">Emotional Impact</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Calm State</span>
                        <span className="text-green-400 font-semibold">₹4,921 avg</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Frustrated</span>
                        <span className="text-red-400 font-semibold">₹755 avg</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Unknown</span>
                        <span className="text-yellow-400 font-semibold">Break-even</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
                    <h5 className="font-semibold text-white mb-2">Self-Awareness Score</h5>
                    <div className="flex items-center space-x-3">
                      <Progress value={30} className="flex-1 h-3" />
                      <span className="text-slate-400 text-sm">3/10</span>
                    </div>
                    <p className="text-xs text-slate-500 mt-2">Need better emotional tracking</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Summary Table */}
      <Card className="bg-slate-800 border-slate-700 shadow-xl">
        <CardHeader>
          <CardTitle className="text-white text-xl">Key Metric Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-slate-600">
                  <th className="text-left py-3 px-4 text-slate-400 font-medium">Factor</th>
                  <th className="text-left py-3 px-4 text-slate-400 font-medium">Current State</th>
                  <th className="text-left py-3 px-4 text-slate-400 font-medium">Target</th>
                  <th className="text-left py-3 px-4 text-slate-400 font-medium">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-600">
                <tr>
                  <td className="py-3 px-4 text-white font-medium">R:R Ratio</td>
                  <td className="py-3 px-4 text-red-400">0.62</td>
                  <td className="py-3 px-4 text-green-400">≥1.5</td>
                  <td className="py-3 px-4">
                    <Badge variant="destructive" className="bg-red-600/20 text-red-400 border-red-500/50">
                      Needs Fix
                    </Badge>
                  </td>
                </tr>
                <tr>
                  <td className="py-3 px-4 text-white font-medium">Avg Win/Loss Ratio</td>
                  <td className="py-3 px-4 text-yellow-400">2.1x</td>
                  <td className="py-3 px-4 text-green-400">≥3x</td>
                  <td className="py-3 px-4">
                    <Badge variant="outline" className="border-yellow-500/50 text-yellow-400 bg-yellow-500/10">
                      Improving
                    </Badge>
                  </td>
                </tr>
                <tr>
                  <td className="py-3 px-4 text-white font-medium">Frustration Impact</td>
                  <td className="py-3 px-4 text-red-400">High</td>
                  <td className="py-3 px-4 text-green-400">Minimal</td>
                  <td className="py-3 px-4">
                    <Badge variant="destructive" className="bg-red-600/20 text-red-400 border-red-500/50">
                      Critical
                    </Badge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
