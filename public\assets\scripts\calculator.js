document.addEventListener('DOMContentLoaded', function () {

    // Leverage controls
    const leverageSlider = document.getElementById('leverage-slider');
    const leverageValue = document.getElementById('leverage-value');
    const decreaseLeverageBtn = document.getElementById('decrease-leverage');
    const increaseLeverageBtn = document.getElementById('increase-leverage');

    leverageSlider.addEventListener('input', function () {
        leverageValue.textContent = `${this.value}x`;
        calculate();
    });

    decreaseLeverageBtn.addEventListener('click', function () {
        if (parseInt(leverageSlider.value) > 1) {
            leverageSlider.value = parseInt(leverageSlider.value) - 1;
            leverageValue.textContent = `${leverageSlider.value}x`;
            calculate();
        }
    });

    increaseLeverageBtn.addEventListener('click', function () {
        if (parseInt(leverageSlider.value) < 10) {
            leverageSlider.value = parseInt(leverageSlider.value) + 1;
            leverageValue.textContent = `${leverageSlider.value}x`;
            calculate();
        }
    });

    // Input fields that trigger calculation
    const inputFields = [
        'accountSize', 'riskPercentage', 'entryPrice', 'stopLoss', 'takeProfit'
    ];

    inputFields.forEach(id => {
        const input = document.getElementById(id);
        // Add debounce to input event
        let timeout = null;
        input.addEventListener('input', function () {
            clearTimeout(timeout);
            timeout = setTimeout(calculate, 300);
        });
        // Add focus effect
        input.addEventListener('focus', function () {
            this.parentElement.classList.add('ring-2', 'ring-blue-500/50');
        });
        input.addEventListener('blur', function () {
            this.parentElement.classList.remove('ring-2', 'ring-blue-500/50');
        });
    });

    // Position type radio buttons
    document.querySelectorAll('input[name="positionType"]').forEach(radio => {
        radio.addEventListener('change', calculate);
    });

    // Reset button
    document.getElementById('resetBtn').addEventListener('click', function () {
        // Show confirmation dialog
        // if (confirm('Are you sure you want to reset all fields to default values?')) {
            document.getElementById('accountSize').value = 10000;
            document.getElementById('riskPercentage').value = 2;
            document.getElementById('entryPrice').value = 50;
            document.getElementById('stopLoss').value = 48;
            document.getElementById('takeProfit').value = 55;
            document.getElementById('leverage-slider').value = 1;
            leverageValue.textContent = '1x';
            // document.getElementById('commission').value = 0;
            // document.getElementById('slippage').value = 0.1;
            document.getElementById('long').checked = true;

            // Show success feedback
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check-circle mr-2"></i> Reset complete!';
            this.classList.add('text-emerald-400');

            setTimeout(() => {
                this.innerHTML = originalText;
                this.classList.remove('text-emerald-400');
            }, 1500);

            calculate();
        // }
    });

    // Main calculation function
    function calculate() {
        const isLong = document.getElementById('long').checked;
        const accountSize = parseFloat(document.getElementById('accountSize').value) || 0;
        const riskPercentage = parseFloat(document.getElementById('riskPercentage').value) || 0;
        const entryPrice = parseFloat(document.getElementById('entryPrice').value) || 0;
        const stopLoss = parseFloat(document.getElementById('stopLoss').value) || 0;
        const takeProfit = parseFloat(document.getElementById('takeProfit').value) || 0;
        const leverage = parseInt(document.getElementById('leverage-slider').value) || 1;

        // Risk amount (in dollars)
        const riskAmount = (accountSize * (riskPercentage / 100)) * leverage;

        // Price difference calculation depends on position type
        let stopDistance, profitDistance;

        if (isLong) {
            stopDistance = entryPrice - stopLoss;
            profitDistance = takeProfit - entryPrice;
        } else {
            stopDistance = stopLoss - entryPrice;
            profitDistance = entryPrice - takeProfit;
        }

        // Position size
        const positionSize = riskAmount / stopDistance;

        // Risk/Reward ratio
        const riskRewardRatio = (stopDistance > 0) ? profitDistance / stopDistance : 0;

        // Reward amount
        const rewardAmount = profitDistance * positionSize;

        const potentialLoss = stopDistance * positionSize;
        const potentialGain = profitDistance * positionSize;
        const breakEvenPrice = entryPrice;

        // Risk level calculation (for gauge)
        let riskGaugePercentage, riskLevel, riskColor;

        if (riskRewardRatio >= 3) {
            riskGaugePercentage = 20;
            riskLevel = 'Low';
            riskColor = 'text-emerald-400';
        } else if (riskRewardRatio >= 2) {
            riskGaugePercentage = 40;
            riskLevel = 'Moderate';
            riskColor = 'text-blue-400';
        } else if (riskRewardRatio >= 1) {
            riskGaugePercentage = 60;
            riskLevel = 'Medium';
            riskColor = 'text-yellow-400';
        } else if (riskRewardRatio > 0) {
            riskGaugePercentage = 80;
            riskLevel = 'High';
            riskColor = 'text-orange-400';
        } else {
            riskGaugePercentage = 95;
            riskLevel = 'Extreme';
            riskColor = 'text-red-400';
        }

        // RR rating for badge
        let rrRating, rrColor, rrIcon;
        if (riskRewardRatio >= 3) {
            rrRating = 'Excellent';
            rrColor = 'text-emerald-400';
            rrIcon = 'fa-star';
        } else if (riskRewardRatio >= 2) {
            rrRating = 'Good';
            rrColor = 'text-blue-400';
            rrIcon = 'fa-star';
        } else if (riskRewardRatio >= 1) {
            rrRating = 'Fair';
            rrColor = 'text-yellow-400';
            rrIcon = 'fa-star-half-alt';
        } else {
            rrRating = 'Poor';
            rrColor = 'text-red-400';
            rrIcon = 'fa-exclamation-triangle';
        }

        // Update UI with calculated values
        document.getElementById('riskAmount').textContent = `₹${riskAmount.toFixed(2)}`;
        document.getElementById('rewardAmount').textContent = `₹${rewardAmount.toFixed(2)}`;
        document.getElementById('riskRewardRatio').innerHTML = riskRewardRatio > 0 ?
            `1:${riskRewardRatio.toFixed(2)}` :
            'N/A';
        document.querySelector('#riskRewardRatio + div').innerHTML =
            `<i class="fas ${rrIcon} mr-1"></i><span>${rrRating}</span>`;
        document.querySelector('#riskRewardRatio + div span').className = rrColor;

        document.getElementById('positionSize').textContent = `${positionSize.toFixed(2)} units`;
        // document.getElementById('potentialLoss').textContent = `$${potentialLoss.toFixed(2)}`;
        // document.getElementById('potentialGain').textContent = `$${potentialGain.toFixed(2)}`;
        // document.getElementById('breakEvenPrice').textContent = `$${breakEvenPrice.toFixed(2)}`;
        document.getElementById('riskGauge').style.width = `${riskGaugePercentage}%`;
        document.getElementById('riskLevel').textContent = riskLevel;
        document.getElementById('riskLevel').className = `text-xs font-medium ${riskColor}`;

        // Add animation to result cards
        document.querySelectorAll('.result-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 50}ms`;
            card.classList.add('animate-fade-in');
        });
    }

    // Initial calculation on page load
    calculate();

    // Auto-focus the first input field
    document.getElementById('accountSize').focus();
});