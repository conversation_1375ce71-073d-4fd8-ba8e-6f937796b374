/* Community Page Styles */
:root {
    --primary: #4fd1c5;
    --primary-dark: #319795;
    --secondary: #805ad5;
    --secondary-dark: #6b46c1;
    --accent: #f687b3;
    --accent-dark: #e53e3e;
    --dark: #1a202c;
    --darker: #171923;
    --light: #f7fafc;
    --gray: #e2e8f0;
    --dark-gray: #2d3748;
}

body {
    font-family: 'Space Grotesk', sans-serif;
    background-color: var(--darker);
    color: var(--gray);
    transition: all 0.3s ease;
}

.glass-card {
    background: rgba(26, 32, 44, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.sidebar {
    transition: all 0.3s ease;
    background-color: var(--dark);
}

.sidebar-collapsed {
    width: 80px !important;
}

.sidebar-collapsed .nav-text {
    display: none;
}

.sidebar-collapsed .logo-text {
    display: none;
}

.sidebar-collapsed .logo-icon {
    margin: 0 auto;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
}

.glow-button {
    box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(79, 209, 197, 0.8);
    }
    100% {
        box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
    }
}

.hashtag {
    color: var(--primary);
}

.mention {
    color: var(--accent);
}

.stock-tag {
    color: var(--secondary);
}

.code-block {
    background-color: #2d3748;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
}

.reaction-btn:hover {
    transform: scale(1.1);
}

.badge-pro {
    background: linear-gradient(90deg, var(--primary), var(--secondary));
}

.badge-gainer {
    background: linear-gradient(90deg, #48bb78, #38b2ac);
}

.badge-master {
    background: linear-gradient(90deg, var(--accent), #ed8936);
}

.tab-active {
    border-bottom: 2px solid var(--primary);
    color: var(--primary);
}

.emoji-picker {
    position: absolute;
    bottom: 50px;
    right: 0;
    z-index: 10;
}

.comment-section {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.comment-section.active {
    max-height: 1000px;
    transition: max-height 0.5s ease-in;
}

.comment-input {
    background: rgba(45, 55, 72, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-input:focus {
    outline: none;
    border-color: var(--primary);
}

.neon-text {
    text-shadow: 0 0 5px rgba(79, 209, 197, 0.5);
}

.modal {
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: translateY(0);
}

.post-type-btn {
    transition: all 0.2s ease;
}

.post-type-btn.active {
    background-color: var(--primary);
    color: white;
}

.post-type-btn:hover:not(.active) {
    background-color: rgba(79, 209, 197, 0.1);
}

.follow-btn {
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    transition: all 0.3s ease;
}

.follow-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
}

.follow-btn.following {
    background: var(--dark-gray);
    border: 1px solid var(--primary);
}

.reply-section {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    margin-left: 2rem;
    border-left: 2px solid var(--primary);
    padding-left: 1rem;
}

.reply-section.active {
    max-height: 200px;
}

.reply-btn {
    color: var(--primary);
    font-size: 0.8rem;
}

.reply-btn:hover {
    text-decoration: underline;
}

.reply-indicator {
    color: var(--primary);
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -300px;
        z-index: 100;
        width: 300px;
        height: 100vh;
    }

    .sidebar.active {
        left: 0;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        z-index: 90;
        display: none;
    }

    .overlay.active {
        display: block;
    }

    .feed-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .feed-header h1 {
        margin-bottom: 1rem;
    }

    .feed-filters {
        width: 100%;
        flex-direction: column;
        gap: 0.5rem;
    }

    .feed-filters select {
        width: 100%;
    }

    .tabs {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0.5rem;
        -webkit-overflow-scrolling: touch;
    }

    .tabs::-webkit-scrollbar {
        display: none;
    }

    .post-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .post-actions > div {
        flex: 1 1 100%;
        justify-content: space-between;
    }

    .post-actions .emoji-picker-container {
        width: 100%;
        text-align: right;
    }

    .user-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-badges {
        margin-top: 0.5rem;
        width: 100%;
        justify-content: space-between;
    }

    .modal-content {
        width: 95%;
        margin: 0 auto;
    }

    .post-type-buttons {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0.5rem;
    }

    .post-type-buttons::-webkit-scrollbar {
        display: none;
    }

    .post-type-btn {
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .comment-input-container {
        flex-direction: column;
    }

    .comment-input-container input {
        border-radius: 0.5rem 0.5rem 0 0 !important;
        width: 100%;
    }

    .comment-input-container button {
        border-radius: 0 0 0.5rem 0.5rem !important;
        width: 100%;
    }

    .reply-section {
        margin-left: 1rem;
    }
}
